-- Menu Items tablosuna aktif saatler ve QR menü görünürlüğü sütunları ekleme

-- 1. is_visible_in_qr_menu sütunu ekle (eğer yoksa)
ALTER TABLE menu_items 
ADD COLUMN IF NOT EXISTS is_visible_in_qr_menu TINYINT(1) NOT NULL DEFAULT 1 COMMENT 'QR menüde görünür mü?';

-- 2. active_start_time sütunu ekle (eğer yoksa)
ALTER TABLE menu_items 
ADD COLUMN IF NOT EXISTS active_start_time TIME NULL COMMENT 'Ürünün aktif olduğu başlangı<PERSON> saati (HH:MM:SS)';

-- 3. active_end_time sütunu ekle (eğer yoksa)
ALTER TABLE menu_items 
ADD COLUMN IF NOT EXISTS active_end_time TIME NULL COMMENT 'Ürünün aktif olduğu bitiş saati (HH:MM:SS)';

-- 4. <PERSON> ekle (performans için)
ALTER TABLE menu_items 
ADD INDEX IF NOT EXISTS idx_active_hours (active_start_time, active_end_time);

-- 5. Index ekle (QR menü görünürlüğü için)
ALTER TABLE menu_items 
ADD INDEX IF NOT EXISTS idx_visible_in_qr (is_visible_in_qr_menu);

