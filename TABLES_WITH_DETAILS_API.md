# POS - Masalar ve Detayları API

## Endpoint

```
GET /api/v1/pos/tables/details
```

## Açıklama

Tüm masaları ve detaylarını getirir:
- <PERSON><PERSON> (başlık, durum, kapasite)
- <PERSON><PERSON><PERSON> (en son açılan siparişin kullanıcısı)
- <PERSON><PERSON> (en eski siparişin tarihi)
- <PERSON><PERSON><PERSON> tuta<PERSON> (tüm siparişlerin toplam tutarı - indirimler)
- <PERSON><PERSON><PERSON><PERSON> ödeme var mı? (boolean)
- Toplam ödenen tutar

## Request

### Headers
```
Authorization: Bearer YOUR_TOKEN
Content-Type: application/json
```

### Query Parameters
Yok

## Response

### Success (200)
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "table_title": "Masa 1",
      "table_status": "busy",
      "floor": 1,
      "seating_capacity": 4,
      "order_ids": "1,2,3",
      "waiter_name": "<PERSON><PERSON> Yılmaz",
      "table_opened_at": "2024-11-24 14:30:00",
      "total_amount": "250.50",
      "has_partial_payment": 1,
      "total_paid": "100.00"
    },
    {
      "id": 2,
      "table_title": "Masa 2",
      "table_status": "empty",
      "floor": 1,
      "seating_capacity": 2,
      "order_ids": null,
      "waiter_name": "N/A",
      "table_opened_at": null,
      "total_amount": "0.00",
      "has_partial_payment": 0,
      "total_paid": "0.00"
    }
  ]
}
```

### Error (500)
```json
{
  "success": false,
  "message": "Masalar getirilirken bir hata oluştu."
}
```

## Response Alanları

| Alan | Tip | Açıklama |
|------|-----|----------|
| id | int | Masa ID |
| table_title | string | Masa adı |
| table_status | enum | Masa durumu (busy, empty, reserved) |
| floor | int | Kat numarası |
| seating_capacity | int | Oturma kapasitesi |
| order_ids | string | Masadaki sipariş ID'leri (virgülle ayrılmış) |
| waiter_name | string | Garson adı |
| table_opened_at | datetime | Masa açılış saati |
| total_amount | decimal | Hesap tutarı (indirimler düşülmüş) |
| has_partial_payment | boolean | Kısmi ödeme var mı? (1=evet, 0=hayır) |
| total_paid | decimal | Toplam ödenen tutar |

## Hesaplama Mantığı

### Hesap Tutarı (total_amount)
```
= Tüm siparişlerin ürün tutarları (cancelled, waste, complimentary hariç)
  - Tüm indirimler (yüzde ve sabit tutar)
```

### Kısmi Ödeme (has_partial_payment)
```
= Eğer payment_transactions tablosunda bu masa için ödeme kaydı varsa 1, yoksa 0
```

### Toplam Ödenen (total_paid)
```
= payment_transactions tablosundaki tüm completed payment işlemlerinin toplamı
```

## Örnek cURL

```bash
curl -X GET http://localhost:3000/api/v1/pos/tables/details \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"
```

## Notlar

1. **Performans**: SQL sorgusu LEFT JOIN'ler kullanarak tek sorguda tüm verileri getirir
2. **Sıralama**: Masalar kat ve başlığa göre sıralanır
3. **Boş Masalar**: Siparişi olmayan masalar da listeye dahil edilir
4. **İndirim Hesaplaması**: 
   - Yüzde indirim: Ürün tutarının belirtilen yüzdesini düşer
   - Sabit tutar indirim: Belirtilen tutarı düşer
5. **Garson Bilgisi**: En son açılan siparişin kullanıcısı gösterilir
6. **Açılış Saati**: Masadaki en eski siparişin tarihi gösterilir

## Kullanım Örneği (Frontend)

```javascript
// Masaları getir
const response = await fetch('/api/v1/pos/tables/details', {
  method: 'GET',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  }
});

const { data: tables } = await response.json();

// Masaları göster
tables.forEach(table => {
  console.log(`${table.table_title} - Garson: ${table.waiter_name}`);
  console.log(`Hesap: ${table.total_amount} TL`);
  console.log(`Ödenen: ${table.total_paid} TL`);
  console.log(`Kalan: ${table.total_amount - table.total_paid} TL`);
});
```

## Yetkilendirme

- **Scope**: `SCOPES.POS`
- **Middleware**: `isLoggedIn`, `isAuthenticated`, `isSubscriptionActive`

