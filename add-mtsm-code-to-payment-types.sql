-- Payment Types tablosuna mTSM code kolonu ekleme

-- 1. mtsm_code kolonu ekle
ALTER TABLE payment_types 
ADD COLUMN mtsm_code VARCHAR(50) NULL COMMENT 'mTSM ödeme tipi kodu (CashPayment, CardPayment, etc.)';

-- 2. Index ekle (performans için)
ALTER TABLE payment_types 
ADD INDEX idx_mtsm_code (mtsm_code);

-- 3. Mevcut ödeme tiplerini güncelle (örnek veriler)
-- Nakit ödeme
UPDATE payment_types 
SET mtsm_code = 'CashPayment' 
WHERE title LIKE '%nakit%' OR title LIKE '%cash%' OR isCash = 1
LIMIT 1;

-- Kart ödeme
UPDATE payment_types 
SET mtsm_code = 'CardPayment' 
WHERE title LIKE '%kart%' OR title LIKE '%card%' OR isCard = 1
LIMIT 1;

-- 4. Kontrol et
SELECT 
    id,
    title,
    mtsm_code,
    isCash,
    isCard,
    is_active,
    tenant_id
FROM payment_types 
WHERE is_active = 1
ORDER BY tenant_id, id;

-- 5. mTSM code'ları kontrol et
SELECT 
    'mTSM Mapping' as info,
    mtsm_code,
    COUNT(*) as count,
    GROUP_CONCAT(title) as payment_types
FROM payment_types 
WHERE mtsm_code IS NOT NULL
GROUP BY mtsm_code;
