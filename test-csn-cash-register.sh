#!/bin/bash

# CSN → <PERSON><PERSON>leştirme Test Script

echo "🧪 CSN → Kasa <PERSON>ştirme Test..."
echo "================================"

# 1. Tabloları güncelle
echo "📋 1. Tabloları güncelleniyor..."
mysql -u root -p hollyposyeni < add-cash-register-to-devices.sql

echo -e "\n================================\n"

# 2. Test verisi oluştur
echo "📊 2. Test verisi oluşturuluyor..."
mysql -u root -p hollyposyeni -e "
-- Test kasası oluştur
INSERT IGNORE INTO cash_registers (name, tenant_id, is_active, description, location)
VALUES ('Test Kasa 1', 5, 1, 'mTSM Test Kasası', 'Ana Salon');

-- Test cihazı oluştur/güncelle
INSERT INTO yazarkasa_devices 
(tenant_id, device_name, device_serial, application_id, ip_address, port, cash_register_id, is_active)
VALUES (5, 'Test mTSM Cihazı', 'T60008697517', 'APP123', '*************', '8080', 
        (SELECT id FROM cash_registers WHERE name = 'Test Kasa 1' AND tenant_id = 5 LIMIT 1), 1)
ON DUPLICATE KEY UPDATE 
  cash_register_id = (SELECT id FROM cash_registers WHERE name = 'Test Kasa 1' AND tenant_id = 5 LIMIT 1),
  is_active = 1;

-- Test kullanıcısı oluştur
INSERT IGNORE INTO users (username, name, role, tenant_id, password)
VALUES ('test_cashier', 'Test Kasiyer', 'cashier', 5, 'hashed_password');

-- Kasa session'ı aç
INSERT INTO cash_register_sessions 
(cash_register_id, tenant_id, opened_by, opening_amount, status)
SELECT 
  cr.id,
  5,
  'test_cashier',
  100.00,
  'open'
FROM cash_registers cr
WHERE cr.name = 'Test Kasa 1' AND cr.tenant_id = 5
AND NOT EXISTS (
  SELECT 1 FROM cash_register_sessions crs 
  WHERE crs.cash_register_id = cr.id AND crs.status = 'open'
);
"

echo -e "\n================================\n"

# 3. Mevcut durumu kontrol et
echo "🔍 3. Mevcut durum:"
mysql -u root -p hollyposyeni -e "
SELECT 
    'DEVICE-CASH REGISTER MAPPING' as info,
    yd.device_name,
    yd.device_serial as csn,
    cr.name as cash_register_name,
    crs.id as session_id,
    crs.opened_by,
    crs.status as session_status
FROM yazarkasa_devices yd
LEFT JOIN cash_registers cr ON yd.cash_register_id = cr.id
LEFT JOIN cash_register_sessions crs ON cr.id = crs.cash_register_id AND crs.status = 'open'
WHERE yd.tenant_id = 5 AND yd.device_serial = 'T60008697517';
"

echo -e "\n================================\n"

# 4. Test siparişi oluştur
echo "📦 4. Test siparişi oluşturuluyor..."
mysql -u root -p hollyposyeni -e "
INSERT IGNORE INTO orders 
(id, tenant_id, status, payment_status, total_amount, created_at)
VALUES (99999, 5, 'pending', 'pending', 200.00, NOW());
"

echo -e "\n================================\n"

# 5. Webhook test et
echo "💳 5. mTSM Webhook Test (CSN ile):"
curl -X POST "http://*************:3000/api/v1/mtsm-webhook/orders/payment" \
  -H "Content-Type: application/json" \
  -d '{
    "id": "99999",
    "payments": [
      {
        "Type": "CashPayment",
        "Amount": 200,
        "Details": null
      }
    ],
    "receipt": {
      "No": "5",
      "ZNo": "21",
      "EruNo": "1",
      "Date": "2025-09-30T20:00:00.000"
    },
    "csn": "T60008697517"
  }' \
  -w "\nHTTP Status: %{http_code}\n" \
  -s | jq '.'

echo -e "\n================================\n"

# 6. Sonuçları kontrol et
echo "🔍 6. Sonuçlar:"
mysql -u root -p hollyposyeni -e "
SELECT 
    'PAYMENT TRANSACTION RESULT' as info,
    pt.order_id,
    pt.payment_type_id,
    pty.title as payment_type,
    pt.amount,
    pt.created_by,
    pt.cash_register_session_id,
    crs.opened_by as session_opened_by,
    cr.name as cash_register_name
FROM payment_transactions pt
LEFT JOIN payment_types pty ON pt.payment_type_id = pty.id
LEFT JOIN cash_register_sessions crs ON pt.cash_register_session_id = crs.id
LEFT JOIN cash_registers cr ON crs.cash_register_id = cr.id
WHERE pt.order_id = 99999
ORDER BY pt.created_at DESC
LIMIT 1;
"

echo -e "\n📊 Sipariş Durumu:"
mysql -u root -p hollyposyeni -e "
SELECT 
    id,
    status,
    payment_status,
    total_amount
FROM orders 
WHERE id = 99999;
"

echo -e "\n================================"
echo "✅ Test Tamamlandı!"
echo ""
echo "Beklenen Sonuçlar:"
echo "- ✅ CSN T60008697517 → Test Kasa 1 eşleşmesi"
echo "- ✅ Payment transaction'da cash_register_session_id dolu"
echo "- ✅ created_by = test_cashier (session açan kullanıcı)"
echo "- ✅ Sipariş durumu: completed/paid"
