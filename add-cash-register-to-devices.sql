-- yazarkasa_devices tablosuna cash_register_id kolonu ekleme

-- 1. cash_register_id kolonu ekle (nullable - zorun<PERSON> değil)
ALTER TABLE yazarkasa_devices 
ADD COLUMN cash_register_id INT NULL COMMENT 'C<PERSON><PERSON>ın bağlı olduğu kasa ID (opsiyonel)';

-- 2. Foreign key constraint ekle
ALTER TABLE yazarkasa_devices 
ADD CONSTRAINT fk_yazarkasa_devices_cash_register 
FOREIGN KEY (cash_register_id) REFERENCES cash_registers(id) ON DELETE SET NULL;

-- 3. Index ekle (performans için)
ALTER TABLE yazarkasa_devices 
ADD INDEX idx_cash_register_id (cash_register_id);

-- 4. CSN için index ekle (mTSM için)
ALTER TABLE yazarkasa_devices 
ADD INDEX idx_device_serial (device_serial);

-- 5. Mevcut yapıyı kontrol et
SELECT 
    'DEVICES WITH CASH REGISTERS' as info,
    yd.id,
    yd.device_name,
    yd.device_serial as csn,
    yd.cash_register_id,
    cr.name as cash_register_name,
    cr.is_active as cash_register_active,
    yd.is_active as device_active,
    yd.tenant_id
FROM yazarkasa_devices yd
LEFT JOIN cash_registers cr ON yd.cash_register_id = cr.id
WHERE yd.tenant_id = 5  -- Test için tenant 5
ORDER BY yd.id;

-- 6. Aktif kasa session'larını kontrol et
SELECT 
    'ACTIVE CASH SESSIONS' as info,
    crs.id as session_id,
    crs.cash_register_id,
    cr.name as cash_register_name,
    crs.opened_by,
    crs.opening_amount,
    crs.opened_at,
    crs.status,
    crs.tenant_id
FROM cash_register_sessions crs
JOIN cash_registers cr ON crs.cash_register_id = cr.id
WHERE crs.status = 'open' AND crs.tenant_id = 5
ORDER BY crs.opened_at DESC;
