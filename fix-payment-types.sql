-- Payment types tablosunu düzelt

-- 1. mtsm_code kolonu ekle (e<PERSON>er yoksa)
ALTER TABLE payment_types 
ADD COLUMN IF NOT EXISTS mtsm_code VARCHAR(50) NULL COMMENT 'mTSM ödeme tipi kodu';

-- 2. Index ekle
ALTER TABLE payment_types 
ADD INDEX IF NOT EXISTS idx_mtsm_code (mtsm_code);

-- 3. Tenant 5 için mtsm_code'ları güncelle
UPDATE payment_types 
SET mtsm_code = 'CashPayment' 
WHERE tenant_id = 5 
AND (title LIKE '%nakit%' OR title LIKE '%cash%' OR isCash = 1) 
AND mtsm_code IS NULL
LIMIT 1;

UPDATE payment_types 
SET mtsm_code = 'CardPayment' 
WHERE tenant_id = 5 
AND (title LIKE '%kart%' OR title LIKE '%card%' OR isCard = 1) 
AND mtsm_code IS NULL
LIMIT 1;

-- 4. Kontrol et
SELECT 
    'TENANT 5 PAYMENT TYPES' as info,
    id,
    title,
    mtsm_code,
    isCash,
    isCard,
    is_active
FROM payment_types 
WHERE tenant_id = 5 AND is_active = 1
ORDER BY id;
