-- Test: Otomatik API key oluşturma

-- 1. <PERSON><PERSON><PERSON><PERSON> temizle (test için)
-- DELETE FROM mtsm_api_keys WHERE name LIKE 'Test%';

-- 2. Otomatik API key ile kayıt oluştur
INSERT INTO mtsm_api_keys (tenant_id, name, description) 
VALUES (1, 'Test Auto Key 1', 'Otomatik oluşturulan test key');

INSERT INTO mtsm_api_keys (tenant_id, name, description) 
VALUES (1, 'Test Auto Key 2', 'İkinci otomatik test key');

-- 3. Manuel API key ile kayıt oluştur
INSERT INTO mtsm_api_keys (tenant_id, name, description, api_key) 
VALUES (1, 'Test Manual Key', 'Manuel oluşturulan test key', 'mtsm_manual123456789abcdef');

-- 4. <PERSON>uçları kontrol et
SELECT 
    id,
    tenant_id,
    name,
    description,
    CONCAT(SUBSTRING(api_key, 1, 8), '...') as api_key_preview,
    LENGTH(api_key) as key_length,
    is_active,
    created_at
FROM mtsm_api_keys 
WHERE name LIKE 'Test%'
ORDER BY id DESC;

-- 5. API key formatını kontrol et
SELECT 
    name,
    api_key,
    CASE 
        WHEN api_key LIKE 'mtsm_%' THEN '✅ Format OK'
        ELSE '❌ Format Hatalı'
    END as format_check,
    CASE 
        WHEN LENGTH(api_key) = 37 THEN '✅ Length OK (37)'
        ELSE CONCAT('❌ Length: ', LENGTH(api_key))
    END as length_check
FROM mtsm_api_keys 
WHERE name LIKE 'Test%';

-- 6. Benzersizlik kontrolü
SELECT 
    COUNT(*) as total_keys,
    COUNT(DISTINCT api_key) as unique_keys,
    CASE 
        WHEN COUNT(*) = COUNT(DISTINCT api_key) THEN '✅ Tüm key\'ler benzersiz'
        ELSE '❌ Duplicate key\'ler var!'
    END as uniqueness_check
FROM mtsm_api_keys;
