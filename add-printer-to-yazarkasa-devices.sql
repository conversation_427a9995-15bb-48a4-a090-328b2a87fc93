-- yazarkasa_devices tablosuna printer_id kolonu ekleme

-- 1. printer_id kolonu ekle (nullable - zor<PERSON><PERSON> değ<PERSON>)
ALTER TABLE yazarkasa_devices 
ADD COLUMN printer_id INT NULL COMMENT '<PERSON><PERSON>az<PERSON>n bağlı olduğu printer ID (opsiyonel)';

-- 2. Foreign key constraint ekle
ALTER TABLE yazarkasa_devices 
ADD CONSTRAINT fk_yazarkasa_devices_printer 
FOREIGN KEY (printer_id) REFERENCES printers(id) ON DELETE SET NULL;

-- 3. Index ekle (performans için)
ALTER TABLE yazarkasa_devices 
ADD INDEX idx_printer_id (printer_id);

-- 4. Mevcut yapıyı kontrol et
SELECT 
    'DEVICES WITH PRINTERS' as info,
    yd.id,
    yd.device_name,
    yd.device_serial as csn,
    yd.printer_id,
    p.name as printer_name,
    p.path as printer_path,
    p.type as printer_type,
    yd.tenant_id
FROM yazarkasa_devices yd
LEFT JOIN printers p ON yd.printer_id = p.id
WHERE yd.tenant_id = 5
ORDER BY yd.id;

-- 5. Mevcut printer'ları kontrol et
SELECT 
    'AVAILABLE PRINTERS' as info,
    p.id,
    p.name,
    p.path,
    p.type,
    p.tenant_id
FROM printers p
WHERE p.tenant_id = 5
ORDER BY p.name;
