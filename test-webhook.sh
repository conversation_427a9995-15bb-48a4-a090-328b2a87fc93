#!/bin/bash

# mTSM Webhook Test Script
BASE_URL="http://***************:3000/api/v1/mtsm-webhook"
# Bu API key'i gerçek tenant API key'i ile değiştirin
API_KEY="mtsm_test123456789abcdef"  # Gerçek API key

echo "🧪 mTSM Webhook Test Başlıyor..."
echo "Base URL: $BASE_URL"
echo "API Key: ${API_KEY:0:8}..."
echo ""

echo "1️⃣ Siparişleri Listeleme Testi"
echo "GET $BASE_URL/orders?csn=TEST123&pageNumber=1&pageSize=10"
curl -X GET "$BASE_URL/orders?csn=TEST123&pageNumber=1&pageSize=10" \
  -H "x-api-key: $API_KEY" \
  -H "Content-Type: application/json" \
  -w "\nStatus: %{http_code}\n" \
  -s
echo ""
echo "---"

echo "2️⃣ Sipariş Detayı Testi"
echo "GET $BASE_URL/orders/details?id=1"
curl -X GET "$BASE_URL/orders/details?id=1" \
  -H "x-api-key: $API_KEY" \
  -H "Content-Type: application/json" \
  -w "\nStatus: %{http_code}\n" \
  -s
echo ""
echo "---"

echo "3️⃣ Sipariş Durum Güncelleme Testi"
echo "POST $BASE_URL/orders/status"
curl -X POST "$BASE_URL/orders/status" \
  -H "x-api-key: $API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "id": "1",
    "status": "LOCKED"
  }' \
  -w "\nStatus: %{http_code}\n" \
  -s
echo ""
echo "---"

echo "4️⃣ Sipariş Ödeme Testi"
echo "POST $BASE_URL/orders/payment"
curl -X POST "$BASE_URL/orders/payment" \
  -H "x-api-key: $API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "id": "1",
    "payments": [
      {
        "type": "cash",
        "amount": 50.00
      }
    ]
  }' \
  -w "\nStatus: %{http_code}\n" \
  -s
echo ""
echo "---"

echo "5️⃣ Yanlış API Key Testi"
echo "GET $BASE_URL/orders?csn=TEST123 (Wrong API Key)"
curl -X GET "$BASE_URL/orders?csn=TEST123" \
  -H "x-api-key: wrong_api_key" \
  -H "Content-Type: application/json" \
  -w "\nStatus: %{http_code}\n" \
  -s
echo ""

echo "6️⃣ API Key Olmadan Testi"
echo "GET $BASE_URL/orders?csn=TEST123 (No API Key)"
curl -X GET "$BASE_URL/orders?csn=TEST123" \
  -H "Content-Type: application/json" \
  -w "\nStatus: %{http_code}\n" \
  -s
echo ""

echo "✅ Test tamamlandı!"
