-- mTSM API Keys tablosu oluşturma
CREATE TABLE IF NOT EXISTS `mtsm_api_keys` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `tenant_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL COMMENT 'API key adı (örn: TSM Cihaz 1)',
  `description` text DEFAULT NULL COMMENT 'API key açıklaması',
  `api_key` varchar(255) DEFAULT NULL UNIQUE COMMENT 'Benzersiz API key (otomatik oluşturulur)',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'API key aktif mi?',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `last_used_at` timestamp NULL DEFAULT NULL COMMENT 'Son kull<PERSON>m zamanı',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_api_key` (`api_key`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_api_key_active` (`api_key`, `is_active`),
  CONSTRAINT `fk_mtsm_api_keys_tenant` FOREIGN KEY (`tenant_id`) REFERENCES `tenants` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='mTSM Webhook API Keys';

-- Otomatik API key oluşturma fonksiyonu
DELIMITER $$

CREATE FUNCTION IF NOT EXISTS generate_api_key()
RETURNS VARCHAR(255)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE api_key VARCHAR(255);
    DECLARE key_exists INT DEFAULT 1;

    WHILE key_exists > 0 DO
        -- mtsm_ prefix + 32 karakter random string
        SET api_key = CONCAT('mtsm_',
            SUBSTRING(MD5(CONCAT(UNIX_TIMESTAMP(), RAND(), CONNECTION_ID())), 1, 32)
        );

        -- Key'in benzersiz olduğunu kontrol et
        SELECT COUNT(*) INTO key_exists
        FROM mtsm_api_keys
        WHERE api_key = api_key;
    END WHILE;

    RETURN api_key;
END$$

-- API key otomatik oluşturma trigger'ı
CREATE TRIGGER IF NOT EXISTS `tr_mtsm_api_keys_before_insert`
BEFORE INSERT ON `mtsm_api_keys`
FOR EACH ROW
BEGIN
    IF NEW.api_key IS NULL OR NEW.api_key = '' THEN
        SET NEW.api_key = generate_api_key();
    END IF;
END$$

DELIMITER ;

-- Test için örnek kayıt (api_key otomatik oluşturulacak)
-- INSERT INTO mtsm_api_keys (tenant_id, name, description)
-- VALUES (1, 'Otomatik Test Key', 'Otomatik oluşturulan test key');

-- Mevcut tabloları kontrol et
SELECT 'mtsm_api_keys tablosu ve trigger\'lar oluşturuldu' as status;
