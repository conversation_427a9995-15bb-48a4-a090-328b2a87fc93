#!/bin/bash

# mTSM Webhook Test Script
BASE_URL="http://***************:3000/api/v1/mtsm-webhook"
API_KEY="mtsm_test123456789abcdef"  # Test API key

echo "🧪 mTSM Webhook Test Başlıyor..."
echo "================================"

# 1. Siparişleri Listeleme (GET)
echo "📋 1. Siparişleri Listeleme Test..."
curl -X GET "${BASE_URL}/orders?csn=12345&pageNumber=1&pageSize=10" \
  -H "x-api-key: ${API_KEY}" \
  -H "Content-Type: application/json" \
  -w "\nStatus: %{http_code}\n" \
  -s | jq '.'

echo -e "\n================================\n"

# 2. Sipar<PERSON><PERSON> Detayları (GET)
echo "📄 2. Sipar<PERSON>ş Detayları Test..."
curl -X GET "${BASE_URL}/orders/details?id=1" \
  -H "x-api-key: ${API_KEY}" \
  -H "Content-Type: application/json" \
  -w "\nStatus: %{http_code}\n" \
  -s | jq '.'

echo -e "\n================================\n"

# 3. Sipariş Durum Güncelleme (POST)
echo "🔄 3. Sipariş Durum Güncelleme Test..."
curl -X POST "${BASE_URL}/orders/status" \
  -H "x-api-key: ${API_KEY}" \
  -H "Content-Type: application/json" \
  -d '{
    "id": "1",
    "status": "LOCKED",
    "payments": [
      {
        "type": "cash",
        "amount": 50.00
      }
    ]
  }' \
  -w "\nStatus: %{http_code}\n" \
  -s | jq '.'

echo -e "\n================================\n"

# 4. Sipariş Ödeme (POST) - TSM Format
echo "💳 4. Sipariş Ödeme Test (TSM Format)..."
curl -X POST "${BASE_URL}/orders/payment" \
  -H "Content-Type: application/json" \
  -d '{
    "id": "48791",
    "payments": [
      {
        "Type": "CashPayment",
        "Amount": 200,
        "Details": null
      }
    ],
    "receipt": {
      "No": "4",
      "ZNo": "20",
      "EruNo": "1",
      "Date": "2025-09-30T19:02:19.452"
    },
    "csn": "T60008697517"
  }' \
  -w "\nStatus: %{http_code}\n" \
  -s | jq '.'

echo -e "\n================================"
echo "✅ mTSM Webhook Test Tamamlandı!"
