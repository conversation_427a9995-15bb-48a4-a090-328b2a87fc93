#!/bin/bash

# Gerçek sipariş ID'si ile test

echo "🧪 Gerçek Sipariş ID'si ile Test..."
echo "================================"

# 1. Mevcut siparişleri listele
echo "📋 1. Mevcut siparişler:"
mysql -u root -p hollyposyeni -e "
SELECT 
    o.id,
    o.tenant_id,
    o.status,
    o.payment_status,
    o.table_id,
    st.table_title,
    st.status as table_status
FROM orders o
LEFT JOIN store_tables st ON o.table_id = st.id
WHERE o.tenant_id = 5 
AND o.status = 'pending' 
AND o.payment_status = 'pending'
ORDER BY o.id DESC
LIMIT 5;
"

echo -e "\n================================\n"

# 2. Payment types kontrol et
echo "📊 2. Payment Types (Tenant 5):"
mysql -u root -p hollyposyeni -e "
SELECT 
    id,
    title,
    mtsm_code,
    isCash,
    isCard,
    is_active
FROM payment_types 
WHERE tenant_id = 5 AND is_active = 1
ORDER BY id;
"

echo -e "\n================================\n"

# 3. Users kontrol et
echo "👥 3. Users (Tenant 5):"
mysql -u root -p hollyposyeni -e "
SELECT 
    username,
    name,
    role,
    tenant_id
FROM users 
WHERE tenant_id = 5
LIMIT 3;
"

echo -e "\n================================\n"

# 4. Webhook test et (gerçek order ID ile)
echo "💳 4. Webhook Test (Order ID: 49185):"
curl -X POST "http://*************:3000/api/v1/mtsm-webhook/orders/payment" \
  -H "Content-Type: application/json" \
  -d '{
    "id": "49185",
    "payments": [
      {
        "Type": "CashPayment",
        "Amount": 200,
        "Details": null
      }
    ],
    "receipt": {
      "No": "4",
      "ZNo": "20",
      "EruNo": "1",
      "Date": "2025-09-30T19:02:19.452"
    },
    "csn": "T60008697517"
  }' \
  -w "\nHTTP Status: %{http_code}\n" \
  -s | jq '.'

echo -e "\n================================\n"

# 5. Sonuçları kontrol et
echo "🔍 5. Sonuçlar:"
mysql -u root -p hollyposyeni -e "
SELECT 
    'AFTER PAYMENT' as stage,
    o.id as order_id,
    o.status as order_status,
    o.payment_status,
    o.table_id,
    st.table_title,
    st.status as table_status,
    st.order_ids as table_order_ids
FROM orders o
LEFT JOIN store_tables st ON o.table_id = st.id
WHERE o.id = 49185;
"

echo -e "\n📊 Payment Transactions:"
mysql -u root -p hollyposyeni -e "
SELECT 
    pt.order_id,
    pt.payment_type_id,
    pty.title as payment_type,
    pty.mtsm_code,
    pt.amount,
    pt.created_by,
    pt.created_at
FROM payment_transactions pt
LEFT JOIN payment_types pty ON pt.payment_type_id = pty.id
WHERE pt.order_id = 49185
ORDER BY pt.created_at DESC;
"

echo -e "\n================================"
echo "✅ Test Tamamlandı!"
