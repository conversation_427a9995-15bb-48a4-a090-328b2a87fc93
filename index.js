const app = require("./src/app");

const { createServer } = require("http");
const { Server } = require("socket.io");
const { CONFIG } = require("./src/config");
const { getTenantIdFromQRCode } = require("./src/services/settings.service");

const PORT = process.env.PORT || 3000;

const httpServer = createServer(app);
const io = new Server(httpServer, { 
    cors: {
        origin: "*",
        origin: CONFIG.FRONTEND_DOMAIN,
        methods: ["GET", "POST"],
    }
});

global.io = io;

io.on("connection", (socket)=>{
    console.log(socket.id);

    socket.on('authenticate', async (tenantId) => {
    console.log(`🔐 Tenant ${tenantId} authenticate oldu, socket ID: ${socket.id}`);
    socket.join(tenantId);
    console.log(`✅ Socket ${socket.id} tenant ${tenantId} room'una katıldı`);
    console.log(`🏠 Tenant ${tenantId} room'undaki toplam client: ${io.sockets.adapter.rooms.get(tenantId)?.size || 0}`);
});

    socket.on("new_order_backend", (payload, tenantId)=>{
        console.log("new_order", payload, tenantId);
        socket.to(tenantId).emit("new_order", payload);
    })

    socket.on("order_update_backend", (payload, tenantId)=>{
        socket.to(tenantId).emit("order_update", payload);
    })

    socket.on("new_qrorder_backend", async (payload, qrcode)=>{
        try {
            const tenantId = await getTenantIdFromQRCode(qrcode);
            socket.to(tenantId).emit("new_qrorder", payload);
        } catch (error) {
            console.log(error);
        }
    })

    socket.on("new_call_backend", (callData, tenantId) => {
        console.log(callData);
        socket.to(tenantId).emit("incoming_call", callData);
    });
    

    socket.on("call_ended_backend", async (callData, tenantId) => {
        socket.to(tenantId).emit("call_completed", callData);
    });

    socket.on("print_order_backend", (printData, tenantId) => {
        socket.to(tenantId).emit("print-order", printData);
    });

    // Yazarkasa işlemi için backend'den masaüstü uygulamasına
    // Yazarkasa işlemi için backend'den masaüstü uygulamasına
// Yazarkasa işlemi için backend'den masaüstü uygulamasına
socket.on("yazarkasa_process_backend", (yazarkasaData, tenantId) => {
    console.log(`📤 Yazarkasa mesajı tenant ${tenantId}'e gönderiliyor:`, yazarkasaData.requestId);
    console.log(`🏠 Tenant ${tenantId} room'undaki client sayısı:`, io.sockets.adapter.rooms.get(tenantId)?.size || 0);
    
    socket.to(tenantId).emit("yazarkasa_process", yazarkasaData);
    
    console.log(`✅ Yazarkasa mesajı emit edildi`);
});

socket.onAny((eventName, ...args) => {
        console.log(`📨 Event alındı: ${eventName}`, args.length > 0 ? args[0] : '');
    });

    // Masaüstü uygulamasından yazarkasa sonucu
   // Masaüstü uygulamasından yazarkasa sonucu
// Her iki event'i de dinle
socket.on("yazarkasa_result_backend", (resultData, tenantId) => {
    console.log(`📥 yazarkasa_result_backend alındı:`, resultData.requestId);
    global.io.emit("yazarkasa-result", resultData);
    socket.to(tenantId).emit("yazarkasa-result", resultData);
});

socket.on("yazarkasa-result", (resultData) => {
    console.log(`📥 yazarkasa-result alındı:`, resultData.requestId);
    
    // Direkt global değişkene kaydet
    global.yazarkasaResults = global.yazarkasaResults || {};
    global.yazarkasaResults[resultData.requestId] = resultData;
    
    console.log(`💾 Yazarkasa sonucu kaydedildi: ${resultData.requestId}`);
});

});

httpServer.listen(PORT);

// app.listen(PORT, ()=>{
//     console.log(`Server Started on PORT: ${PORT}`);
// });