const app = require("./src/app");

const { createServer } = require("http");
const { Server } = require("socket.io");
const { CONFIG } = require("./src/config");
const { getTenantIdFromQRCode } = require("./src/services/settings.service");

const PORT = process.env.PORT || 3000;

const httpServer = createServer(app);
const io = new Server(httpServer, { 
    cors: {
        credentials: true,
        origin: CONFIG.FRONTEND_DOMAIN,
        methods: ["GET", "POST"],
    }
});

global.io = io;

io.on("connection", (socket)=>{
    console.log(socket.id);

    socket.on('authenticate', async (tenantId) => {
        socket.join(tenantId); // Join the room for the restaurant
    });

    socket.on("new_order_backend", (payload, tenantId)=>{
        console.log("new_order", payload, tenantId);
        socket.to(tenantId).emit("new_order", payload);
    })

    socket.on("order_update_backend", (payload, tenantId)=>{
        socket.to(tenantId).emit("order_update", payload);
    })

    socket.on("new_qrorder_backend", async (payload, qrcode)=>{
        try {
            const tenantId = await getTenantIdFromQRCode(qrcode);
            socket.to(tenantId).emit("new_qrorder", payload);
        } catch (error) {
            console.log(error);
        }
    })

    socket.on("new_call_backend", (callData, tenantId) => {
        console.log(callData);
        socket.to(tenantId).emit("incoming_call", callData);
    });
    

    socket.on("call_ended_backend", async (callData, tenantId) => {
        socket.to(tenantId).emit("call_completed", callData);
    });

    socket.on("print_order_backend", (printData, tenantId) => {
        socket.to(tenantId).emit("print-order", printData);
    });
});

httpServer.listen(PORT);

// app.listen(PORT, ()=>{
//     console.log(`Server Started on PORT: ${PORT}`);
// });