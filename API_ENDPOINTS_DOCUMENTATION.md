# Menu Item API Endpoints - <PERSON><PERSON>

## 1. QR Menü Görünürlüğ<PERSON>ğiştir

### Endpoint
```
PATCH /api/v1/menu-items/change-qr-visibility/:id
```

### Açıklama
Bir ürünün QR menüde görünüp görünmeyeceğini değiştirir. Sadece `is_visible_in_qr_menu` alanını günceller.

### Request Body
```json
{
  "isVisibleInQrMenu": true
}
```

### Response (Success)
```json
{
  "success": true,
  "message": "QR menü görünürlüğü başarıyla güncellendi."
}
```

### Response (Error)
```json
{
  "success": false,
  "message": "isVisibleInQrMenu alanı gereklidir."
}
```

### Örnek cURL
```bash
curl -X PATCH http://localhost:3000/api/v1/menu-items/change-qr-visibility/5 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"isVisibleInQrMenu": false}'
```

---

## 2. Ürün Aktif Saatleri Güncelle

### Endpoint
```
PATCH /api/v1/menu-items/update-active-hours/:id
```

### Açıklama
Bir ürünün hangi saatlerde aktif olacağını belirler. Örneğin, bir ürün sadece 18:00-20:00 saatleri arasında görünebilir.

### Request Body
```json
{
  "activeStartTime": "18:00:00",
  "activeEndTime": "20:00:00"
}
```

### Saat Formatı
- Format: `HH:MM:SS` (24 saat formatı)
- Örnek: `18:00:00`, `09:30:45`, `23:59:59`

### Response (Success)
```json
{
  "success": true,
  "message": "Ürün aktif saatleri başarıyla güncellendi."
}
```

### Response (Error - Geçersiz Format)
```json
{
  "success": false,
  "message": "activeStartTime formatı HH:MM:SS olmalıdır."
}
```

### Örnek cURL
```bash
curl -X PATCH http://localhost:3000/api/v1/menu-items/update-active-hours/5 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "activeStartTime": "18:00:00",
    "activeEndTime": "20:00:00"
  }'
```

### Aktif Saatleri Kaldırma
Aktif saatleri kaldırmak için null gönderin:
```json
{
  "activeStartTime": null,
  "activeEndTime": null
}
```

---

## 3. Ürün Bilgisi Alma (Güncellenmiş)

### Endpoint
```
GET /api/v1/menu-items/:id
```

### Response (Güncellenmiş Alanlar)
```json
{
  "id": 5,
  "title": "Kahvaltı Menüsü",
  "description": "Sabah 9-11 arası özel menü",
  "price": 45.00,
  "net_price": 38.14,
  "tax_id": 1,
  "tax_title": "KDV %18",
  "tax_rate": 18,
  "tax_type": "percentage",
  "category_id": 2,
  "category_title": "Kahvaltı",
  "image": "/public/1/5.webp",
  "is_enabled": true,
  "sort_order": 1,
  "is_visible_in_qr_menu": true,
  "active_start_time": "09:00:00",
  "active_end_time": "11:00:00",
  "addons": [],
  "variants": [],
  "recipeItems": []
}
```

---

## 4. Tüm Ürünleri Alma (Güncellenmiş)

### Endpoint
```
GET /api/v1/menu-items
```

### Response (Güncellenmiş Alanlar)
Her ürün aşağıdaki yeni alanları içerir:
- `is_visible_in_qr_menu`: QR menüde görünür mü?
- `active_start_time`: Aktif başlangıç saati
- `active_end_time`: Aktif bitiş saati

---

## Database Schema Değişiklikleri

### Yeni Sütunlar
```sql
ALTER TABLE menu_items 
ADD COLUMN is_visible_in_qr_menu TINYINT(1) NOT NULL DEFAULT 1;

ALTER TABLE menu_items 
ADD COLUMN active_start_time TIME NULL;

ALTER TABLE menu_items 
ADD COLUMN active_end_time TIME NULL;
```

### Yeni Indexler
```sql
ALTER TABLE menu_items 
ADD INDEX idx_active_hours (active_start_time, active_end_time);

ALTER TABLE menu_items 
ADD INDEX idx_visible_in_qr (is_visible_in_qr_menu);
```

---

## Kullanım Örnekleri

### Örnek 1: Ürünü QR menüden gizle
```bash
curl -X PATCH http://localhost:3000/api/v1/menu-items/change-qr-visibility/5 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"isVisibleInQrMenu": false}'
```

### Örnek 2: Ürünü sadece akşam 18:00-20:00 arasında göster
```bash
curl -X PATCH http://localhost:3000/api/v1/menu-items/update-active-hours/5 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "activeStartTime": "18:00:00",
    "activeEndTime": "20:00:00"
  }'
```

### Örnek 3: Ürünü sabah 09:00-11:00 arasında göster
```bash
curl -X PATCH http://localhost:3000/api/v1/menu-items/update-active-hours/5 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "activeStartTime": "09:00:00",
    "activeEndTime": "11:00:00"
  }'
```

---

## Notlar

1. **Saat Formatı**: Tüm saatler UTC+3 (Türkiye saati) olarak kaydedilir
2. **QR Menü Görünürlüğü**: Varsayılan olarak `true` (görünür)
3. **Aktif Saatler**: Boş bırakılırsa ürün her zaman aktif olur
4. **Yetkilendirme**: Tüm endpoint'ler `SCOPES.INVENTORY` yetkisi gerektirir

