-- CSN Debug Script

-- 1. CSN'i kontrol et
SELECT 
    'CSN DEVICE CHECK' as info,
    yd.id,
    yd.device_name,
    yd.device_serial,
    yd.cash_register_id,
    yd.is_active,
    yd.tenant_id
FROM yazarkasa_devices yd
WHERE yd.device_serial = 'T60008697517';

-- 2. <PERSON><PERSON><PERSON> yoksa, mevcut cihazları listele
SELECT 
    'ALL DEVICES' as info,
    yd.device_serial,
    yd.device_name,
    yd.tenant_id,
    yd.is_active
FROM yazarkasa_devices yd
WHERE yd.tenant_id = 5
ORDER BY yd.created_at DESC;

-- 3. <PERSON><PERSON><PERSON><PERSON> kontrol et
SELECT 
    'CASH REGISTERS' as info,
    cr.id,
    cr.name,
    cr.tenant_id,
    cr.is_active
FROM cash_registers cr
WHERE cr.tenant_id = 5;

-- 4. Aktif kasa session'larını kontrol et
SELECT 
    'ACTIVE SESSIONS' as info,
    crs.id,
    crs.cash_register_id,
    cr.name as cash_register_name,
    crs.opened_by,
    crs.status,
    crs.tenant_id
FROM cash_register_sessions crs
JOIN cash_registers cr ON crs.cash_register_id = cr.id
WHERE crs.tenant_id = 5 AND crs.status = 'open';

-- 5. CSN ile tam eşleştirme testi
SELECT 
    'CSN FULL MAPPING TEST' as info,
    yd.device_serial as csn,
    yd.cash_register_id,
    cr.name as cash_register_name,
    crs.id as session_id,
    crs.opened_by,
    crs.status as session_status
FROM yazarkasa_devices yd
LEFT JOIN cash_registers cr ON yd.cash_register_id = cr.id
LEFT JOIN cash_register_sessions crs ON cr.id = crs.cash_register_id 
  AND crs.status = 'open' AND crs.tenant_id = 5
WHERE yd.device_serial = 'T60008697517' AND yd.tenant_id = 5 AND yd.is_active = 1;

-- 6. Test CSN'i ekle (eğer yoksa)
INSERT IGNORE INTO yazarkasa_devices 
(tenant_id, device_name, device_serial, application_id, ip_address, port, is_active)
VALUES (5, 'Test mTSM Device', 'T60008697517', 'APP123', '*************', '8080', 1);

-- 7. Test kasası ekle (eğer yoksa)
INSERT IGNORE INTO cash_registers (name, tenant_id, is_active, description)
VALUES ('mTSM Test Kasa', 5, 1, 'Test için oluşturulan kasa');

-- 8. CSN'i kasaya bağla
UPDATE yazarkasa_devices 
SET cash_register_id = (
    SELECT id FROM cash_registers 
    WHERE name = 'mTSM Test Kasa' AND tenant_id = 5 
    LIMIT 1
)
WHERE device_serial = 'T60008697517' AND tenant_id = 5;

-- 9. Test kullanıcısı ekle
INSERT IGNORE INTO users (username, name, role, tenant_id, password)
VALUES ('mtsm_test_user', 'mTSM Test User', 'cashier', 5, '$2b$10$test');

-- 10. Kasa session'ı aç
INSERT IGNORE INTO cash_register_sessions 
(cash_register_id, tenant_id, opened_by, opening_amount, status)
SELECT 
    cr.id,
    5,
    'mtsm_test_user',
    0.00,
    'open'
FROM cash_registers cr
WHERE cr.name = 'mTSM Test Kasa' AND cr.tenant_id = 5;

-- 11. Final kontrol
SELECT 
    'FINAL CHECK' as info,
    yd.device_serial as csn,
    cr.name as kasa_adi,
    crs.id as session_id,
    crs.opened_by,
    crs.status
FROM yazarkasa_devices yd
JOIN cash_registers cr ON yd.cash_register_id = cr.id
JOIN cash_register_sessions crs ON cr.id = crs.cash_register_id 
WHERE yd.device_serial = 'T60008697517' 
  AND yd.tenant_id = 5 
  AND crs.status = 'open';
