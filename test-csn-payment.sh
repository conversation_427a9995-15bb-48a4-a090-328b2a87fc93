#!/bin/bash

# CSN ile Payment Webhook Test

echo "🧪 CSN ile Payment Webhook Test..."
echo "================================"

# 1. Mevcut CSN cihazını kontrol et
echo "📱 1. CSN Cihaz Kontrolü:"
mysql -u root -p hollyposyeni -e "
SELECT 
    'CSN DEVICE CHECK' as info,
    yd.device_name,
    yd.device_serial as csn,
    yd.cash_register_id,
    cr.name as cash_register_name,
    yd.is_active
FROM yazarkasa_devices yd
LEFT JOIN cash_registers cr ON yd.cash_register_id = cr.id
WHERE yd.device_serial = 'T60008697517' AND yd.tenant_id = 5;
"

echo -e "\n================================\n"

# 2. Aktif kasa session'ını kontrol et
echo "🏦 2. Aktif <PERSON>sa <PERSON> Kontrolü:"
mysql -u root -p hollyposyeni -e "
SELECT 
    'ACTIVE CASH SESSION' as info,
    crs.id as session_id,
    crs.cash_register_id,
    cr.name as cash_register_name,
    crs.opened_by,
    crs.status,
    crs.opened_at
FROM cash_register_sessions crs
JOIN cash_registers cr ON crs.cash_register_id = cr.id
WHERE crs.cash_register_id = 7 AND crs.status = 'open' AND crs.tenant_id = 5;
"

echo -e "\n================================\n"

# 3. Test siparişi hazırla
echo "📦 3. Test Siparişi Hazırlama:"
mysql -u root -p hollyposyeni -e "
-- Test siparişi oluştur
INSERT IGNORE INTO orders (id, tenant_id, status, payment_status, total_amount, date)
VALUES (77777, 5, 'pending', 'pending', 200.00, NOW());

-- Sipariş durumunu kontrol et
SELECT 
    'TEST ORDER' as info,
    id,
    status,
    payment_status,
    total_amount,
    tenant_id
FROM orders 
WHERE id = 77777;
"

echo -e "\n================================\n"

# 4. CSN ile Payment Webhook Test
echo "💳 4. CSN ile Payment Webhook Çağırma:"
curl -X POST "http://***********:3000/api/v1/mtsm-webhook/orders/payment" \
  -H "Content-Type: application/json" \
  -d '{
    "id": "77777",
    "payments": [
      {
        "Type": "CashPayment",
        "Amount": 200,
        "Details": null
      }
    ],
    "receipt": {
      "No": "5",
      "ZNo": "21",
      "EruNo": "1",
      "Date": "2025-09-30T20:30:00.000"
    },
    "csn": "T60008697517"
  }' \
  -w "\nHTTP Status: %{http_code}\n" \
  -s | jq '.'

echo -e "\n================================\n"

# 5. Sonuçları kontrol et
echo "🔍 5. Payment Transaction Sonucu:"
mysql -u root -p hollyposyeni -e "
SELECT 
    'PAYMENT RESULT' as info,
    pt.order_id,
    pt.payment_type_id,
    pty.title as payment_type,
    pt.amount,
    pt.created_by,
    pt.cash_register_session_id,
    crs.opened_by as session_opened_by,
    cr.name as cash_register_name,
    pt.created_at
FROM payment_transactions pt
LEFT JOIN payment_types pty ON pt.payment_type_id = pty.id
LEFT JOIN cash_register_sessions crs ON pt.cash_register_session_id = crs.id
LEFT JOIN cash_registers cr ON crs.cash_register_id = cr.id
WHERE pt.order_id = 77777
ORDER BY pt.created_at DESC
LIMIT 1;
"

echo -e "\n📊 Sipariş Durumu:"
mysql -u root -p hollyposyeni -e "
SELECT 
    'ORDER STATUS' as info,
    id,
    status,
    payment_status,
    total_amount
FROM orders 
WHERE id = 77777;
"

echo -e "\n================================"
echo "✅ Test Tamamlandı!"
echo ""
echo "Beklenen Sonuçlar:"
echo "- ✅ HTTP Status: 200"
echo "- ✅ cash_register_session_id: DOLU (NULL değil)"
echo "- ✅ created_by: Kasa session açan kullanıcı"
echo "- ✅ Sipariş durumu: completed/paid"
echo ""
echo "Console loglarında şunları görmeli:"
echo "- 🏦 CSN T60008697517 → Kasa: [Kasa Adı], Session: [Session ID]"
echo "- 💰 Ödeme kaydedildi - Session: [Session ID]"
