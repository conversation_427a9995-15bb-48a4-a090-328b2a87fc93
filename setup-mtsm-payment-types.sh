#!/bin/bash

# mTSM Payment Types Kurulum Script

echo "🔧 mTSM Payment Types Kurulumu Başlıyor..."
echo "================================"

# 1. mtsm_code kolonu ekle
echo "📋 1. Payment types tablosuna mtsm_code kolonu ekleniyor..."
mysql -u root -p your_database < add-mtsm-code-to-payment-types.sql

echo -e "\n================================\n"

# 2. Test verilerini hazırla
echo "📋 2. Test verileri hazırlanıyor..."
mysql -u root -p your_database < test-payment-webhook.sql

echo -e "\n================================\n"

# 3. Payment types'ları kontrol et
echo "📊 3. Payment types kontrol ediliyor..."
mysql -u root -p your_database -e "
SELECT 
    'PAYMENT TYPES MAPPING' as info,
    id,
    title,
    mtsm_code,
    isCash,
    isCard,
    is_active,
    tenant_id
FROM payment_types 
WHERE is_active = 1
ORDER BY tenant_id, id;
"

echo -e "\n📊 mTSM Code Mapping:"
mysql -u root -p your_database -e "
SELECT 
    'mTSM MAPPING' as info,
    mtsm_code,
    COUNT(*) as count,
    GROUP_CONCAT(CONCAT(title, ' (ID:', id, ')')) as payment_types
FROM payment_types 
WHERE mtsm_code IS NOT NULL AND is_active = 1
GROUP BY mtsm_code;
"

echo -e "\n================================"
echo "✅ mTSM Payment Types Kurulumu Tamamlandı!"
echo ""
echo "Mapping Bilgileri:"
echo "- CashPayment → Nakit ödeme tipi"
echo "- CardPayment → Kart ödeme tipi"
echo ""
echo "Artık webhook test edebilirsiniz:"
echo "curl -X POST 'http://*************:3000/api/v1/mtsm-webhook/orders/payment' \\"
echo "  -H 'Content-Type: application/json' \\"
echo "  -d '{ \"id\": \"48791\", \"payments\": [{ \"Type\": \"CashPayment\", \"Amount\": 200 }] }'"
