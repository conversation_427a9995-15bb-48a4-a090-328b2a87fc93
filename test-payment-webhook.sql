-- Test için örnek sipariş ve masa oluşturma

-- 0. Payment types'a mTSM code'lar<PERSON> ekle (eğer yoksa)
UPDATE payment_types
SET mtsm_code = 'CashPayment'
WHERE (title LIKE '%nakit%' OR title LIKE '%cash%' OR isCash = 1)
AND tenant_id = 1 AND mtsm_code IS NULL
LIMIT 1;

UPDATE payment_types
SET mtsm_code = 'CardPayment'
WHERE (title LIKE '%kart%' OR title LIKE '%card%' OR isCard = 1)
AND tenant_id = 1 AND mtsm_code IS NULL
LIMIT 1;

-- 1. Test masası oluştur (eğer yoksa)
INSERT IGNORE INTO store_tables (id, tenant_id, table_title, status, order_ids, created_at)
VALUES (999, 1, 'Test Masa 999', 'occupied', '48791', NOW());

-- 2. Test siparişi oluştur (eğer yoksa)
INSERT IGNORE INTO orders (
    id, tenant_id, date, delivery_type, customer_type, 
    table_id, status, payment_status, token_no, username, created_at
) VALUES (
    48791, 1, NOW(), 'dine_in', 'guest', 
    999, 'pending', 'pending', 'T001', 'test_user', NOW()
);

-- 3. Test sipariş item'ları ekle
INSERT IGNORE INTO order_items (
    order_id, tenant_id, item_name, quantity, price, status, created_at
) VALUES 
(48791, 1, 'Test Kahve', 2, 100, 'pending', NOW());

-- 4. Mevcut durumu kontrol et
SELECT 
    'BEFORE PAYMENT' as stage,
    o.id as order_id,
    o.status as order_status,
    o.payment_status,
    o.table_id,
    st.table_title,
    st.status as table_status,
    st.order_ids as table_order_ids
FROM orders o
LEFT JOIN store_tables st ON o.table_id = st.id
WHERE o.id = 48791;

-- 5. Mevcut ödemeleri kontrol et
SELECT
    'BEFORE PAYMENT' as stage,
    pt.order_id,
    pt.payment_type_id,
    pty.title as payment_type,
    pty.mtsm_code,
    pt.amount,
    pt.created_at
FROM payment_transactions pt
LEFT JOIN payment_types pty ON pt.payment_type_id = pty.id
WHERE pt.order_id = 48791;

-- 6. Payment types'ları kontrol et
SELECT
    'PAYMENT TYPES' as info,
    id,
    title,
    mtsm_code,
    isCash,
    isCard,
    is_active,
    tenant_id
FROM payment_types
WHERE tenant_id = 1 AND is_active = 1
ORDER BY id;

-- Bu script'i çalıştırdıktan sonra webhook'u test et:
-- curl -X POST "http://localhost:3000/api/v1/mtsm-webhook/orders/payment" \
--   -H "Content-Type: application/json" \
--   -d '{
--     "id": "48791",
--     "payments": [
--       {
--         "Type": "CashPayment",
--         "Amount": 200,
--         "Details": null
--       }
--     ],
--     "receipt": {
--       "No": "4",
--       "ZNo": "20", 
--       "EruNo": "1",
--       "Date": "2025-09-30T19:02:19.452"
--     },
--     "csn": "T60008697517"
--   }'

-- Sonra bu script'i çalıştır:
-- SELECT 
--     'AFTER PAYMENT' as stage,
--     o.id as order_id,
--     o.status as order_status,
--     o.payment_status,
--     o.table_id,
--     st.table_title,
--     st.status as table_status,
--     st.order_ids as table_order_ids
-- FROM orders o
-- LEFT JOIN store_tables st ON o.table_id = st.id
-- WHERE o.id = 48791;

-- SELECT 
--     'AFTER PAYMENT' as stage,
--     pt.order_id,
--     pt.payment_type,
--     pt.amount,
--     pt.created_at
-- FROM payment_transactions pt
-- WHERE pt.order_id = 48791;
