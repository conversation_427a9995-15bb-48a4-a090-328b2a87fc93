const { getMySqlPromiseConnection } = require("../config/mysql.db");

// Ödenmemiş siparişleri bulan servis
exports.getUnpaidOrdersDB = async (type, from, to, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {
        const { filter, params } = getFilterCondition('CONVERT_TZ(o.date, \'+00:00\', \'+03:00\')', type, from, to);

        const sql = `
        SELECT
            o.id as order_id,
            o.date as order_date,
            o.delivery_type,
            o.table_id,
            st.table_title,
            o.customer_id,
            c.name as customer_name,
            c.phone as customer_phone,
            o.payment_status,
            o.status as order_status,
            o.username as created_by,
            u.name as created_by_name,

            -- Sipariş toplam tutarı (indirimler düşülmemiş)
            COALESCE(SUM(oi.price * oi.quantity), 0) as order_total_gross,

            -- <PERSON><PERSON><PERSON> tutarı
            COALESCE(discount_summary.total_discount, 0) as total_discount,

            -- Net sipariş tutarı (indirimler düşülmüş)
            COALESCE(SUM(oi.price * oi.quantity), 0) - COALESCE(discount_summary.total_discount, 0) as order_total,

            -- Ödenen toplam tutar (payment_transactions'dan)
            COALESCE(pt_summary.total_paid, 0) as total_paid,

            -- Kalan borç (net tutar - ödenen tutar)
            (COALESCE(SUM(oi.price * oi.quantity), 0) - COALESCE(discount_summary.total_discount, 0)) - COALESCE(pt_summary.total_paid, 0) as remaining_debt,

            -- Sipariş öğeleri
            GROUP_CONCAT(
                CONCAT(
                    mi.title,
                    CASE WHEN miv.title IS NOT NULL THEN CONCAT(' (', miv.title, ')') ELSE '' END,
                    ' x', oi.quantity,
                    ' = ', (oi.price * oi.quantity), ' TL'
                )
                SEPARATOR ' | '
            ) as order_items

        FROM orders o
        LEFT JOIN store_tables st ON o.table_id = st.id
        LEFT JOIN customers c ON o.customer_id = c.phone AND o.tenant_id = c.tenant_id
        LEFT JOIN users u ON o.username = u.username AND o.tenant_id = u.tenant_id
        LEFT JOIN order_items oi ON o.id = oi.order_id AND o.tenant_id = oi.tenant_id
        LEFT JOIN menu_items mi ON oi.item_id = mi.id
        LEFT JOIN menu_item_variants miv ON oi.variant_id = miv.id AND oi.item_id = miv.item_id

        -- İndirim toplamları alt sorgusu
        LEFT JOIN (
            SELECT
                od.order_id,
                SUM(
                    CASE
                        WHEN od.discount_type = 'percentage' THEN
                            (SELECT SUM(oi2.price * oi2.quantity)
                             FROM order_items oi2
                             WHERE oi2.order_id = od.order_id
                             AND oi2.tenant_id = od.tenant_id
                             AND oi2.status NOT IN ('cancelled', 'waste', 'complimentary')) * (od.discount_value / 100)
                        WHEN od.discount_type = 'amount' THEN od.discount_value
                        ELSE 0
                    END
                ) as total_discount
            FROM order_discounts od
            GROUP BY od.order_id
        ) discount_summary ON o.id = discount_summary.order_id

        -- Ödeme toplamları alt sorgusu
        LEFT JOIN (
            SELECT
                pt.order_id,
                SUM(
                    CASE
                        WHEN pt.transaction_type = 'payment' AND pt.status = 'completed' THEN pt.amount
                        WHEN pt.transaction_type = 'refund' AND pt.status = 'completed' THEN -pt.amount
                        ELSE 0
                    END
                ) as total_paid
            FROM payment_transactions pt
            WHERE pt.status = 'completed'
            GROUP BY pt.order_id
        ) pt_summary ON o.id = pt_summary.order_id

        WHERE
            o.tenant_id = ? AND
            o.status NOT IN ('cancelled') AND
            o.payment_status NOT IN ('credit') AND
            oi.status NOT IN ('cancelled', 'waste', 'complimentary') AND
            ${filter}

        GROUP BY o.id, o.date, o.delivery_type, o.table_id, st.table_title,
                 o.customer_id, c.name, c.phone, o.payment_status, o.status,
                 o.username, u.name, pt_summary.total_paid, discount_summary.total_discount

        -- Sadece ödenmemiş veya eksik ödenmiş siparişler (net tutar bazında)
        HAVING remaining_debt > 0

        ORDER BY o.date DESC, remaining_debt DESC
        `;

        const [result] = await conn.query(sql, [tenantId, ...params]);

        return result;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

// Tarih filtre fonksiyonu (reports.service.js'den kopyalandı)
const getFilterCondition = (field, type, from, to) => {
    const params = [];
    let filter = '';

    switch (type) {
        case 'custom': {
            const fromDate = from ? new Date(from) : null;
            const toDate = to ? new Date(to) : null;

            if (fromDate && toDate) {
                const fromISO = fromDate.toISOString().slice(0, 19).replace('T', ' ');
                const toISO = toDate.toISOString().slice(0, 19).replace('T', ' ');

                params.push(fromISO, toISO);
                filter = `${field} >= ? AND ${field} <= ?`;
            }
            break;
        }
        case 'today': {
            filter = `CONVERT_TZ(${field}, '+00:00', '+03:00') >=
                     CONCAT(CURDATE(), ' 08:00:00') AND
                     CONVERT_TZ(${field}, '+00:00', '+03:00') <
                     CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 08:00:00')`;
            break;
        }
        case 'yesterday': {
            filter = `CONVERT_TZ(${field}, '+00:00', '+03:00') >=
                     CONCAT(DATE_SUB(CURDATE(), INTERVAL 1 DAY), ' 08:00:00') AND
                     CONVERT_TZ(${field}, '+00:00', '+03:00') <
                     CONCAT(CURDATE(), ' 08:00:00')`;
            break;
        }
        default: {
            filter = '';
        }
    }

    return { params, filter };
};

// Belirli bir siparişin ödeme detaylarını getir
exports.getOrderPaymentDetailsDB = async (orderId, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {
        // Sipariş bilgileri
        const orderSql = `
            SELECT
                o.*,
                st.table_title,
                c.name as customer_name,
                c.phone as customer_phone,
                u.name as created_by_name
            FROM orders o
            LEFT JOIN store_tables st ON o.table_id = st.id
            LEFT JOIN customers c ON o.customer_id = c.phone AND o.tenant_id = c.tenant_id
            LEFT JOIN users u ON o.username = u.username AND o.tenant_id = u.tenant_id
            WHERE o.id = ? AND o.tenant_id = ?
        `;

        // Sipariş öğeleri
        const itemsSql = `
            SELECT
                oi.*,
                mi.title as item_title,
                miv.title as variant_title
            FROM order_items oi
            LEFT JOIN menu_items mi ON oi.item_id = mi.id
            LEFT JOIN menu_item_variants miv ON oi.variant_id = miv.id AND oi.item_id = miv.item_id
            WHERE oi.order_id = ? AND oi.tenant_id = ?
            ORDER BY oi.id
        `;

        // Ödeme işlemleri
        const paymentsSql = `
            SELECT
                pt.*,
                ptype.title as payment_type_title,
                u.name as created_by_name
            FROM payment_transactions pt
            LEFT JOIN payment_types ptype ON pt.payment_type_id = ptype.id
            LEFT JOIN users u ON pt.created_by = u.username AND u.tenant_id = ?
            WHERE pt.order_id = ?
            ORDER BY pt.created_at DESC
        `;

        const [orderResult] = await conn.query(orderSql, [orderId, tenantId]);
        const [itemsResult] = await conn.query(itemsSql, [orderId, tenantId]);
        const [paymentsResult] = await conn.query(paymentsSql, [tenantId, orderId]);

        if (orderResult.length === 0) {
            return null;
        }

        // Toplamları hesapla
        const orderTotal = itemsResult
            .filter(item => !['cancelled', 'waste', 'complimentary'].includes(item.status))
            .reduce((sum, item) => sum + (item.price * item.quantity), 0);

        const totalPaid = paymentsResult
            .filter(payment => payment.status === 'completed')
            .reduce((sum, payment) => {
                if (payment.transaction_type === 'payment') {
                    return sum + parseFloat(payment.amount);
                } else if (payment.transaction_type === 'refund') {
                    return sum - parseFloat(payment.amount);
                }
                return sum;
            }, 0);

        const remainingDebt = orderTotal - totalPaid;

        return {
            order: orderResult[0],
            items: itemsResult,
            payments: paymentsResult,
            summary: {
                order_total: orderTotal,
                total_paid: totalPaid,
                remaining_debt: remainingDebt
            }
        };

    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};
