// services/mobile.service.js
const { getMySqlPromiseConnection } = require("../config/mysql.db");

exports.getTenantInfoDB = async (tenantId) => {
    try {
        const connection = await getMySqlPromiseConnection();
        const query = `
            SELECT 
                t.*,
                s.store_name as storeName,
                s.address as storeAddress,
                s.logo_url as storeLogo,
                s.currency,
                s.language,
                s.timezone as timeZone
            FROM tenants t
            LEFT JOIN store_settings s ON t.id = s.tenant_id
            WHERE t.id = ? AND t.status = 'active'
        `;
        const [results] = await connection.execute(query, [tenantId]);
        
        if (results.length > 0) {
            const tenant = results[0];
            return {
                ...tenant,
                settings: {
                    storeName: tenant.storeName,
                    storeAddress: tenant.storeAddress,
                    storeLogo: tenant.storeLogo,
                    currency: tenant.currency,
                    language: tenant.language,
                    timeZone: tenant.timeZone
                }
            };
        }
        return null;
    } catch (error) {
        console.error('Database error in getTenantInfoDB:', error);
        throw error;
    }
};

exports.getAllTenantsDB = async () => {
    try {
        const connection = await getMySqlPromiseConnection();
        const query = `
            SELECT 
                t.*,
                s.store_name as storeName,
                s.address as storeAddress,
                s.logo_url as storeLogo,
                s.currency,
                s.language,
                s.timezone as timeZone
            FROM tenants t
            LEFT JOIN store_settings s ON t.id = s.tenant_id
            WHERE t.status = 'active'
        `;
        const [results] = await connection.execute(query);
        
        return results.map(tenant => ({
            ...tenant,
            settings: {
                storeName: tenant.storeName,
                storeAddress: tenant.storeAddress,
                storeLogo: tenant.storeLogo,
                currency: tenant.currency,
                language: tenant.language,
                timeZone: tenant.timeZone
            }
        }));
    } catch (error) {
        console.error('Database error in getAllTenantsDB:', error);
        throw error;
    }
};

exports.getMenuItemsDB = async (tenantId) => {
    try {
        const connection = await getMySqlPromiseConnection();
        
        // Menü öğelerini al
        const menuQuery = `
            SELECT * FROM menu_items
            WHERE tenant_id = ? AND is_active = true
            ORDER BY category_id, name
        `;
        const [menuItems] = await connection.execute(menuQuery, [tenantId]);

        // Addon'ları al
        const addonQuery = `
            SELECT a.* 
            FROM addons a
            INNER JOIN menu_items mi ON a.menu_item_id = mi.id
            WHERE mi.tenant_id = ?
        `;
        const [addons] = await connection.execute(addonQuery, [tenantId]);

        // Varyantları al
        const variantQuery = `
            SELECT v.* 
            FROM variants v
            INNER JOIN menu_items mi ON v.menu_item_id = mi.id
            WHERE mi.tenant_id = ?
        `;
        const [variants] = await connection.execute(variantQuery, [tenantId]);

        // Menü öğeleriyle addon ve varyantları birleştir
        const menuWithAddonsAndVariants = menuItems.map(menuItem => ({
            ...menuItem,
            addons: addons.filter(addon => addon.menu_item_id === menuItem.id),
            variants: variants.filter(variant => variant.menu_item_id === menuItem.id)
        }));

        return menuWithAddonsAndVariants;
    } catch (error) {
        console.error('Database error in getMenuItemsDB:', error);
        throw error;
    }
};

exports.getCategoriesDB = async (tenantId) => {
    try {
        const connection = await getMySqlPromiseConnection();
        const query = `
            SELECT * FROM categories
            WHERE tenant_id = ? AND is_active = true
            ORDER BY sort_order
        `;
        const [results] = await connection.execute(query, [tenantId]);
        return results;
    } catch (error) {
        console.error('Database error in getCategoriesDB:', error);
        throw error;
    }
};

exports.getTablesDB = async (tenantId) => {
    try {
        const connection = await getMySqlPromiseConnection();
        const query = `
            SELECT * FROM tables
            WHERE tenant_id = ?
            ORDER BY table_number
        `;
        const [results] = await connection.execute(query, [tenantId]);
        return results;
    } catch (error) {
        console.error('Database error in getTablesDB:', error);
        throw error;
    }
};

exports.updateTableStatusDB = async (tenantId, tableId, status) => {
    try {
        const connection = await getMySqlPromiseConnection();
        const query = `
            UPDATE tables 
            SET status = ?, updated_at = NOW()
            WHERE tenant_id = ? AND id = ?
        `;
        await connection.execute(query, [status, tenantId, tableId]);
        
        const selectQuery = `
            SELECT * FROM tables
            WHERE id = ? AND tenant_id = ?
        `;
        const [results] = await connection.execute(selectQuery, [tableId, tenantId]);
        return results[0];
    } catch (error) {
        console.error('Database error in updateTableStatusDB:', error);
        throw error;
    }
};

exports.createOrderDB = async (tenantId, orderData) => {
    const connection = await getMySqlPromiseConnection();
    try {
        await connection.beginTransaction();

        // Ana sipariş kaydını oluştur
        const orderQuery = `
            INSERT INTO orders (
                tenant_id, table_id, customer_type,
                customer_name, customer_phone, total_amount,
                status, created_at, updated_at
            )
            VALUES (?, ?, ?, ?, ?, ?, 'new', NOW(), NOW())
        `;
        const [orderResult] = await connection.execute(orderQuery, [
            tenantId,
            orderData.tableId,
            orderData.customerType,
            orderData.customerName,
            orderData.customerPhone,
            orderData.total
        ]);
        const orderId = orderResult.insertId;

        // Sipariş öğelerini kaydet
        for (const item of orderData.items) {
            const itemQuery = `
                INSERT INTO order_items (
                    order_id, menu_item_id, quantity,
                    variant_id, notes, created_at
                )
                VALUES (?, ?, ?, ?, ?, NOW())
            `;
            const [itemResult] = await connection.execute(itemQuery, [
                orderId,
                item.menuItemId,
                item.quantity,
                item.variantId || null,
                item.notes || null
            ]);

            // Addon'ları kaydet
            if (item.addons?.length > 0) {
                const addonQuery = `
                    INSERT INTO order_item_addons (
                        order_id, order_item_id, addon_id
                    )
                    VALUES (?, ?, ?)
                `;
                for (const addonId of item.addons) {
                    await connection.execute(addonQuery, [
                        orderId,
                        itemResult.insertId,
                        addonId
                    ]);
                }
            }
        }

        await connection.commit();

        // Oluşturulan siparişi getir
        const selectQuery = `
            SELECT * FROM orders WHERE id = ?
        `;
        const [orderDetails] = await connection.execute(selectQuery, [orderId]);
        return orderDetails[0];

    } catch (error) {
        await connection.rollback();
        console.error('Database error in createOrderDB:', error);
        throw error;
    }
};