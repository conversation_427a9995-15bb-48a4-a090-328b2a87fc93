const { getMySqlPromiseConnection } = require("../config/mysql.db")

const axios = require("axios");
const fs = require("fs");
const path = require("path");
const sharp = require("sharp"); // Resmi WEBP formatına dönüştürmek için

// SOCKS5 proxy desteği için
let SocksProxyAgent = null;
try {
    const { SocksProxyAgent: SocksAgent } = require('socks-proxy-agent');
    SocksProxyAgent = SocksAgent;
    console.log('✅ SOCKS5 proxy desteği yüklendi');
} catch (error) {
    console.log('⚠️  SOCKS5 proxy desteği yok, HTTP proxy kullanılacak');
}

// Sadece Axios kullanılacak

// Proxy bağlantı testi
exports.testProxyConnection = async () => {
    const proxyConfig = {
        host: '**************',
        port: 80,
        auth: {
            username: 'se<PERSON><PERSON><PERSON>',
            password: 'Premire07o7'
        }
    };

    try {
        console.log('🧪 Proxy bağlantı testi başlıyor...');
        console.log(`Proxy: ${proxyConfig.host}:${proxyConfig.port}`);

        const response = await axios({
            url: 'https://httpbin.org/ip',
            method: 'GET',
            proxy: proxyConfig,
            timeout: 10000
        });

        console.log('✅ Proxy test başarılı!');
        console.log('🌐 Proxy IP adresi:', response.data.origin);
        return { success: true, ip: response.data.origin };

    } catch (error) {
        console.error('❌ Proxy test başarısız:', error.message);
        return { success: false, error: error.message };
    }
};


exports.downloadAndUpdateImage = async (imageUrl, tenantId, menuItemId) => {
    try {
        const imageDir = path.join(__dirname, `../../public/${tenantId}/`);
        const imageName = `${menuItemId}.webp`; // Resim adı WEBP formatında
        const imageFilePath = path.join(imageDir, imageName);

        // Klasör yoksa oluştur
        if (!fs.existsSync(imageDir)) {
            fs.mkdirSync(imageDir, { recursive: true });
        }

        // Sadece Axios ile resim indir
        console.log(`📥 Resim indirme başlıyor: ${imageUrl}`);
        const response = await downloadWithAxios(imageUrl);
        console.log(`🎉 Resim indirme tamamlandı: ${imageUrl}`);

        // Resmi indir ve WEBP formatına dönüştür
        await sharp(response.data)
            .webp({ quality: 80 }) // WEBP formatına dönüştürme, kalite %80
            .toFile(imageFilePath);

        return `/public/${tenantId}/${imageName}`; // WEBP formatında yeni resim yolu
    } catch (error) {
        // Hata türüne göre farklı mesajlar
        if (error.response) {
            // HTTP hata kodları
            const status = error.response.status;
            const statusText = error.response.statusText;

            if (status === 403) {
                console.error(`Resim erişim engellendi (403 Forbidden) - ${imageUrl}: Cloudflare veya güvenlik duvarı tarafından engellenmiş olabilir`);
            } else if (status === 404) {
                console.error(`Resim bulunamadı (404 Not Found) - ${imageUrl}`);
            } else if (status === 429) {
                console.error(`Çok fazla istek (429 Too Many Requests) - ${imageUrl}: Rate limit aşıldı`);
            } else {
                console.error(`HTTP Hatası ${status} ${statusText} - ${imageUrl}`);
            }
        } else if (error.request) {
            // Network hatası
            console.error(`Ağ hatası - ${imageUrl}:`, error.message);
        } else if (error.code === 'ECONNABORTED') {
            // Timeout hatası
            console.error(`Zaman aşımı hatası - ${imageUrl}: İstek 30 saniyede tamamlanamadı`);
        } else {
            // Diğer hatalar (Sharp, dosya sistemi vb.)
            console.error(`Resim işleme hatası - ${imageUrl}:`, error.message);
        }

        return null; // Hata durumunda null döndür
    }
};



// Axios ile resim indirme (proxy ile)
async function downloadWithAxios(imageUrl) {
    const proxyHost = '**************';
    const proxyPort = 80;
    const proxyUsername = 'selimweez';
    const proxyPassword = 'Premire07o7';

    console.log('🔗 Proxy bağlantısı deneniyor...');
    console.log(`Proxy: ${proxyHost}:${proxyPort} (${proxyUsername})`);
    console.log(`URL: ${imageUrl}`);

    // Önce SOCKS5 dene
    if (SocksProxyAgent) {
        console.log('🧦 SOCKS5 proxy deneniyor...');
        try {
            const socksProxyUrl = `socks5://${proxyUsername}:${proxyPassword}@${proxyHost}:${proxyPort}`;
            const socksAgent = new SocksProxyAgent(socksProxyUrl);

            const response = await axios({
                url: imageUrl,
                method: "GET",
                responseType: "arraybuffer",
                timeout: 30000,
                httpsAgent: socksAgent,
                httpAgent: socksAgent,
                headers: {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    'Accept': 'image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
                    'Accept-Language': 'en-US,en;q=0.9,tr;q=0.8',
                    'Referer': 'https://google.com/'
                },
                maxRedirects: 5,
                validateStatus: function (status) {
                    return status >= 200 && status < 300;
                }
            });

            console.log('✅ SOCKS5 proxy bağlantısı başarılı!');
            console.log(`Response status: ${response.status}`);
            console.log(`Content-Length: ${response.headers['content-length'] || 'unknown'}`);
            return response;

        } catch (socksError) {
            console.error('❌ SOCKS5 proxy hatası:', socksError.message);
            console.log('🔄 HTTP proxy deneniyor...');
        }
    }

    // SOCKS5 başarısızsa veya yoksa HTTP proxy dene
    try {
        // HTTPS URL'leri HTTP'ye çevir (HTTP proxy için)
        let processedUrl = imageUrl;
        if (imageUrl.startsWith('https://')) {
            processedUrl = imageUrl.replace('https://', 'http://');
            console.log(`🔄 HTTPS -> HTTP dönüştürüldü: ${processedUrl}`);
        }

        const proxyConfig = {
            host: proxyHost,
            port: proxyPort,
            auth: {
                username: proxyUsername,
                password: proxyPassword
            }
        };

        const response = await axios({
            url: processedUrl,
            method: "GET",
            responseType: "arraybuffer",
            timeout: 30000,
            proxy: proxyConfig,
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.9,tr;q=0.8',
                'Referer': 'https://google.com/'
            },
            maxRedirects: 5,
            validateStatus: function (status) {
                return status >= 200 && status < 300;
            }
        });

        console.log('✅ HTTP proxy bağlantısı başarılı!');
        console.log(`Response status: ${response.status}`);
        console.log(`Content-Length: ${response.headers['content-length'] || 'unknown'}`);
        return response;

    } catch (error) {
        console.error('❌ Proxy bağlantı hatası:', error.message);

        if (error.code === 'ECONNREFUSED') {
            console.log('💡 Proxy sunucusu erişilemez - Sunucu kapalı olabilir');
        } else if (error.code === 'ENOTFOUND') {
            console.log('💡 Proxy sunucusu bulunamadı - DNS hatası');
        } else if (error.response?.status === 407) {
            console.log('💡 Proxy authentication hatası - Kullanıcı adı/şifre yanlış');
        } else if (error.code === 'ETIMEDOUT' || error.code === 'ECONNABORTED') {
            console.log('💡 Proxy bağlantı zaman aşımı - HTTPS desteği olmayabilir');

            // HTTPS timeout durumunda proxy olmadan dene
            console.log('🔄 Proxy olmadan deneniyor...');
            try {
                const fallbackResponse = await axios({
                    url: imageUrl, // Orijinal URL'i kullan
                    method: "GET",
                    responseType: "arraybuffer",
                    timeout: 30000,
                    // proxy yok
                    headers: {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                        'Accept': 'image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
                        'Accept-Language': 'en-US,en;q=0.9,tr;q=0.8',
                        'Referer': 'https://google.com/'
                    },
                    maxRedirects: 5,
                    validateStatus: function (status) {
                        return status >= 200 && status < 300;
                    }
                });

                console.log('✅ Proxy olmadan başarılı!');
                console.log(`Response status: ${fallbackResponse.status}`);
                return fallbackResponse;

            } catch (fallbackError) {
                console.error('❌ Proxy olmadan da başarısız:', fallbackError.message);
                throw error; // Orijinal hatayı fırlat
            }
        } else if (error.response?.status) {
            console.log(`💡 HTTP hatası: ${error.response.status} ${error.response.statusText}`);
        }

        throw error;
    }
}

exports.updateMenuItemsBulkDB = async (menuItems, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {
        const updatePromises = menuItems.map(async (item) => {
            const {
                id,
                title,
                description,
                price,
                netPrice,
                categoryId,
                taxID,
                stock,
                isStockActive,
                imageUrl,
            } = item;

            try {

                // Resim işlemleri
                let imagePath = null;
                if (imageUrl) {
                    // URL mu yoksa local path mi kontrol et
                    if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
                        // Internet URL'i - proxy ile indir
                        console.log(`🌐 Internet URL'i tespit edildi: ${imageUrl}`);
                        imagePath = await exports.downloadAndUpdateImage(imageUrl, tenantId, id);
                    } else {
                        // Local dosya yolu - kopyala
                        console.log(`📁 Local dosya yolu tespit edildi: ${imageUrl}`);
                        imagePath = await exports.copyLocalImageAndUpdate(imageUrl, tenantId, id);
                    }
                }

                // Güncelleme SQL
                const sql = `
                UPDATE menu_items SET
                title = ?, description = ?, price = ?, net_price = ?, tax_id = ?, category = ?,
                stock = ?, is_stock_active = ?, image = COALESCE(?, image)
                WHERE id = ? AND tenant_id = ?
                `;
                return conn.query(sql, [
                    title,
                    description || null,
                    price,
                    netPrice || null,
                    taxID || null,
                    categoryId,
                    stock || 0,
                    isStockActive || 0,
                    imagePath, // Eğer yeni bir resim URL'si yoksa mevcut resmi koruyacak
                    id,
                    tenantId,
                ]);
            } catch (innerError) {
                console.error(`Ürün güncellenirken hata oluştu (ID: ${item.id}):`, innerError);
                return null; // Hata durumunda bu öğeyi atla
            }
        });

        await Promise.all(updatePromises);

        return { success: true };
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.addMenuItemDB = async (title, description, price, netPrice, taxId, categoryId, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {

        const sql = `
        INSERT INTO menu_items
        (title, description, price, net_price, tax_id, category, tenant_id)
        VALUES
        (?, ?, ?, ?, ?, ?, ?);
        `;

        const [result] = await conn.query(sql, [title, description, price, netPrice, taxId, categoryId, tenantId]);

        return result.insertId;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
}

exports.updateMenuItemDB = async (
    id, title, description, price, netPrice, taxId, categoryId, tenantId, allergens
  ) => {
    const conn = await getMySqlPromiseConnection();
    try {
      const sql = `
        UPDATE menu_items SET
          title = ?, 
          description = ?, 
          price = ?, 
          net_price = ?, 
          tax_id = ?, 
          category = ?, 
          allergens = ?
        WHERE id = ? AND tenant_id = ?;
      `;
  
      await conn.query(sql, [
        title,
        description,
        price,
        netPrice,
        taxId,
        categoryId,
        JSON.stringify(allergens || []),
        id,
        tenantId
      ]);
  
      return;
    } catch (error) {
      console.error(error);
      throw error;
    } finally {
      conn.release();
    }
  };
  

exports.updateMenuItemImageDB = async (id, image, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {

        const sql = `
        UPDATE menu_items SET
        image = ?
        WHERE id = ? AND tenant_id = ?;
        `;

        await conn.query(sql, [image, id, tenantId]);

        return;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
}

exports.deleteMenuItemDB = async (id, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {

        const sql = `
        DELETE FROM menu_items
        WHERE id = ? AND tenant_id = ?;
        `;

        await conn.query(sql, [id, tenantId]);

        return;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
}

exports.changeMenuItemVisibilityDB = async (id, isEnabled, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {

        const sql = `
         UPDATE menu_items SET
         is_enabled = ?
         WHERE id = ? AND tenant_id = ?;
        `;

        await conn.query(sql, [isEnabled, id, tenantId]);

        return;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
}

exports.changeMenuItemQRVisibilityDB = async (id, isVisibleInQrMenu, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {

        const sql = `
         UPDATE menu_items SET
         is_visible_in_qr_menu = ?
         WHERE id = ? AND tenant_id = ?;
        `;

        await conn.query(sql, [isVisibleInQrMenu, id, tenantId]);

        return;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
}

exports.getAllMenuItemsDB = async (tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {

        const sql = `
        SELECT
        i.id, i.title, i.description, price, net_price, tax_id, allergens, t.title AS tax_title, t.rate AS tax_rate, t.type AS tax_type, category as category_id, c.title AS category_title, image, i.is_enabled, i.is_visible_in_qr_menu, i.sort_order
        FROM menu_items i
        LEFT JOIN taxes t
        ON i.tax_id = t.id
        LEFT JOIN categories c
        ON i.category = c.id
        WHERE i.tenant_id = ?
        ORDER BY i.sort_order ASC, i.id ASC;
        `;

        const [result] = await conn.query(sql, [tenantId]);
        return result;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
}


exports.getMenuItemDB = async (id, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {

        const sql = `
        SELECT
        i.id, i.title, i.description, price, net_price, tax_id, t.title AS tax_title, t.rate AS tax_rate, t.type AS tax_type, category as category_id, c.title AS category_title, image, i.is_enabled, i.is_visible_in_qr_menu, i.sort_order
        FROM menu_items i
        LEFT JOIN taxes t
        ON i.tax_id = t.id
        LEFT JOIN categories c
        ON i.category = c.id
        WHERE i.id = ? AND i.tenant_id = ?
        `;

        const [result] = await conn.query(sql, [id, tenantId]);
        return result[0];
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
}


/**
 * @param {number} itemId Menu Item ID to add Addon
 * @param {string} title Title of Addon
 * @param {number} price Additonal Price for addon, Put 0 / null to make addon as free option
 * @returns {Promise<number>}
 *  */
exports.addMenuItemAddonDB = async (itemId, title, price, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {

        const sql = `
        INSERT INTO menu_item_addons
        (item_id, title, price, tenant_id)
        VALUES
        (?, ?, ?, ?);
        `;

        const [result] = await conn.query(sql, [itemId, title, price, tenantId]);
        return result.insertId;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.updateMenuItemAddonDB = async (itemId, addonId, title, price, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {

        const sql = `
        UPDATE menu_item_addons
        SET
        title = ?, price = ?
        WHERE id = ? AND item_id = ? AND tenant_id = ?
        `;

        await conn.query(sql, [title, price, addonId, itemId, tenantId]);
        return;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.deleteMenuItemAddonDB = async (itemId, addonId, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {

        const sql = `
        DELETE FROM menu_item_addons
        WHERE id = ? AND item_id = ? AND tenant_id = ?;
        `;

        await conn.query(sql, [addonId, itemId, tenantId]);

        return;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.getMenuItemAddonsDB = async (itemId, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {

        const sql = `
        SELECT id, item_id, title, price FROM menu_item_addons
        WHERE item_id = ? AND tenant_id = ?;
        `;

        const [result] = await conn.query(sql, [itemId, tenantId]);

        return result;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.getAllAddonsDB = async (tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {

        const sql = `
        SELECT id, item_id, title, price FROM menu_item_addons
        WHERE tenant_id = ?;
        `;

        const [result] = await conn.query(sql, [tenantId]);

        return result;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.addMenuItemVariantDB = async (itemId, title, price, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {

        const sql = `
        INSERT INTO menu_item_variants
        (item_id, title, price, tenant_id)
        VALUES
        (?, ?, ?, ?);
        `;

        const [result] = await conn.query(sql, [itemId, title, price, tenantId]);

        return result.insertId;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.updateMenuItemVariantDB = async (itemId, variantId, title, price, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {

        const sql = `
        UPDATE menu_item_variants
        SET
        title = ?, price = ?
        WHERE item_id = ? AND id = ? AND tenant_id = ?
        `;

        await conn.query(sql, [title, price, itemId, variantId, tenantId]);
        return;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.deleteMenuItemVariantDB = async (itemId, variantId, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {

        const sql = `
        DELETE FROM menu_item_variants
        WHERE item_id = ? AND id = ? AND tenant_id = ?
        `;

        await conn.query(sql, [itemId, variantId, tenantId]);

        return;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.getMenuItemVariantsDB = async (itemId, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {

        const sql = `
        SELECT id, item_id, title, price FROM menu_item_variants
        WHERE item_id = ? AND tenant_id = ?;
        `;

        const [result] = await conn.query(sql, [itemId, tenantId]);

        return result;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};
exports.getAllVariantsDB = async (tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {

        const sql = `
        SELECT id, item_id, title, price FROM menu_item_variants
        WHERE tenant_id = ?;
        `;

        const [result] = await conn.query(sql, [tenantId]);
        return result;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.updateMenuItemBulkDB = async (updateData, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {
        const updatePromises = [];
        const updatedItems = {};

        // Her bir menü öğesi için güncelleme işlemi oluştur
        for (const [itemId, itemData] of Object.entries(updateData)) {
            // Güncellenecek alanları ve değerlerini hazırla
            const updateFields = [];
            const updateValues = [];

            for (const [field, value] of Object.entries(itemData)) {
                // Alan adını veritabanı formatına dönüştür (örn: category_id -> category)
                let dbField = field;
                if (field === 'category_id') dbField = 'category';
                if (field === 'tax_id') dbField = 'tax_id';

                updateFields.push(`${dbField} = ?`);
                updateValues.push(value);
            }

            // Sorgu parametrelerini tamamla
            updateValues.push(itemId, tenantId);

            // SQL sorgusunu oluştur
            const sql = `
                UPDATE menu_items
                SET ${updateFields.join(', ')}
                WHERE id = ? AND tenant_id = ?
            `;

            // Güncelleme işlemini promises dizisine ekle
            updatePromises.push(
                conn.query(sql, updateValues)
                    .then(() => {
                        updatedItems[itemId] = itemData;
                        return { itemId, success: true };
                    })
                    .catch(err => {
                        console.error(`Error updating item ${itemId}:`, err);
                        return { itemId, success: false, error: err.message };
                    })
            );
        }

        // Tüm güncelleme işlemlerini paralel olarak çalıştır
        await Promise.all(updatePromises);

        return updatedItems;
    } catch (error) {
        console.error("Error in updateMenuItemBulkDB:", error);
        throw error;
    } finally {
        conn.release();
    }
};


exports.addRecipeItemDB = async (menuItemId, variantId, addonId, ingredientId, quantity, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {
        const sql = `
        INSERT INTO menu_item_recipes
        (menu_item_id, variant_id, addon_id, inventory_item_id, quantity, tenant_id)
        VALUES
        (?, ?, ?, ?, ?, ?);
        `;

        const [result] = await conn.query(sql, [menuItemId, variantId, addonId, ingredientId, quantity, tenantId]);

        return result.insertId;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.updateRecipeItemDB = async (recipeItemId, menuItemId, variantId, addonId, ingredientId, quantity, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {
        const sql = `
        UPDATE menu_item_recipes
        SET
          menu_item_id = ?,
          variant_id = ?,
          addon_id = ?,
          inventory_item_id = ?,
          quantity = ?
        WHERE id = ? AND tenant_id = ?;
      `;

        const [result] = await conn.query(sql, [
            menuItemId,
            variantId,
            addonId,
            ingredientId,
            quantity,
            recipeItemId,
            tenantId
        ]);

        return;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.getRecipeItemsDB = async (menuItemId, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {
        const sql = `
        SELECT
            mir.id,
            mir.menu_item_id,
            mir.variant_id,
            mir.addon_id,
            mir.inventory_item_id,
            mi.title AS menu_item_title,
            v.title AS variant_title,
            a.title AS addon_title,
            ii.title AS ingredient_title,
            ii.unit,
            mir.quantity
        FROM
            menu_item_recipes mir
        LEFT JOIN
            menu_items mi ON mir.menu_item_id = mi.id
        LEFT JOIN
            menu_item_variants v ON mir.variant_id = v.id
        LEFT JOIN
            menu_item_addons a ON mir.addon_id = a.id
        LEFT JOIN
            inventory_items ii ON mir.inventory_item_id = ii.id
        WHERE
            mir.menu_item_id = ? AND mir.tenant_id = ?
        `;

        const [rows] = await conn.query(sql, [menuItemId, tenantId]);
        return rows;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.getAllRecipeItemsDB = async (tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {
        const sql = `
        SELECT
            mir.id,
            mir.menu_item_id,
            mir.variant_id,
            mir.addon_id,
            mir.inventory_item_id,
            mi.title AS menu_item_title,
            v.title AS variant_title,
            a.title AS addon_title,
            ii.title AS ingredient_title,
            ii.unit,
            ii.quantity as current_quantity,
            ii.min_quantity_threshold,
            mir.quantity as recipe_quantity
        FROM
            menu_item_recipes mir
        LEFT JOIN
            menu_items mi ON mir.menu_item_id = mi.id
        LEFT JOIN
            menu_item_variants v ON mir.variant_id = v.id
        LEFT JOIN
            menu_item_addons a ON mir.addon_id = a.id
        LEFT JOIN
            inventory_items ii ON mir.inventory_item_id = ii.id
        WHERE
            mir.tenant_id = ?
        `;

        const [rows] = await conn.query(sql, [tenantId]);
        return rows;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.deleteRecipeItemDB = async (itemId, recipeItemId, variant, addon, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {
        let sql = `
        DELETE FROM menu_item_recipes
        WHERE menu_item_id = ?
        AND id = ?
        AND tenant_id = ?
      `;

        const params = [itemId, recipeItemId, tenantId];

        if (variant) {
            sql += ` AND variant_id = ?`;
            params.push(variant);
        }

        if (addon) {
            sql += ` AND addon_id = ?`;
            params.push(addon);
        }

        await conn.query(sql, params);
        return;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};



exports.getOrCreateCategoryId = async (categoryTitle, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {
        // Kategoriyi kontrol et
        const sqlCheck = `
        SELECT id FROM categories
        WHERE title = ? AND tenant_id = ?
        LIMIT 1
        `;
        const [rows] = await conn.query(sqlCheck, [categoryTitle, tenantId]);

        // Eğer kategori mevcutsa ID'yi döndür
        if (rows.length > 0) {
            return rows[0].id;
        }

        // Kategori mevcut değilse yeni bir kategori oluştur
        const sqlInsert = `
        INSERT INTO categories (title, tenant_id)
        VALUES (?, ?)
        `;
        const [result] = await conn.query(sqlInsert, [categoryTitle, tenantId]);

        // Yeni oluşturulan kategori ID'sini döndür
        return result.insertId;
    } catch (error) {
        console.error("Error in getOrCreateCategoryId:", error);
        throw error;
    } finally {
        conn.release();
    }
};
exports.addMenuBulkDB = async (menuItems, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {
        const bulkInsertValues = [];
        for (const item of menuItems) {
            const {
                title,
                description,
                price,
                netPrice,
                taxId,
                categoryTitle,
                stock,
                isStockActive,
            } = item;

            // Kategori başlığını ID'ye çevir (gerekirse oluştur)
            const categoryId = await exports.getOrCreateCategoryId(categoryTitle, tenantId);

            // Değerleri toplu eklemek için hazırlayın
            bulkInsertValues.push([
                title,
                description || null,
                price,
                netPrice || null,
                taxId || null,
                categoryId,
                stock || 0,
                isStockActive || 0,
                tenantId,
            ]);
        }

        // Toplu ekleme işlemi
        const sql = `
        INSERT INTO menu_items
        (title, description, price, net_price, tax_id, category, stock, is_stock_active, tenant_id)
        VALUES ?
        `;
        const [result] = await conn.query(sql, [bulkInsertValues]);

        return result;
    } catch (error) {
        console.error("Error in addMenuBulkDB:", error);
        throw error;
    } finally {
        conn.release();
    }
};



exports.updateCategoryOrderDB = async (categoryOrder, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {
      // Kategorilerin sıralamasını toplu olarak güncelle
      const updatePromises = categoryOrder.map((category) => {
        const sql = `
          UPDATE categories
          SET sort_order = ?
          WHERE id = ? AND tenant_id = ?;
        `;
        return conn.query(sql, [category.order, category.id, tenantId]);
      });

      await Promise.all(updatePromises);

      return { success: true, message: "Kategori sıralaması başarıyla güncellendi." };
    } catch (error) {
      console.error("Error in updateCategoryOrderDB:", error);
      throw error;
    } finally {
      conn.release();
    }
  };

exports.updateMenuItemOrderDB = async (menuItemOrder, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {
      // Menü öğelerinin sıralamasını toplu olarak güncelle
      const updatePromises = menuItemOrder.map((menuItem) => {
        const sql = `
          UPDATE menu_items
          SET sort_order = ?
          WHERE id = ? AND tenant_id = ?;
        `;
        return conn.query(sql, [menuItem.order, menuItem.id, tenantId]);
      });

      await Promise.all(updatePromises);

      return { success: true, message: "Menü öğeleri sıralaması başarıyla güncellendi." };
    } catch (error) {
      console.error("Error in updateMenuItemOrderDB:", error);
      throw error;
    } finally {
      conn.release();
    }
  };

// Yüzde bazlı toplu fiyat güncelleme
exports.updateMenuItemPricesByPercentageDB = async (options, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {
        const {
            percentage,
            itemIds = null,
            categoryIds = null,
            updateType = 'increase', // 'increase' veya 'decrease'
            includeAddons = false,
            includeVariants = false
        } = options;

        console.log(`🔄 Yüzde bazlı fiyat güncelleme başlıyor: ${updateType} %${percentage}`);

        let updatedItems = 0;
        let updatedAddons = 0;
        let updatedVariants = 0;

        // 1. Ana ürün fiyatlarını güncelle
        let whereClause = 'WHERE tenant_id = ?';
        let queryParams = [tenantId];

        // Seçili ürünler varsa
        if (itemIds && itemIds.length > 0) {
            whereClause += ` AND id IN (${itemIds.map(() => '?').join(',')})`;
            queryParams.push(...itemIds);
        }

        // Seçili kategoriler varsa
        if (categoryIds && categoryIds.length > 0) {
            whereClause += ` AND category IN (${categoryIds.map(() => '?').join(',')})`;
            queryParams.push(...categoryIds);
        }

        // Fiyat güncelleme formülü
        const priceFormula = updateType === 'increase'
            ? `price * (1 + ${percentage} / 100)`
            : `price * (1 - ${percentage} / 100)`;

        const netPriceFormula = updateType === 'increase'
            ? `COALESCE(net_price * (1 + ${percentage} / 100), NULL)`
            : `COALESCE(net_price * (1 - ${percentage} / 100), NULL)`;

        // Ana ürün fiyatları güncelleme
        const menuItemSql = `
            UPDATE menu_items
            SET
                price = ROUND(${priceFormula}, 2),
                net_price = ROUND(${netPriceFormula}, 2)
            ${whereClause}
        `;

        const [menuResult] = await conn.query(menuItemSql, queryParams);
        updatedItems = menuResult.affectedRows;

        console.log(`✅ ${updatedItems} ürün fiyatı güncellendi`);

        // 2. Addon fiyatlarını güncelle (istenirse)
        if (includeAddons) {
            let addonWhereClause = 'WHERE tenant_id = ?';
            let addonParams = [tenantId];

            if (itemIds && itemIds.length > 0) {
                addonWhereClause += ` AND item_id IN (${itemIds.map(() => '?').join(',')})`;
                addonParams.push(...itemIds);
            }

            const addonPriceFormula = updateType === 'increase'
                ? `price * (1 + ${percentage} / 100)`
                : `price * (1 - ${percentage} / 100)`;

            const addonSql = `
                UPDATE menu_item_addons
                SET price = ROUND(${addonPriceFormula}, 2)
                ${addonWhereClause}
            `;

            const [addonResult] = await conn.query(addonSql, addonParams);
            updatedAddons = addonResult.affectedRows;

            console.log(`✅ ${updatedAddons} addon fiyatı güncellendi`);
        }

        // 3. Variant fiyatlarını güncelle (istenirse)
        if (includeVariants) {
            let variantWhereClause = 'WHERE tenant_id = ?';
            let variantParams = [tenantId];

            if (itemIds && itemIds.length > 0) {
                variantWhereClause += ` AND item_id IN (${itemIds.map(() => '?').join(',')})`;
                variantParams.push(...itemIds);
            }

            const variantPriceFormula = updateType === 'increase'
                ? `price * (1 + ${percentage} / 100)`
                : `price * (1 - ${percentage} / 100)`;

            const variantSql = `
                UPDATE menu_item_variants
                SET price = ROUND(${variantPriceFormula}, 2)
                ${variantWhereClause}
            `;

            const [variantResult] = await conn.query(variantSql, variantParams);
            updatedVariants = variantResult.affectedRows;

            console.log(`✅ ${updatedVariants} variant fiyatı güncellendi`);
        }

        return {
            success: true,
            updatedItems,
            updatedAddons,
            updatedVariants,
            percentage,
            updateType
        };

    } catch (error) {
        console.error("Yüzde bazlı fiyat güncelleme hatası:", error);
        throw error;
    } finally {
        conn.release();
    }
};

// Fiyat güncelleme önizlemesi (gerçek güncelleme yapmadan sonuçları göster)
exports.previewPriceUpdateDB = async (options, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {
        const {
            percentage,
            itemIds = null,
            categoryIds = null,
            updateType = 'increase'
        } = options;

        let whereClause = 'WHERE tenant_id = ?';
        let queryParams = [tenantId];

        if (itemIds && itemIds.length > 0) {
            whereClause += ` AND id IN (${itemIds.map(() => '?').join(',')})`;
            queryParams.push(...itemIds);
        }

        if (categoryIds && categoryIds.length > 0) {
            whereClause += ` AND category IN (${categoryIds.map(() => '?').join(',')})`;
            queryParams.push(...categoryIds);
        }

        const priceFormula = updateType === 'increase'
            ? `price * (1 + ${percentage} / 100)`
            : `price * (1 - ${percentage} / 100)`;

        const sql = `
            SELECT
                id,
                title,
                price as current_price,
                ROUND(${priceFormula}, 2) as new_price,
                ROUND(${priceFormula} - price, 2) as price_difference
            FROM menu_items
            ${whereClause}
            ORDER BY title
        `;

        const [result] = await conn.query(sql, queryParams);
        return result;

    } catch (error) {
        console.error("Fiyat önizleme hatası:", error);
        throw error;
    } finally {
        conn.release();
    }
};

// Local dosyadan resim kopyalama
exports.copyLocalImageAndUpdate = async (localFilePath, tenantId, menuItemId) => {
    try {
        const imageDir = path.join(__dirname, `../../public/${tenantId}/`);
        const imageName = `${menuItemId}.webp`;
        const imageFilePath = path.join(imageDir, imageName);

        // Klasör yoksa oluştur
        if (!fs.existsSync(imageDir)) {
            fs.mkdirSync(imageDir, { recursive: true });
        }

        console.log(`📁 Local dosya kopyalanıyor: ${localFilePath}`);

        // Dosya var mı kontrol et
        if (!fs.existsSync(localFilePath)) {
            throw new Error(`Dosya bulunamadı: ${localFilePath}`);
        }

        // Dosya uzantısını kontrol et
        const allowedExtensions = ['.jpg', '.jpeg', '.png', '.webp', '.gif', '.bmp'];
        const fileExtension = path.extname(localFilePath).toLowerCase();
        if (!allowedExtensions.includes(fileExtension)) {
            throw new Error(`Desteklenmeyen dosya formatı: ${fileExtension}`);
        }

        // Dosyayı oku ve WEBP formatına dönüştür
        await sharp(localFilePath)
            .webp({ quality: 80 })
            .toFile(imageFilePath);

        console.log(`✅ Dosya başarıyla kopyalandı: ${imageName}`);
        return `/public/${tenantId}/${imageName}`;

    } catch (error) {
        console.error(`❌ Dosya kopyalama hatası: ${error.message}`);

        if (error.code === 'ENOENT') {
            console.log('💡 Dosya yolu kontrol edin - Dosya mevcut değil');
        } else if (error.code === 'EACCES') {
            console.log('💡 Dosya erişim hatası - İzin sorunu olabilir');
        }

        return null;
    }
};