const { getMySqlPromiseConnection } = require("../config/mysql.db");
const { savePrintDataMTSMPaymentDB } = require("./print_data.service");

/**
 * Webhook için siparişleri listele
 * TSM cihazının istediği formatta sipariş listesi döndürür
 */
exports.getOrdersForWebhookDB = async (tenantId, filters = {}) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const {
      pageNumber = 1,
      pageSize = 20,
      orderNo,
      status
    } = filters;

    const offset = (pageNumber - 1) * pageSize;
    let whereConditions = ['o.tenant_id = ?'];
    let queryParams = [tenantId];

    // Sadece pending durumundaki siparişleri göster (TSM için uygun olanlar)
    whereConditions.push("o.status = 'created' AND o.payment_status = 'pending'");

    // Sipariş numarası filtresi
    if (orderNo) {
      whereConditions.push('o.id = ?');
      queryParams.push(orderNo);
    }

    // Durum filtresi
    if (status) {
      whereConditions.push('o.status = ?');
      queryParams.push(status);
    }

    const whereClause = whereConditions.join(' AND ');

    // Toplam sayı sorgusu
    const countSql = `
      SELECT COUNT(*) as total
      FROM orders o
      WHERE ${whereClause}
    `;
    const [countResult] = await conn.query(countSql, queryParams);
    const totalCount = countResult[0].total;

    // Siparişleri getir
    const ordersSql = `
      SELECT
        o.id,
        o.date,
        o.delivery_type,
        o.customer_type,
        o.customer_id,
        c.name AS customer_name,
        o.table_id,
        st.table_title,
        st.floor,
        o.status,
        o.payment_status,
        o.token_no,
        o.username,
        u.name AS user_name
      FROM orders o
        LEFT JOIN customers c ON o.customer_id = c.phone AND c.tenant_id = o.tenant_id
        LEFT JOIN store_tables st ON o.table_id = st.id
        LEFT JOIN users u ON o.username = u.username AND u.tenant_id = o.tenant_id
      WHERE ${whereClause}
      ORDER BY o.date DESC
      LIMIT ? OFFSET ?
    `;

    queryParams.push(pageSize, offset);
    const [orders] = await conn.query(ordersSql, queryParams);

    // TSM formatına dönüştür
    const formattedOrders = [];

    for (let order of orders) {
      const totalAmount = await calculateOrderTotal(conn, order.id, tenantId);
      // Number'a çevir ve 2 ondalık basamağa yuvarla
      const amount = Number(totalAmount) || 0;
      const formattedAmount = Math.round(amount * 100) / 100;

      formattedOrders.push({
        id: order.id.toString(),
        name: `Sipariş #${order.id}`,
        no: order.id,
        status: mapStatusToTSM(order.status),
        totalAmount: formattedAmount,
        subtotalAmount: formattedAmount,
        createdAt: new Date(order.date).toISOString(),
        note: order.table_title ? `Masa: ${order.table_title}` : null
      });
    }

    return {
      items: formattedOrders,
      totalCount: totalCount,
      currentPage: pageNumber,
      pageSize: pageSize,
      totalPages: Math.ceil(totalCount / pageSize)
    };

  } catch (error) {
    console.error("getOrdersForWebhookDB error:", error);
    throw error;
  } finally {
    conn.release();
  }
};

/**
 * Webhook için sipariş detayını getir
 */
exports.getOrderDetailsForWebhookDB = async (orderId, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    // Sipariş bilgilerini getir
    const orderSql = `
      SELECT
        o.id,
        o.date,
        o.delivery_type,
        o.customer_type,
        o.customer_id,
        c.name AS customer_name,
        c.phone AS customer_phone,
        o.table_id,
        st.table_title,
        o.status,
        o.payment_status,
        o.token_no,
        o.username,
        u.name AS user_name,
        o.tenant_id
      FROM orders o
        LEFT JOIN customers c ON o.customer_id = c.phone AND c.tenant_id = o.tenant_id
        LEFT JOIN store_tables st ON o.table_id = st.id
        LEFT JOIN users u ON o.username = u.username AND u.tenant_id = o.tenant_id
      WHERE o.id = ? AND o.tenant_id = ?
    `;

    const [orderResult] = await conn.query(orderSql, [orderId, tenantId]);
    if (orderResult.length === 0) {
      return null;
    }

    const order = orderResult[0];

    // Sipariş kalemlerini getir (KDV bilgisi ile) - cancelled, complimentary, waste hariç
    const itemsSql = `
      SELECT
        oi.id,
        oi.item_id,
        mi.title AS item_name,
        mi.tax_id,
        t.rate AS tax_rate,
        oi.quantity,
        oi.price,
        oi.status,
        oi.notes
      FROM order_items oi
        LEFT JOIN menu_items mi ON oi.item_id = mi.id
        LEFT JOIN taxes t ON mi.tax_id = t.id AND t.tenant_id = oi.tenant_id
      WHERE oi.order_id = ? AND oi.tenant_id = ?
        AND oi.status NOT IN ('cancelled', 'complimentary', 'waste')
      ORDER BY oi.id
    `;

    const [items] = await conn.query(itemsSql, [orderId, order.tenant_id]);

    // Ürünlerin subtotal tutarını hesapla
    const [subtotalResult] = await conn.query(`
      SELECT SUM(oi.quantity * oi.price) as total
      FROM order_items oi
      WHERE oi.order_id = ? AND oi.tenant_id = ? AND oi.status NOT IN ('cancelled', 'complimentary', 'waste')
    `, [orderId, order.tenant_id]);
    const subtotalAmount = Number(subtotalResult[0].total) || 0;

    // İndirimleri getir ve hesapla
    const [discountsResult] = await conn.query(`
      SELECT
        SUM(
          CASE
            WHEN od.discount_type = 'percentage' THEN
              CASE
                WHEN od.order_item_id IS NULL THEN
                  (SELECT SUM(oi2.price * oi2.quantity) FROM order_items oi2
                   WHERE oi2.order_id = od.order_id AND oi2.status NOT IN ('cancelled')) * (od.discount_value / 100)
                ELSE
                  (SELECT oi2.price * oi2.quantity FROM order_items oi2 WHERE oi2.id = od.order_item_id) * (od.discount_value / 100)
              END
            WHEN od.discount_type = 'amount' THEN od.discount_value
            ELSE 0
          END
        ) as total_discount
      FROM order_discounts od
      WHERE od.order_id = ? AND od.tenant_id = ?
    `, [orderId, order.tenant_id]);

    const totalDiscountAmount = Number(discountsResult[0].total_discount) || 0;
    const totalAmount = subtotalAmount - totalDiscountAmount;

    const formattedSubtotal = Math.round(subtotalAmount * 100) / 100;
    const formattedDiscount = Math.round(totalDiscountAmount * 100) / 100;
    const formattedTotal = Math.round(totalAmount * 100) / 100;

    // TSM formatında döndür
    return {
      id: order.id.toString(),
      name: `Sipariş #${order.id}`,
      no: order.id,
      status: mapStatusToTSM(order.status),
      createdAt: new Date(order.date).toISOString(),
      items: items.map(item => {
        const unitPrice = Math.round((Number(item.price) || 0) * 100) / 100;

        // KDV oranını belirle: tax_rate varsa onu kullan, yoksa %20 default
        let vatRate = 20; // Default %20
        if (item.tax_rate !== null && item.tax_rate !== undefined) {
          vatRate = Number(item.tax_rate) || 20;
        }

        return {
          name: item.item_name || 'Ürün',
          unitPrice: unitPrice,
          vat: vatRate,
          quantity: item.quantity,
          discountRate: 0,
          discountAmount: 0
        };
      }),
      totalAmount: formattedTotal,
      subtotalAmount: formattedSubtotal,
      discountRate: 0,
      discountAmount: formattedDiscount,
      note: order.table_title ? `Masa: ${order.table_title}` : null
    };

  } catch (error) {
    console.error("getOrderDetailsForWebhookDB error:", error);
    throw error;
  } finally {
    conn.release();
  }
};

/**
 * Webhook için sipariş durumunu güncelle
 */
exports.updateOrderStatusForWebhookDB = async (orderId, tenantId, updateData) => {
  const conn = await getMySqlPromiseConnection();
  try {
    await conn.beginTransaction();

    const { status, payments, receipt, deviceDocument } = updateData;

    // Sipariş durumunu güncelle
    const newStatus = mapStatusFromTSM(status);
    await conn.query(
      'UPDATE orders SET status = ?, updated_at = NOW() WHERE id = ? AND tenant_id = ?',
      [newStatus, orderId, tenantId]
    );

    // Eğer ödeme bilgileri varsa kaydet (updateOrderStatus için - şimdilik skip)
    if (payments && payments.length > 0) {
      console.log('⚠️ updateOrderStatus içinde payment bilgileri var, ancak işlem yapılmıyor (payment webhook\'u kullanılıyor)');
    }

    // Eğer fiş bilgileri varsa kaydet (opsiyonel)
    if (receipt) {
      // Receipt bilgilerini kaydetmek için ek tablo gerekebilir
      console.log('Receipt bilgileri:', receipt);
    }

    await conn.commit();

    console.log(`✅ Sipariş durumu güncellendi - ID: ${orderId}, Status: ${status} -> ${newStatus}`);

    return {
      id: orderId,
      status: newStatus,
      updatedAt: new Date()
    };

  } catch (error) {
    await conn.rollback();
    console.error("updateOrderStatusForWebhookDB error:", error);
    throw error;
  } finally {
    conn.release();
  }
};

/**
 * Webhook için sipariş ödemesini güncelle ve siparişi tamamla
 * TSM'den ödeme geldiğinde sipariş durumunu completed, payment_status'u paid yap
 * Eğer masa siparişi ise masayı boşalt
 */
exports.updateOrderPaymentForWebhookDB = async (orderId, tenantId, payments, receipt = null, csn = null) => {
  const conn = await getMySqlPromiseConnection();
  try {
    await conn.beginTransaction();

    // Önce siparişi bul ve tenant_id'yi orders tablosundan çek
    const [orderResult] = await conn.query('SELECT * FROM orders WHERE id = ?', [orderId]);

    if (orderResult.length === 0) {
      throw new Error(`Sipariş bulunamadı - ID: ${orderId}`);
    }

    const order = orderResult[0];
    const actualTenantId = order.tenant_id; // orders tablosundan tenant_id'yi al



    // CSN'den cihazı, kasayı ve printer'ı bul
    let cashRegisterSessionId = null;
    let sessionOpenedBy = null;
    let printerPath = null;

    if (csn) {
      const [deviceResult] = await conn.query(`
        SELECT
          yd.cash_register_id,
          cr.name as cash_register_name,
          crs.id as session_id,
          crs.opened_by,
          yd.printer_id,
          p.path as printer_path,
          p.name as printer_name
        FROM yazarkasa_devices yd
        LEFT JOIN cash_registers cr ON yd.cash_register_id = cr.id
        LEFT JOIN cash_register_sessions crs ON cr.id = crs.cash_register_id
          AND crs.status = 'open' AND crs.tenant_id = ?
        LEFT JOIN printers p ON yd.printer_id = p.id
        WHERE yd.device_serial = ? AND yd.tenant_id = ? AND yd.is_active = 1
        LIMIT 1
      `, [actualTenantId, csn, actualTenantId]);

      if (deviceResult.length > 0) {
        if (deviceResult[0].cash_register_id) {
          cashRegisterSessionId = deviceResult[0].session_id;
          sessionOpenedBy = deviceResult[0].opened_by;

        }

        if (deviceResult[0].printer_path) {
          printerPath = deviceResult[0].printer_path;
          console.log(`🖨️ CSN ${csn} → Printer: ${deviceResult[0].printer_name}, Path: ${printerPath}`);
        }
      } else {
        console.log(`⚠️ CSN ${csn} için cihaz bulunamadı`);
      }
    }

    // 1. Ödemeleri kaydet
    for (const payment of payments) {
      // mTSM code'undan payment_type_id'yi bul
      const [paymentTypeResult] = await conn.query(`
        SELECT id, title FROM payment_types
        WHERE mtsm_code = ? AND tenant_id = ? AND is_active = 1
        LIMIT 1
      `, [payment.Type, actualTenantId]);

      let paymentTypeId;
      let paymentTypeName;

      if (paymentTypeResult.length > 0) {
        paymentTypeId = paymentTypeResult[0].id;
        paymentTypeName = paymentTypeResult[0].title;
      } else {
        // Fallback: Default nakit ödeme tipini bul
        const [defaultPaymentType] = await conn.query(`
          SELECT id, title FROM payment_types
          WHERE (isCash = 1 OR title LIKE '%nakit%')
          AND tenant_id = ? AND is_active = 1
          LIMIT 1
        `, [actualTenantId]);

        if (defaultPaymentType.length > 0) {
          paymentTypeId = defaultPaymentType[0].id;
          paymentTypeName = defaultPaymentType[0].title;
          console.log(`⚠️ mTSM code bulunamadı (${payment.Type}), default nakit kullanıldı`);
        } else {
          throw new Error(`Ödeme tipi bulunamadı - mTSM Code: ${payment.Type}, Tenant: ${actualTenantId}`);
        }
      }

      // created_by için: Kasa session'ı varsa session açan kullanıcı, yoksa admin
      let createdBy;
      if (sessionOpenedBy) {
        createdBy = sessionOpenedBy;
      } else {
        // Tenant'ın bir admin user'ını bul
        const [adminUser] = await conn.query(`
          SELECT username FROM users
          WHERE tenant_id = ? AND role = 'admin'
          LIMIT 1
        `, [actualTenantId]);
        createdBy = adminUser.length > 0 ? adminUser[0].username : 'system';
      }

      // Payment transaction'ı kaydet (kasa session'ı varsa dahil et)
      const insertSql = cashRegisterSessionId
        ? `INSERT INTO payment_transactions
           (order_id, payment_type_id, amount, transaction_type, created_by, tenant_id, cash_register_session_id, created_at)
           VALUES (?, ?, ?, 'payment', ?, ?, ?, NOW())`
        : `INSERT INTO payment_transactions
           (order_id, payment_type_id, amount, transaction_type, created_by, tenant_id, created_at)
           VALUES (?, ?, ?, 'payment', ?, ?, NOW())`;

      const insertParams = cashRegisterSessionId
        ? [orderId, paymentTypeId, payment.Amount, createdBy, actualTenantId, cashRegisterSessionId]
        : [orderId, paymentTypeId, payment.Amount, createdBy, actualTenantId];

      await conn.query(insertSql, insertParams);

      console.log(`💰 Ödeme kaydedildi - mTSM: ${payment.Type} → ${paymentTypeName} (ID: ${paymentTypeId}), Amount: ${payment.Amount}, Session: ${cashRegisterSessionId || 'N/A'}`);
    }

    // 2. Sipariş durumunu güncelle: completed ve paid
    await conn.query(`
      UPDATE orders
      SET status = 'completed',
          payment_status = 'paid'
      WHERE id = ?
    `, [orderId]);

    console.log(`✅ Sipariş durumu güncellendi - ID: ${orderId} -> completed/paid`);

   // 3. Eğer masa siparişi ise masayı kasiyer bekliyor durumuna al
if (order.table_id) {
  await conn.query(`
    UPDATE store_tables
    SET table_status = 'waiting_cashier'
    WHERE id = ? AND tenant_id = ?
  `, [order.table_id, actualTenantId]);

  console.log(`🪑 Masa kasiyer bekliyor - Table ID: ${order.table_id}`);
}


    // 4. Receipt bilgilerini logla (opsiyonel kayıt için)
    if (receipt) {
      console.log(`📄 Receipt bilgileri - No: ${receipt.No}, Z No: ${receipt.ZNo}, ERU No: ${receipt.EruNo}`);
    }

    await conn.commit();

    console.log(`🎉 mTSM Ödeme işlemi tamamlandı - Order: ${orderId}, CSN: ${csn}`);

    // mTSM Ödeme print data oluştur ve kaydet
    if (printerPath) {
      try {
        // Sipariş bilgilerini al (garson adı için)
        const [orderDetailResult] = await conn.query(`
          SELECT
            o.username,
            u.name as server_name,
            st.table_title
          FROM orders o
          LEFT JOIN users u ON o.username = u.username AND u.tenant_id = o.tenant_id
          LEFT JOIN store_tables st ON o.table_id = st.id
          WHERE o.id = ?
        `, [orderId]);

        const orderDetail = orderDetailResult[0] || {};

        const paymentData = {
          orderId: orderId,
          tableName: orderDetail.table_title || null,
          serverName: orderDetail.server_name || 'Sistem',
          payments: payments.map(p => {
            let paymentType = p.Type;
            if (p.Type === 'CashPayment') {
              paymentType = 'Nakit';
            } else if (p.Type === 'CreditCardPayment') {
              paymentType = 'Kart';
            }
            return {
              type: paymentType,
              amount: p.Amount
            };
          }),
          totalAmount: payments.reduce((total, p) => total + p.Amount, 0)
        };

        await savePrintDataMTSMPaymentDB(actualTenantId, paymentData, printerPath);
        console.log(`🖨️ mTSM ödeme print data kaydedildi - Printer: ${printerPath}`);

      } catch (printError) {
        console.error('mTSM print data hatası:', printError);
        // Print hatası ana işlemi etkilemesin
      }
    }

    // Frontend'e socket ile bildirim gönder
    try {
      if (global.io) {
        const socketPayload = {
          orderId: orderId,
          status: 'completed',
          paymentStatus: 'paid',
          tableId: order.table_id,
          tableName: order.table_id ? `Masa ${order.table_id}` : null,
          amount: payments.reduce((total, p) => total + p.Amount, 0),
          paymentType: 'mTSM',
          timestamp: new Date().toISOString(),
          message: 'mTSM ödemesi tamamlandı'
        };

        global.io.to(actualTenantId.toString()).emit("order_update", socketPayload);
        console.log(`📡 Socket bildirim gönderildi - Tenant: ${actualTenantId}, Order: ${orderId}`);
      }
    } catch (socketError) {
      console.error('Socket bildirim hatası:', socketError);
      // Socket hatası ana işlemi etkilemesin
    }

    return {
      id: orderId,
      status: 'completed',
      paymentStatus: 'paid',
      tableCleared: !!order.table_id,
      payments: payments,
      receipt: receipt,
      updatedAt: new Date()
    };

  } catch (error) {
    await conn.rollback();
    console.error("updateOrderPaymentForWebhookDB error:", error);
    throw error;
  } finally {
    conn.release();
  }
};

/**
 * Helper Functions
 */

// Sipariş toplam tutarını hesapla (indirimler dahil)
async function calculateOrderTotal(conn, orderId, tenantId) {
  try {
    // Ürünlerin toplam tutarını hesapla
    const [itemsResult] = await conn.query(`
      SELECT SUM(oi.quantity * oi.price) as total
      FROM order_items oi
      WHERE oi.order_id = ? AND oi.tenant_id = ? AND oi.status != 'cancelled'
    `, [orderId, tenantId]);

    const subtotal = itemsResult[0].total || 0;

    // İndirimleri hesapla
    const [discountsResult] = await conn.query(`
      SELECT
        SUM(
          CASE
            WHEN od.discount_type = 'percentage' THEN
              CASE
                WHEN od.order_item_id IS NULL THEN
                  (SELECT SUM(oi2.price * oi2.quantity) FROM order_items oi2
                   WHERE oi2.order_id = od.order_id AND oi2.status NOT IN ('cancelled')) * (od.discount_value / 100)
                ELSE
                  (SELECT oi2.price * oi2.quantity FROM order_items oi2 WHERE oi2.id = od.order_item_id) * (od.discount_value / 100)
              END
            WHEN od.discount_type = 'amount' THEN od.discount_value
            ELSE 0
          END
        ) as total_discount
      FROM order_discounts od
      WHERE od.order_id = ? AND od.tenant_id = ?
    `, [orderId, tenantId]);

    const totalDiscount = discountsResult[0].total_discount || 0;

    // Final toplam = subtotal - indirim
    return subtotal - totalDiscount;
  } catch (error) {
    console.error('calculateOrderTotal error:', error);
    return 0;
  }
}

// Sistem durumunu TSM durumuna çevir
function mapStatusToTSM(systemStatus) {
  const statusMap = {
    'pending': 'OPEN',
    'preparing': 'LOCKED',
    'ready': 'LOCKED',
    'completed': 'CLOSED',
    'cancelled': 'CANCELLED'
  };
  return statusMap[systemStatus] || 'OPEN';
}

// TSM durumunu sistem durumuna çevir
function mapStatusFromTSM(tsmStatus) {
  const statusMap = {
    'OPEN': 'pending',
    'LOCKED': 'preparing',
    'CLOSED': 'completed',
    'CANCELLED': 'cancelled'
  };
  return statusMap[tsmStatus] || 'pending';
}
