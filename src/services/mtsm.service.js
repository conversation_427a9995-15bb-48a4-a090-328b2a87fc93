const { getMySqlPromiseConnection } = require("../config/mysql.db");
const axios = require("axios");

// mTSM Ayarlarını Getir
exports.getMTSMSettingsDB = async (tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const sql = `
      SELECT 
        id, tenant_id, server_url, email, 
        access_token, refresh_token, token_expires_at,
        integration_type, is_active,
        webhook_order_list_url, webhook_order_detail_url, webhook_order_update_url,
        created_at, updated_at
      FROM mtsm_settings 
      WHERE tenant_id = ? AND is_active = 1
    `;
    const [result] = await conn.query(sql, [tenantId]);
    return result[0] || null;
  } catch (error) {
    console.error("getMTSMSettingsDB error:", error);
    throw error;
  } finally {
    conn.release();
  }
};

// mTSM Ayarlarını Kaydet/Güncelle
exports.saveMTSMSettingsDB = async (tenantId, settings) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const { 
      serverUrl, email, password, integrationType,
      webhookOrderListUrl, webhookOrderDetailUrl, webhookOrderUpdateUrl 
    } = settings;

    // Şifreyi dümdüz kaydet (basit ve etkili!)
    const sql = `
      INSERT INTO mtsm_settings
      (tenant_id, server_url, email, password, integration_type,
       webhook_order_list_url, webhook_order_detail_url, webhook_order_update_url)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      ON DUPLICATE KEY UPDATE
        server_url = VALUES(server_url),
        email = VALUES(email),
        password = VALUES(password),
        integration_type = VALUES(integration_type),
        webhook_order_list_url = VALUES(webhook_order_list_url),
        webhook_order_detail_url = VALUES(webhook_order_detail_url),
        webhook_order_update_url = VALUES(webhook_order_update_url),
        updated_at = CURRENT_TIMESTAMP
    `;

    await conn.query(sql, [
      tenantId, serverUrl, email, password, integrationType,
      webhookOrderListUrl, webhookOrderDetailUrl, webhookOrderUpdateUrl
    ]);

    return { success: true };
  } catch (error) {
    console.error("saveMTSMSettingsDB error:", error);
    throw error;
  } finally {
    conn.release();
  }
};

// mTSM Login ve Token Al
exports.loginToMTSM = async (tenantId) => {
  const conn = await getMySqlPromiseConnection();
  let settings = null;

  try {
    // Tenant ayarlarını al
    settings = await exports.getMTSMSettingsDB(tenantId);
    if (!settings) {
      throw new Error("mTSM ayarları bulunamadı");
    }

    const startTime = Date.now();

    // Şifreyi dümdüz database'den al
    const [passwordResult] = await conn.query(
      `SELECT password FROM mtsm_settings WHERE tenant_id = ?`,
      [tenantId]
    );

    if (!passwordResult.length) {
      throw new Error("Şifre bulunamadı");
    }

    // mTSM Login API çağrısı
    const loginData = {
      email: settings.email,
      password: passwordResult[0].password // Dümdüz şifre
    };

    const response = await axios.post(`${settings.server_url}/api/login`, loginData, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 30000
    });

    const { accessToken, token, refreshToken, expiresIn } = response.data;
    const executionTime = Date.now() - startTime;

    // Token'ları kaydet
    const expiresAt = new Date(Date.now() + (expiresIn * 1000));

    await conn.query(`
      UPDATE mtsm_settings
      SET access_token = ?, refresh_token = ?, token_expires_at = ?
      WHERE tenant_id = ?
    `, [accessToken || token, refreshToken, expiresAt, tenantId]);

    // Log kaydet
    await exports.logMTSMOperation(tenantId, 'LOGIN', loginData, response.data, 'SUCCESS', null, executionTime);

    return {
      success: true,
      accessToken: accessToken || token,
      refreshToken,
      expiresAt
    };

  } catch (error) {
    const executionTime = Date.now() - (error.startTime || Date.now());

    // Hata log'u kaydet
    await exports.logMTSMOperation(
      tenantId, 'LOGIN',
      { email: settings?.email },
      null, 'FAILED',
      error.message,
      executionTime
    );

    console.error("loginToMTSM error:", error);
    throw error;
  } finally {
    conn.release();
  }
};

// Token Geçerliliğini Kontrol Et
exports.isTokenValid = async (tenantId) => {
  const settings = await exports.getMTSMSettingsDB(tenantId);
  if (!settings || !settings.access_token || !settings.token_expires_at) {
    return false;
  }

  const now = new Date();
  const expiresAt = new Date(settings.token_expires_at);
  
  // 5 dakika öncesinden token'ı yenile
  return expiresAt > new Date(now.getTime() + 5 * 60 * 1000);
};

// Geçerli Token Al (gerekirse yenile)
exports.getValidToken = async (tenantId) => {
  const isValid = await exports.isTokenValid(tenantId);
  
  if (!isValid) {
    await exports.loginToMTSM(tenantId);
  }

  const settings = await exports.getMTSMSettingsDB(tenantId);
  return settings.access_token;
};

// mTSM Operation Log
exports.logMTSMOperation = async (tenantId, operationType, requestData, responseData, status, errorMessage, executionTime) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const sql = `
      INSERT INTO mtsm_sync_logs 
      (tenant_id, operation_type, request_data, response_data, status, error_message, execution_time_ms)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `;

    await conn.query(sql, [
      tenantId, operationType, 
      JSON.stringify(requestData), 
      JSON.stringify(responseData), 
      status, errorMessage, executionTime
    ]);

  } catch (error) {
    console.error("logMTSMOperation error:", error);
  } finally {
    conn.release();
  }
};

// mTSM Eşleştirmeleri Getir
exports.getMTSMMatches = async (tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const sql = `
      SELECT match_id, match_name, branch_name, device_count, is_active
      FROM mtsm_matches
      WHERE tenant_id = ? AND is_active = 1
      ORDER BY match_name
    `;
    const [result] = await conn.query(sql, [tenantId]);
    return result;
  } catch (error) {
    console.error("getMTSMMatches error:", error);
    throw error;
  } finally {
    conn.release();
  }
};

// Sipariş Verilerini mTSM Formatına Çevir
exports.convertOrderToMTSMFormat = async (tenantId, orderId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    // Sipariş detaylarını al
    const [orderDetails] = await conn.query(`
      SELECT o.id, o.token_no, o.date, o.customer_type, o.customer_id,
             c.name as customer_name
      FROM orders o
      LEFT JOIN customers c ON o.customer_id = c.phone AND c.tenant_id = o.tenant_id
      WHERE o.id = ? AND o.tenant_id = ?
    `, [orderId, tenantId]);

    if (!orderDetails.length) {
      throw new Error("Sipariş bulunamadı");
    }

    const order = orderDetails[0];

    // Sipariş kalemlerini al
    const [orderItems] = await conn.query(`
      SELECT oi.id, oi.item_id, oi.variant_id, oi.price, oi.quantity, oi.notes,
             mi.title as item_title, mi.tax_id,
             t.rate as tax_rate,
             miv.title as variant_title
      FROM order_items oi
      LEFT JOIN menu_items mi ON oi.item_id = mi.id
      LEFT JOIN taxes t ON mi.tax_id = t.id
      LEFT JOIN menu_item_variants miv ON oi.variant_id = miv.id
      WHERE oi.order_id = ? AND oi.status NOT IN ('cancelled')
    `, [orderId]);

    // mTSM formatına çevir
    const mtsmItems = orderItems.map(item => {
      const itemName = item.variant_title ?
        `${item.item_title} - ${item.variant_title}` :
        item.item_title;

      return {
        name: itemName,
        unitPrice: parseFloat(item.price),
        vat: item.tax_rate || 20, // Default KDV %20
        quantity: parseFloat(item.quantity) || 1,
        barcode: null,
        unit: "adet",
        discountRate: 0,
        discountAmount: 0,
        section: null
      };
    });

    // Toplam tutarı hesapla
    const totalAmount = orderItems.reduce((sum, item) => {
      return sum + (parseFloat(item.price) * parseFloat(item.quantity || 1));
    }, 0);

    // mTSM sipariş objesi
    const mtsmOrder = {
      name: `Sipariş #${order.token_no || order.id}`,
      no: parseInt(order.token_no || order.id),
      items: mtsmItems,
      totalAmount: parseFloat(totalAmount.toFixed(2)),
      subtotalAmount: parseFloat(totalAmount.toFixed(2)),
      discountRate: 0,
      discountAmount: 0,
      document: {
        type: "Fis",
        date: new Date().toISOString(),
        taxNo: null,
        no: null,
        slipCount: 2
      },
      note: null
    };

    return mtsmOrder;

  } catch (error) {
    console.error("convertOrderToMTSMFormat error:", error);
    throw error;
  } finally {
    conn.release();
  }
};

// Siparişi mTSM'e Gönder (POS'tan çağrılacak)
exports.sendOrderToMTSMFromPOS = async (tenantId, orderId) => {
  try {
    // mTSM ayarları aktif mi kontrol et
    const settings = await exports.getMTSMSettingsDB(tenantId);
    if (!settings || !settings.is_active || settings.integration_type !== 'API') {
      console.log(`mTSM entegrasyonu aktif değil - Tenant: ${tenantId}`);
      return { success: false, reason: "mTSM entegrasyonu aktif değil" };
    }

    // Sipariş verilerini mTSM formatına çevir
    const mtsmOrderData = await exports.convertOrderToMTSMFormat(tenantId, orderId);

    // matchId zorunlu - eğer varsa kullan, yoksa default kullan
    const matches = await exports.getMTSMMatches(tenantId);
    if (matches.length > 0) {
      mtsmOrderData.matchId = matches[0].match_id;
      console.log(`📍 mTSM eşleştirmesi kullanılıyor: ${matches[0].match_name}`);
    } else {
      // Default matchId kullan
      mtsmOrderData.matchId = "c519e7b8-63d2-4636-91cc-7200b2371d09";
      console.log(`📍 mTSM eşleştirmesi yok - Default matchId kullanılıyor`);
    }

    // mTSM'e gönder
    const result = await exports.sendOrderToMTSM(tenantId, mtsmOrderData, orderId);

    console.log(`✅ Sipariş mTSM'e gönderildi - Order: ${orderId}, mTSM ID: ${result.id}`);
    return { success: true, mtsmOrderId: result.id };

  } catch (error) {
    console.error(`❌ mTSM sipariş gönderme hatası - Order: ${orderId}:`, error);
    return { success: false, reason: error.message };
  }
};

// mTSM'e Sipariş Gönder
exports.sendOrderToMTSM = async (tenantId, orderData, localOrderId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const settings = await exports.getMTSMSettingsDB(tenantId);
    if (!settings || settings.integration_type !== 'API') {
      throw new Error("API entegrasyonu aktif değil");
    }

    const token = await exports.getValidToken(tenantId);
    const startTime = Date.now();

    // Debug: Gönderilen veriyi logla
    console.log('🔍 mTSM\'e gönderilen veri:', JSON.stringify(orderData, null, 2));

    const response = await axios.post(
      `${settings.server_url}/api/addOrder`,
      orderData,
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        timeout: 30000
      }
    );

    const executionTime = Date.now() - startTime;

    // Sipariş mapping'ini kaydet
    if (response.data && response.data.id) {
      await conn.query(`
        INSERT INTO mtsm_order_mappings
        (tenant_id, local_order_id, mtsm_order_id, mtsm_match_id, status, sync_status, last_sync_at)
        VALUES (?, ?, ?, ?, 'PENDING', 'SYNCED', NOW())
      `, [tenantId, localOrderId, response.data.id, orderData.matchId || null]);
    }

    // Log kaydet
    await exports.logMTSMOperation(tenantId, 'ORDER_CREATE', orderData, response.data, 'SUCCESS', null, executionTime);

    return response.data;

  } catch (error) {
    const executionTime = Date.now() - (error.startTime || Date.now());

    // Hata mapping'ini kaydet
    if (localOrderId) {
      await conn.query(`
        INSERT INTO mtsm_order_mappings
        (tenant_id, local_order_id, mtsm_order_id, status, sync_status, error_message)
        VALUES (?, ?, NULL, 'PENDING', 'FAILED', ?)
        ON DUPLICATE KEY UPDATE
          sync_status = 'FAILED',
          error_message = VALUES(error_message),
          updated_at = CURRENT_TIMESTAMP
      `, [tenantId, localOrderId, error.message]);
    }

    // Hata log'u kaydet
    await exports.logMTSMOperation(
      tenantId, 'ORDER_CREATE',
      orderData,
      null, 'FAILED',
      error.message,
      executionTime
    );

    console.error("sendOrderToMTSM error:", error);
    throw error;
  } finally {
    conn.release();
  }
};

// mTSM Sipariş Durumunu Güncelle
exports.updateMTSMOrderStatus = async (tenantId, localOrderId, status, paymentData = null) => {
  const conn = await getMySqlPromiseConnection();
  try {
    // Mapping'i bul
    const [mapping] = await conn.query(`
      SELECT mtsm_order_id, mtsm_match_id
      FROM mtsm_order_mappings
      WHERE tenant_id = ? AND local_order_id = ?
    `, [tenantId, localOrderId]);

    if (!mapping.length) {
      throw new Error("mTSM sipariş eşleştirmesi bulunamadı");
    }

    const settings = await exports.getMTSMSettingsDB(tenantId);
    if (!settings || settings.integration_type !== 'API') {
      throw new Error("API entegrasyonu aktif değil");
    }

    const token = await exports.getValidToken(tenantId);
    const startTime = Date.now();

    const updateData = {
      status,
      ...(paymentData && { payments: paymentData.payments }),
      ...(paymentData && { receipt: paymentData.receipt }),
      ...(paymentData && { deviceDocument: paymentData.deviceDocument })
    };

    const response = await axios.put(
      `${settings.server_url}/api/orders/${mapping[0].mtsm_order_id}/status`,
      updateData,
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        timeout: 30000
      }
    );

    const executionTime = Date.now() - startTime;

    // Mapping'i güncelle
    await conn.query(`
      UPDATE mtsm_order_mappings
      SET status = ?, sync_status = 'SYNCED', last_sync_at = NOW(), error_message = NULL
      WHERE tenant_id = ? AND local_order_id = ?
    `, [status, tenantId, localOrderId]);

    // Log kaydet
    await exports.logMTSMOperation(tenantId, 'ORDER_UPDATE', updateData, response.data, 'SUCCESS', null, executionTime);

    return response.data;

  } catch (error) {
    const executionTime = Date.now() - (error.startTime || Date.now());

    // Hata mapping'ini güncelle
    await conn.query(`
      UPDATE mtsm_order_mappings
      SET sync_status = 'FAILED', error_message = ?
      WHERE tenant_id = ? AND local_order_id = ?
    `, [error.message, tenantId, localOrderId]);

    // Hata log'u kaydet
    await exports.logMTSMOperation(
      tenantId, 'ORDER_UPDATE',
      { localOrderId, status },
      null, 'FAILED',
      error.message,
      executionTime
    );

    console.error("updateMTSMOrderStatus error:", error);
    throw error;
  } finally {
    conn.release();
  }
};
