const { getMySqlPromiseConnection } = require("../config/mysql.db");
const { deductStockForOrderDB } = require("./storage-location.service");

exports.createOrderDB = async (
  tenantId,
  username,
  cartItems,
  deliveryType,
  customerType,
  customerId,
  tableId,
  paymentStatus = 'pending',
  invoiceId = null,
  floorId = null
) => {
  const conn = await getMySqlPromiseConnection();

  try {
    await conn.beginTransaction();

    // 1. Günlük token numarasını al
    let tokenNo = 0;
    const [tokenSequence] = await conn.query(
      "SELECT sequence_no, DATE(last_updated) as last_updated, DATE(NOW()) as todays_date FROM token_sequences WHERE tenant_id = ? LIMIT 1 FOR UPDATE",
      [tenantId]
    );

    tokenNo = tokenSequence[0]?.sequence_no || 0;
    const tokenLastUpdated = tokenSequence[0]?.last_updated
      ? new Date(tokenSequence[0]?.last_updated).toISOString().substring(0, 10)
      : new Date().toISOString().substring(0, 10);

    const today = new Date(tokenSequence[0]?.todays_date || Date.now()).toISOString().substring(0, 10);
    if (tokenLastUpdated !== today) {
      tokenNo = 0;
    }

    tokenNo += 1;

    // 2. Siparişi `orders` tablosuna kaydet
    const [orderResult] = await conn.query(
      `INSERT INTO orders (delivery_type, customer_type, customer_id, table_id, floor_id, token_no, payment_status, invoice_id, tenant_id, username)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?);`,
      [
        deliveryType, // ✔ Doğru sıraya alındı
        customerType, // ✔ Doğru sıraya alındı
        customerId || null,
        tableId || null,
        floorId || null,
        tokenNo,
        paymentStatus || 'pending',
        invoiceId || null,
        tenantId,
        username
      ]
    );

    const orderId = orderResult.insertId;

    // Ürün ID'lerini bir diziye topla
    const menuItemIds = cartItems.map(item => item.id);

    // menu_items tablosundan puanları al
    const [menuItemsData] = await conn.query(
      `SELECT id, sew_points as points FROM menu_items WHERE id IN (?) AND tenant_id = ?`,
      [menuItemIds, tenantId]
    );

    // Puan bilgilerini ID'ye göre map'le
    const pointsMap = {};
    menuItemsData.forEach(item => {
      pointsMap[item.id] = item.points;
    });

    // 3. Sipariş ürünlerini `order_items` tablosuna ekle
    const sqlOrderItems =
    `INSERT INTO order_items
    (order_id, item_id, variant_id, price, quantity, notes, addons, tenant_id, points)
    VALUES ?`;

    await conn.query(sqlOrderItems, [
      cartItems.map((item) => [
        orderId,
        item.id,
        item.variant_id,
        item.price,
        item.quantity,
        item.notes,
        item?.addons_ids?.length > 0 ? JSON.stringify(item.addons_ids) : null,
        tenantId,
        pointsMap[item.id] || null, // menu_items tablosundaki sew-points değerini ekle, yoksa null
      ]),
    ]);

    // 4. Masanın durumunu güncelle
    if (tableId) {
      const [tableData] = await conn.query(
        `SELECT order_ids FROM store_tables WHERE id = ? AND tenant_id = ? LIMIT 1`,
        [tableId, tenantId]
      );

      let currentOrderId = tableData[0]?.order_ids || '';
      currentOrderId = currentOrderId ? `${currentOrderId},${orderId}` : `${orderId}`;

      await conn.query(
        `UPDATE store_tables SET table_status = 'busy', order_ids = ? WHERE id = ? AND tenant_id = ?`,
        [currentOrderId, tableId, tenantId]
      );
    }

    // 5. Güncellenmiş token numarasını kaydet
    await conn.query(
      `INSERT INTO token_sequences (sequence_no, last_updated, tenant_id)
       VALUES (?, NOW(), ?)
       ON DUPLICATE KEY UPDATE sequence_no = VALUES(sequence_no), last_updated = VALUES(last_updated)`,
      [tokenNo, tenantId]
    );

         // Track Recipe/Inventory Item Usage
         const inventoryUsage = {};
         const recipeItemsForLocationDeduction = [];

         cartItems.forEach(item => {
           item.recipeItems.forEach(recipe => {
             const { inventory_item_id, recipe_quantity, ingredient_title, unit, variant_id, addon_id } = recipe;

             // Skip if variant-specific and doesn't match
             if (variant_id && variant_id != item.variant_id) return;

             // Skip if addon-specific and not included
             if (addon_id && !item.addons_ids?.map(String).includes(String(addon_id))) return;

             const invId = inventory_item_id;
             const qtyNeeded = parseFloat(recipe_quantity) * item.quantity;

             if (!inventoryUsage[invId]) {
               inventoryUsage[invId] = {
                 ingredient_title,
                 unit,
                 total_quantity: 0
               };
             }

             inventoryUsage[invId].total_quantity += qtyNeeded;

             // Storage location modülü için reçete kalemlerini hazırla
             recipeItemsForLocationDeduction.push({
               inventory_item_id: invId,
               quantity: qtyNeeded
             });
           });
         });
    
    
          // Step 7: Stok düşümü - Storage Location modülü ile entegre

          // Önce Storage Location modülü ile floor bazlı stok düşümü dene
          let locationStockDeducted = false;
          if (floorId && recipeItemsForLocationDeduction.length > 0) {
            try {
              locationStockDeducted = await deductStockForOrderDB(
                orderId,
                floorId,
                recipeItemsForLocationDeduction,
                tenantId,
                username
              );
            } catch (error) {
              console.warn("Storage location stok düşümü başarısız, normal sisteme geçiliyor:", error.message);
              locationStockDeducted = false;
            }
          }

          // Eğer Storage Location modülü ile stok düşümü yapılamadıysa, normal sistemi kullan
          if (!locationStockDeducted) {
            const updateInventorySql = `
              UPDATE inventory_items
              SET quantity = ?, status = ?
              WHERE id = ? AND tenant_id = ?
            `;

            const insertLogSql = `
              INSERT INTO inventory_logs
              (tenant_id, inventory_item_id, type, quantity_change, previous_quantity, new_quantity, note, created_by)
              VALUES (?, ?, 'OUT', ?, ?, ?, ?, ?)
            `;

            for (const [inventoryItemId, usage] of Object.entries(inventoryUsage)) {
              const invId = parseInt(inventoryItemId);
              const qtyUsed = parseFloat(usage.total_quantity);

              const [[currentItem]] = await conn.query(
                'SELECT quantity, min_quantity_threshold FROM inventory_items WHERE id = ? AND tenant_id = ? FOR UPDATE',
                [invId, tenantId]
              );

              const previousQty = parseFloat(currentItem?.quantity || 0);
              const newQty = previousQty - qtyUsed;
              const minQuantityThreshold = parseFloat(currentItem?.min_quantity_threshold || 0);

              // Insert into inventory_logs
              await conn.query(insertLogSql, [
                tenantId,
                invId,
                qtyUsed,
                previousQty,
                newQty,
                invoiceId ? `Auto deduction for recipe usage in invoice #${invoiceId}` : 'Auto deduction for recipe usage in order',
                username
              ]);

              let status = 'out';
              if (newQty > 0 && newQty <= minQuantityThreshold) {
                status = 'low';
              } else if (newQty > minQuantityThreshold) {
                status = 'in';
              }

              // Update inventory_items
              await conn.query(updateInventorySql, [newQty, status, invId, tenantId]);
            }
          }
    
        // step 7: commit transaction / if any exception occurs then rollback

    await conn.commit();



    return {
      tokenNo,
      orderId,
    };
  } catch (error) {
    console.error(error);
    await conn.rollback();
    throw error;
  } finally {
    conn.release();
  }
};



exports.updateOrderDB = async (tenantId, cartItems, orderId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    await conn.beginTransaction();

    const menuItemIds = cartItems.map(item => item.id);

    // menu_items tablosundan puanları al
    const [menuItemsData] = await conn.query(
      `SELECT id, sew_points as points FROM menu_items WHERE id IN (?) AND tenant_id = ?`,
      [menuItemIds, tenantId]
    );

    // Puan bilgilerini ID'ye göre map'le
    const pointsMap = {};
    menuItemsData.forEach(item => {
      pointsMap[item.id] = item.points;
    });

    const sqlOrderItems =
      `INSERT INTO order_items
      (order_id, item_id, variant_id, price, quantity, notes, addons, tenant_id, points)
      VALUES ?`
    ;

    await conn.query(sqlOrderItems, [
      cartItems.map((item) => [
        orderId,
        item.id,
        item.variant_id,
        item.price,
        item.quantity,
        item.notes,
        item?.addons_ids?.length > 0 ? JSON.stringify(item.addons_ids) : null,
        tenantId,
        pointsMap[item.id] || null, // menu_items tablosundaki sew-points değerini ekle, yoksa null
      ]),
    ]);


    await conn.commit();
    return { success: true, orderId };
  } catch (error) {
    console.error(error);
    await conn.rollback();
    throw error;
  } finally {
    conn.release();
  }
};

exports.getTableStatus = async (tableId, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const [result] = await conn.query(
      `SELECT table_status, order_ids FROM store_tables WHERE id = ? AND tenant_id = ? LIMIT 1`,
      [tableId, tenantId]
    );
    return result[0] || null; // Eğer sonuç yoksa null dön
  } catch (error) {
    console.error(error);
    throw error;
  } finally {
    conn.release();
  }
};

exports.addStockMovementDB = async (menuItemId, movementType, quantity, previousStock, newStock, reason, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const sql = `
      INSERT INTO stock_movements
      (menu_item_id, movement_type, quantity, previous_stock, new_stock, reason, tenant_id)
      VALUES (?, ?, ?, ?, ?, ?, ?);
    `;
    await conn.query(sql, [menuItemId, movementType, quantity, previousStock, newStock, reason, tenantId]);
  } catch (error) {
    console.error(error);
    throw error;
  } finally {
    conn.release();
  }
};


exports.getPOSQROrdersCountDB = async (tenantId) => {
  const conn = await getMySqlPromiseConnection();

    try {
      const sql = `
        SELECT COUNT(*) AS total_orders FROM qr_orders
        WHERE tenant_id = ? AND status NOT IN('completed', 'cancelled');
      `;

      const [result] = await conn.query(sql, [tenantId]);
      return result[0].total_orders ?? 0;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.getPOSQROrdersDB = async (tenantId) => {
  const conn = await getMySqlPromiseConnection();

    try {
      const sql = `
       SELECT
        o.id,
        o.date,
        o.delivery_type,
        o.customer_type,
        o.customer_id,
        c.name AS customer_name,
        o.table_id,
        st.table_title,
        st.floor,
        o.status,
        o.payment_status
      FROM
        qr_orders o
        LEFT JOIN customers c ON o.customer_id = c.phone AND c.tenant_id = o.tenant_id
        LEFT JOIN store_tables st ON o.table_id = st.id
      WHERE
        o.status NOT IN('completed', 'cancelled')
        AND o.tenant_id = ?
      `;

      const [kitchenOrders] = await conn.query(sql, [tenantId]);

      let kitchenOrdersItems = [];
      let addons = [];

      if(kitchenOrders.length > 0) {
        const orderIds = kitchenOrders.map(o=>o.id).join(",");
        const sql2 = `
          SELECT
            oi.id,
            oi.order_id,
            oi.item_id,
            mi.title AS item_title,
            mi.tax_id,
            t.title as tax_title,
            t.rate as tax_rate,
            t.type as tax_type,
            oi.variant_id,
            miv.title AS variant_title,
            oi.price,
            oi.quantity,
            oi.status,
            oi.date,
            oi.addons,
            oi.notes
          FROM
            qr_order_items oi
            LEFT JOIN menu_items mi ON oi.item_id = mi.id
            LEFT JOIN taxes t ON t.id = mi.tax_id
            LEFT JOIN menu_item_variants miv ON oi.item_id = miv.item_id
            AND oi.variant_id = miv.id
          WHERE
            oi.order_id IN (${orderIds})
        `
        const [kitchenOrdersItemsResult] = await conn.query(sql2);
        kitchenOrdersItems = kitchenOrdersItemsResult;

        const addonIds = [...new Set([...kitchenOrdersItems.flatMap((o)=>o.addons?JSON.parse(o?.addons):[])])].join(",");
        const [addonsResult] = addonIds ? await conn.query(`SELECT id, item_id, title FROM menu_item_addons WHERE id IN (${addonIds});`):[]
        addons = addonsResult;
      }

      return {
        kitchenOrders,
        kitchenOrdersItems,
        addons
      }
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.updateQROrderStatusDB = async (tenantId, orderId, status) => {
  const conn = await getMySqlPromiseConnection();

  try {
    const sql = `
      UPDATE qr_orders
      SET status = ?
      WHERE tenant_id = ? AND id = ?;
    `;

    const [result] = await conn.query(sql, [status, tenantId, orderId]);
    return
  } catch (error) {
      console.error(error);
      throw error;
  } finally {
      conn.release();
  }
};


exports.cancelAllQROrdersDB = async (tenantId, status) => {
  const conn = await getMySqlPromiseConnection();

  try {
    const sql = `
      UPDATE qr_orders
      SET status = ?
      WHERE tenant_id = ?;
    `;

    const [result] = await conn.query(sql, [status, tenantId]);
    return;
  } catch (error) {
      console.error(error);
      throw error;
  } finally {
      conn.release();
  }
};
