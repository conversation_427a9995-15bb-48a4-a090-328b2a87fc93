const { getMySqlPromiseConnection } = require("../config/mysql.db");

/**
 * Tüm kasaları getirir
 * @param {number} tenantId - Kiracı ID
 * @returns {Array} Kasalar listesi
 */
exports.getAllCashRegistersDB = async (tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const sql = `
      SELECT
        cr.*,
        (SELECT COUNT(*) FROM cash_register_sessions crs
         WHERE crs.cash_register_id = cr.id AND crs.status = 'open' AND crs.tenant_id = ?) as has_active_session
      FROM
        cash_registers cr
      WHERE
        cr.tenant_id = ?
      ORDER BY
        cr.name ASC
    `;

    const [registers] = await conn.query(sql, [tenantId, tenantId]);
    return registers;
  } catch (error) {
    console.error("getAllCashRegistersDB error:", error);
    throw error;
  } finally {
    conn.release();
  }
};

/**
 * <PERSON><PERSON><PERSON> bir kasayı getirir
 * @param {number} id - Kasa ID
 * @param {number} tenantId - Kiracı ID
 * @returns {Object} Kasa bilgileri
 */
exports.getCashRegisterByIdDB = async (id, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const sql = `
      SELECT
        cr.*,
        (SELECT COUNT(*) FROM cash_register_sessions crs
         WHERE crs.cash_register_id = cr.id AND crs.status = 'open' AND crs.tenant_id = ?) as has_active_session
      FROM
        cash_registers cr
      WHERE
        cr.id = ? AND cr.tenant_id = ?
    `;

    const [registers] = await conn.query(sql, [tenantId, id, tenantId]);
    return registers.length > 0 ? registers[0] : null;
  } catch (error) {
    console.error("getCashRegisterByIdDB error:", error);
    throw error;
  } finally {
    conn.release();
  }
};

/**
 * Yeni kasa ekler
 * @param {Object} registerData - Kasa bilgileri
 * @param {number} tenantId - Kiracı ID
 * @returns {number} Eklenen kasanın ID'si
 */
exports.addCashRegisterDB = async (registerData, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const { name, description, location } = registerData;

    const sql = `
      INSERT INTO cash_registers
        (name, description, location, is_active, tenant_id)
      VALUES
        (?, ?, ?, 1, ?)
    `;

    const [result] = await conn.query(sql, [name, description, location, tenantId]);
    return result.insertId;
  } catch (error) {
    console.error("addCashRegisterDB error:", error);
    throw error;
  } finally {
    conn.release();
  }
};

/**
 * Kasa bilgilerini günceller
 * @param {number} id - Kasa ID
 * @param {Object} registerData - Güncellenecek kasa bilgileri
 * @param {number} tenantId - Kiracı ID
 * @returns {boolean} İşlem başarılı mı?
 */
exports.updateCashRegisterDB = async (id, registerData, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const { name, description, location, is_active } = registerData;

    const sql = `
      UPDATE cash_registers
      SET
        name = ?,
        description = ?,
        location = ?,
        is_active = ?
      WHERE
        id = ? AND tenant_id = ?
    `;

    const [result] = await conn.query(sql, [name, description, location, is_active, id, tenantId]);
    return result.affectedRows > 0;
  } catch (error) {
    console.error("updateCashRegisterDB error:", error);
    throw error;
  } finally {
    conn.release();
  }
};

/**
 * Kasa siler
 * @param {number} id - Kasa ID
 * @param {number} tenantId - Kiracı ID
 * @returns {boolean} İşlem başarılı mı?
 */
exports.deleteCashRegisterDB = async (id, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    // Önce aktif oturum var mı kontrol et
    const [sessions] = await conn.query(
      "SELECT id FROM cash_register_sessions WHERE cash_register_id = ? AND status = 'open' AND tenant_id = ?",
      [id, tenantId]
    );

    if (sessions.length > 0) {
      throw new Error("Bu kasa şu anda kullanımda olduğu için silinemez.");
    }

    const sql = "DELETE FROM cash_registers WHERE id = ? AND tenant_id = ?";
    const [result] = await conn.query(sql, [id, tenantId]);
    return result.affectedRows > 0;
  } catch (error) {
    console.error("deleteCashRegisterDB error:", error);
    throw error;
  } finally {
    conn.release();
  }
};

/**
 * Kullanıcının aktif kasa oturumunu kontrol eder ve ilgili işlemleri getirir
 * @param {string} username - Kullanıcı adı
 * @param {number} tenantId - Kiracı ID
 * @returns {Object|null} Aktif oturum varsa oturum ve işlem bilgileri, yoksa null
 */
exports.getUserActiveSessionDB = async (username, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    // 1. Ana session bilgisi
    const sessionSql = `
      SELECT
        crs.*,
        cr.name as register_name,
        cr.location as register_location
      FROM
        cash_register_sessions crs
        JOIN cash_registers cr ON crs.cash_register_id = cr.id
      WHERE
        crs.opened_by = ?
        AND crs.status = 'open'
        AND crs.tenant_id = ?
    `;

    const [sessions] = await conn.query(sessionSql, [username, tenantId]);

    if (sessions.length === 0) {
      return null;
    }

    const session = sessions[0];

    // 2. Tek sorguda tüm verileri çek
    const combinedSql = `
      WITH last_orders AS (
        SELECT DISTINCT o.id as order_id
        FROM payment_transactions pt
        JOIN orders o ON pt.order_id = o.id
        WHERE pt.cash_register_session_id = ?
          AND pt.tenant_id = ?
          AND pt.transaction_type = 'payment'
          AND pt.status = 'completed'
        GROUP BY o.id
        ORDER BY MAX(pt.created_at) DESC
        LIMIT 10
      ),
      order_totals AS (
        SELECT 
          o.id as order_id,
          o.table_id,
          st.table_title,
          o.date as order_date,
          o.customer_id,
          c.name as customer_name,
          COALESCE(SUM(CASE WHEN oi.status NOT IN ('cancelled', 'waste', 'complimentary') THEN oi.price * oi.quantity ELSE 0 END), 0) as order_total,
          COALESCE(SUM(CASE WHEN od.discount_type = 'percentage' THEN 
            (SELECT SUM(oi2.price * oi2.quantity) FROM order_items oi2 WHERE oi2.order_id = o.id AND oi2.status NOT IN ('cancelled', 'waste', 'complimentary')) * (od.discount_value / 100)
            WHEN od.discount_type = 'amount' THEN od.discount_value 
            ELSE 0 END), 0) as total_discounts
        FROM last_orders lo
        JOIN orders o ON lo.order_id = o.id
        LEFT JOIN store_tables st ON o.table_id = st.id
        LEFT JOIN customers c ON o.customer_id = c.phone AND o.tenant_id = c.tenant_id
        LEFT JOIN order_items oi ON o.id = oi.order_id AND oi.tenant_id = o.tenant_id
        LEFT JOIN order_discounts od ON o.id = od.order_id AND od.tenant_id = o.tenant_id
        GROUP BY o.id, o.table_id, st.table_title, o.date, o.customer_id, c.name
      )
      SELECT 
        -- Order bilgileri
        'order' as data_type,
        ot.order_id,
        ot.table_id,
        ot.table_title,
        ot.order_date,
        ot.customer_id,
        ot.customer_name,
        ot.order_total,
        ot.total_discounts,
        NULL as payment_type_id,
        NULL as payment_type,
        NULL as payment_icon,
        NULL as amount,
        NULL as transaction_type,
        NULL as created_at,
        NULL as created_by,
        NULL as user_name,
        NULL as item_id,
        NULL as item_title,
        NULL as variant_id,
        NULL as variant_title,
        NULL as price,
        NULL as quantity,
        NULL as status,
        NULL as notes,
        NULL as addons,
        NULL as isCash,
        NULL as is_mali
      FROM order_totals ot
      
      UNION ALL
      
      -- Payment bilgileri
      SELECT
        'payment' as data_type,
        pt.order_id,
        NULL as table_id,
        NULL as table_title,
        NULL as order_date,
        NULL as customer_id,
        NULL as customer_name,
        NULL as order_total,
        NULL as total_discounts,
        pt.payment_type_id,
        pt2.title as payment_type,
        pt2.icon as payment_icon,
        pt.amount,
        pt.transaction_type,
        pt.created_at,
        pt.created_by,
        u.name as user_name,
        NULL as item_id,
        NULL as item_title,
        NULL as variant_id,
        NULL as variant_title,
        NULL as price,
        NULL as quantity,
        NULL as status,
        pt.notes,
        NULL as addons,
        NULL as isCash,
        NULL as is_mali
      FROM last_orders lo
      JOIN payment_transactions pt ON lo.order_id = pt.order_id
      JOIN payment_types pt2 ON pt.payment_type_id = pt2.id
      JOIN users u ON pt.created_by = u.username
      WHERE pt.cash_register_session_id = ?
        AND pt.tenant_id = ?
        AND pt.transaction_type = 'payment'
        AND pt.status = 'completed'
      
      UNION ALL
      
      -- Order items bilgileri
      SELECT 
        'item' as data_type,
        oi.order_id,
        NULL as table_id,
        NULL as table_title,
        NULL as order_date,
        NULL as customer_id,
        NULL as customer_name,
        NULL as order_total,
        NULL as total_discounts,
        NULL as payment_type_id,
        NULL as payment_type,
        NULL as payment_icon,
        NULL as amount,
        NULL as transaction_type,
        NULL as created_at,
        NULL as created_by,
        NULL as user_name,
        oi.item_id,
        mi.title as item_title,
        oi.variant_id,
        miv.title as variant_title,
        CAST(oi.price AS DECIMAL(10,2)) as price,
        oi.quantity,
        oi.status,
        oi.notes,
        oi.addons,
        NULL as isCash,
        NULL as is_mali
      FROM last_orders lo
      JOIN order_items oi ON lo.order_id = oi.order_id
      LEFT JOIN menu_items mi ON oi.item_id = mi.id
      LEFT JOIN menu_item_variants miv ON oi.variant_id = miv.id AND oi.item_id = miv.item_id
      WHERE oi.tenant_id = ?
      
      UNION ALL
      
      -- Transaction totals (withdrawals, deposits, payment totals)
      SELECT
        pt.transaction_type as data_type,
        NULL as order_id,
        NULL as table_id,
        NULL as table_title,
        NULL as order_date,
        NULL as customer_id,
        NULL as customer_name,
        NULL as order_total,
        NULL as total_discounts,
        pt.payment_type_id,
        pt2.title as payment_type,
        pt2.icon as payment_icon,
        pt.amount,
        pt.transaction_type,
        pt.created_at,
        pt.created_by,
        u.name as user_name,
        NULL as item_id,
        NULL as item_title,
        NULL as variant_id,
        NULL as variant_title,
        NULL as price,
        NULL as quantity,
        NULL as status,
        pt.notes,
        NULL as addons,
        pt2.isCash,
        pt2.is_mali
      FROM payment_transactions pt
      JOIN payment_types pt2 ON pt.payment_type_id = pt2.id
      JOIN users u ON pt.created_by = u.username
      WHERE pt.cash_register_session_id = ?
        AND pt.tenant_id = ?
        AND pt.status = 'completed'
        AND (
          pt.transaction_type IN ('withdrawal', 'deposit') OR
          (pt.transaction_type = 'payment' AND (
            (pt2.isCash = 0) OR
            (pt2.isCash = 1 AND pt2.is_mali = 1)
          ))
        )
      
      ORDER BY data_type, order_id, created_at DESC
    `;

    const [combinedResults] = await conn.query(combinedSql, [
      session.id, tenantId,  // last_orders CTE için
      session.id, tenantId,  // payments için  
      tenantId,              // items için
      session.id, tenantId   // transactions için
    ]);

    // Verileri grupla ve işle
    const lastTransactions = [];
    const transactions = [];
    const paymentTotals = new Map();
    const withdrawalTotals = new Map();
    const depositTotals = new Map();
    let totalWithdrawals = 0, withdrawalCount = 0;
    let totalDeposits = 0, depositCount = 0;

    // Sipariş verilerini grupla
    const orderData = new Map();
    const orderPayments = new Map();
    const orderItems = new Map();

    combinedResults.forEach(row => {
      switch (row.data_type) {
        case 'order':
          orderData.set(row.order_id, {
            order_id: row.order_id,
            table_id: row.table_id,
            table_title: row.table_title,
            order_date: row.order_date,
            customer_id: row.customer_id,
            customer_name: row.customer_name,
            order_total: parseFloat(row.order_total || 0),
            total_discounts: parseFloat(row.total_discounts || 0)
          });
          break;
          
        case 'payment':
          if (!orderPayments.has(row.order_id)) {
            orderPayments.set(row.order_id, []);
          }
          orderPayments.get(row.order_id).push({
            ...row,
            transaction_type_display: 'Ödeme'
          });
          break;
          
        case 'item':
          if (!orderItems.has(row.order_id)) {
            orderItems.set(row.order_id, []);
          }
          try {
            const addons = row.addons ? JSON.parse(row.addons) : [];
            orderItems.get(row.order_id).push({
              ...row,
              addons,
              total_price: parseFloat((row.price * row.quantity).toFixed(2))
            });
          } catch (e) {
            orderItems.get(row.order_id).push({
              ...row,
              addons: [],
              total_price: parseFloat((row.price * row.quantity).toFixed(2))
            });
          }
          break;
          
        case 'withdrawal':
          withdrawalCount++;
          totalWithdrawals += parseFloat(row.amount);
          
          const wKey = `${row.payment_type_id}-${row.payment_type}`;
          if (!withdrawalTotals.has(wKey)) {
            withdrawalTotals.set(wKey, {
              payment_type_id: row.payment_type_id,
              payment_type: row.payment_type,
              payment_icon: row.payment_icon,
              count: 0,
              total: 0
            });
          }
          withdrawalTotals.get(wKey).count++;
          withdrawalTotals.get(wKey).total += parseFloat(row.amount);
          
          transactions.push({
            ...row,
            transaction_type_display: 'Para Çıkışı'
          });
          break;
          
        case 'deposit':
          depositCount++;
          totalDeposits += parseFloat(row.amount);
          
          const dKey = `${row.payment_type_id}-${row.payment_type}`;
          if (!depositTotals.has(dKey)) {
            depositTotals.set(dKey, {
              payment_type_id: row.payment_type_id,
              payment_type: row.payment_type,
              payment_icon: row.payment_icon,
              count: 0,
              total: 0
            });
          }
          depositTotals.get(dKey).count++;
          depositTotals.get(dKey).total += parseFloat(row.amount);
          
          transactions.push({
            ...row,
            transaction_type_display: 'Para Girişi'
          });
          break;
          
        case 'payment':
          const pKey = `${row.payment_type_id}-${row.payment_type}`;
          if (!paymentTotals.has(pKey)) {
            paymentTotals.set(pKey, {
              payment_type_id: row.payment_type_id,
              payment_type_name: row.payment_type,
              payment_icon: row.payment_icon,
              isCash: row.isCash,
              is_mali: row.is_mali,
              total_amount: 0
            });
          }
          paymentTotals.get(pKey).total_amount += parseFloat(row.amount);
          break;
      }
    });

    // Son siparişleri oluştur
    for (const [orderId, orderInfo] of orderData) {
      const payments = orderPayments.get(orderId) || [];
      const items = orderItems.get(orderId) || [];
      
      const totalPayments = payments.reduce((sum, p) => sum + parseFloat(p.amount), 0);
      const remainingAmount = orderInfo.order_total - totalPayments - orderInfo.total_discounts;
      
      lastTransactions.push({
        ...orderInfo,
        total_payments: totalPayments,
        remaining_amount: parseFloat(remainingAmount.toFixed(2)),
        payments,
        items
      });
    }

    return {
      ...session,
      transactions,
      lastTransactions,
      payment_totals_by_type: Array.from(paymentTotals.values()),
      withdrawal_totals_by_type: Array.from(withdrawalTotals.values()),
      deposit_totals_by_type: Array.from(depositTotals.values()),
      total_withdrawals: totalWithdrawals,
      withdrawal_count: withdrawalCount,
      total_deposits: totalDeposits,
      deposit_count: depositCount
    };

  } catch (error) {
    console.error("getUserActiveSessionDB error:", error);
    throw error;
  } finally {
    conn.release();
  }
};
/**
 * Kasanın aktif oturumunu kontrol eder
 * @param {number} registerId - Kasa ID
 * @param {number} tenantId - Kiracı ID
 * @returns {Object|null} Aktif oturum varsa oturum bilgileri, yoksa null
 */
exports.getRegisterActiveSessionDB = async (registerId, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const sql = `
      SELECT
        crs.*,
        u.name as cashier_name
      FROM
        cash_register_sessions crs
        JOIN users u ON crs.opened_by = u.username
      WHERE
        crs.cash_register_id = ?
        AND crs.status = 'open'
        AND crs.tenant_id = ?
    `;

    const [sessions] = await conn.query(sql, [registerId, tenantId]);
    return sessions.length > 0 ? sessions[0] : null;
  } catch (error) {
    console.error("getRegisterActiveSessionDB error:", error);
    throw error;
  } finally {
    conn.release();
  }
};

/**
 * Kasa oturumu açar
 * @param {Object} sessionData - Oturum bilgileri
 * @param {number} tenantId - Kiracı ID
 * @returns {number} Açılan oturumun ID'si
 */
exports.openCashRegisterSessionDB = async (sessionData, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    await conn.beginTransaction();

    const { cash_register_id, opened_by, opening_amount, opening_notes } = sessionData;

    // Kullanıcının aktif oturumu var mı kontrol et
    const [userSessions] = await conn.query(
      "SELECT id FROM cash_register_sessions WHERE opened_by = ? AND status = 'open' AND tenant_id = ?",
      [opened_by, tenantId]
    );

    if (userSessions.length > 0) {
      await conn.rollback();
      throw new Error("Bu kullanıcının zaten açık bir kasa oturumu bulunmaktadır.");
    }

    // Kasanın aktif oturumu var mı kontrol et
    const [registerSessions] = await conn.query(
      "SELECT id FROM cash_register_sessions WHERE cash_register_id = ? AND status = 'open' AND tenant_id = ?",
      [cash_register_id, tenantId]
    );

    if (registerSessions.length > 0) {
      await conn.rollback();
      throw new Error("Bu kasa zaten açık durumda.");
    }

    // Yeni oturum oluştur
    const sql = `
      INSERT INTO cash_register_sessions
        (cash_register_id, opened_by, opening_amount, opening_notes, tenant_id)
      VALUES
        (?, ?, ?, ?, ?)
    `;

    const [result] = await conn.query(sql, [
      cash_register_id,
      opened_by,
      opening_amount,
      opening_notes,
      tenantId
    ]);

    await conn.commit();
    return result.insertId;
  } catch (error) {
    if (conn) await conn.rollback();
    console.error("openCashRegisterSessionDB error:", error);
    throw error;
  } finally {
    conn.release();
  }
};

/**
 * Kasa oturumunu kapatır
 * @param {number} sessionId - Oturum ID
 * @param {Object} closeData - Kapanış bilgileri
 * @param {number} tenantId - Kiracı ID
 * @returns {boolean} İşlem başarılı mı?
 */
exports.closeCashRegisterSessionDB = async (sessionId, closeData, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    await conn.beginTransaction();

    const { closed_by, closing_amount, closing_notes, bank_details } = closeData;

    // Oturumu kontrol et
    const [sessions] = await conn.query(
      "SELECT * FROM cash_register_sessions WHERE id = ? AND status = 'open' AND tenant_id = ?",
      [sessionId, tenantId]
    );

    if (sessions.length === 0) {
      await conn.rollback();
      throw new Error("Geçerli bir açık kasa oturumu bulunamadı.");
    }

    const session = sessions[0];

    // Beklenen tutarı hesapla
    const [payments] = await conn.query(
      `SELECT COALESCE(SUM(
        CASE
          WHEN transaction_type = 'withdrawal' THEN -amount
          WHEN transaction_type = 'deposit' THEN amount
          ELSE amount
        END
      ), 0) as total_payments
       FROM payment_transactions
       WHERE cash_register_session_id = ? AND status = 'completed' AND tenant_id = ?`,
      [sessionId, tenantId]
    );

    const totalPayments = parseFloat(payments[0].total_payments || 0);
    const openingAmount = parseFloat(session.opening_amount || 0);
    const expectedAmount = openingAmount + totalPayments;
    const closingAmount = parseFloat(closing_amount || 0);
    const differenceAmount = closingAmount - expectedAmount;

    // Oturumu kapat
    const sql = `
      UPDATE cash_register_sessions
      SET
        closed_by = ?,
        closing_amount = ?,
        expected_amount = ?,
        difference_amount = ?,
        closing_notes = ?,
        closed_at = NOW(),
        status = 'closed'
      WHERE
        id = ? AND tenant_id = ?
    `;

    const [result] = await conn.query(sql, [
      closed_by,
      closingAmount,
      expectedAmount,
      differenceAmount,
      closing_notes,
      sessionId,
      tenantId
    ]);

    // Son 3 işlemi getir
    const lastTransactionsSql = `
      SELECT
        pt.*,
        pt.created_by as username,
        u.name as user_name,
        o.id as order_id,
        o.table_id,
        st.table_title,
        pt2.title as payment_type,
        CASE
          WHEN pt.transaction_type = 'withdrawal' THEN 'Para Çıkışı'
          WHEN pt.transaction_type = 'deposit' THEN 'Para Girişi'
          ELSE 'Ödeme'
        END as transaction_type_display
      FROM
        payment_transactions pt
        JOIN users u ON pt.created_by = u.username
        LEFT JOIN orders o ON pt.order_id = o.id
        LEFT JOIN store_tables st ON o.table_id = st.id
        JOIN payment_types pt2 ON pt.payment_type_id = pt2.id
      WHERE
        pt.cash_register_session_id = ? AND pt.tenant_id = ?
      ORDER BY
        pt.created_at DESC
      LIMIT 10
    `;

    const [lastTransactions] = await conn.query(lastTransactionsSql, [sessionId, tenantId]);

    // Banka detaylarını kaydet (eğer varsa)
    if (bank_details && bank_details.length > 0) {
      const { addCashRegisterSessionBankDetailsDB } = require("./bank.service");
      await addCashRegisterSessionBankDetailsDB(sessionId, bank_details, tenantId);
    }

    await conn.commit();

    return {
      success: result.affectedRows > 0,
      lastTransactions: lastTransactions
    };
  } catch (error) {
    if (conn) await conn.rollback();
    console.error("closeCashRegisterSessionDB error:", error);
    throw error;
  } finally {
    conn.release();
  }
};

/**
 * Kasa oturumlarını listeler (sayfalama ile)
 * @param {Object} filters - Filtre seçenekleri
 * @param {number} tenantId - Kiracı ID
 * @param {number} page - Sayfa numarası (varsayılan: 1)
 * @param {number} limit - Sayfa başına kayıt sayısı (varsayılan: 20)
 * @returns {Object} Oturumlar listesi ve sayfalama bilgileri
 */
exports.getCashRegisterSessionsDB = async (filters, tenantId, page = 1, limit = 20) => {
  const conn = await getMySqlPromiseConnection();
  try {
    let whereConditions = ["crs.tenant_id = ?"];
    let queryParams = [tenantId];

    // Filtreler
    if (filters.status) {
      whereConditions.push("crs.status = ?");
      queryParams.push(filters.status);
    }

    if (filters.cashRegisterId) {
      whereConditions.push("crs.cash_register_id = ?");
      queryParams.push(filters.cashRegisterId);
    }

    if (filters.username) {
      whereConditions.push("(crs.opened_by = ? OR crs.closed_by = ?)");
      queryParams.push(filters.username, filters.username);
    }

    if (filters.startDate && filters.endDate) {
      whereConditions.push("crs.opened_at BETWEEN ? AND ?");
      queryParams.push(filters.startDate + " 00:00:00", filters.endDate + " 23:59:59");
    }

    const whereClause = whereConditions.join(" AND ");

    // Sayfalama hesaplamaları
    const offset = (page - 1) * limit;

    // Toplam kayıt sayısını bul
    const countSql = `
      SELECT COUNT(*) as total
      FROM cash_register_sessions crs
      JOIN cash_registers cr ON crs.cash_register_id = cr.id
      JOIN users u1 ON crs.opened_by = u1.username
      LEFT JOIN users u2 ON crs.closed_by = u2.username
      WHERE ${whereClause}
    `;

    const [countResult] = await conn.query(countSql, queryParams);
    const totalRecords = countResult[0].total;
    const totalPages = Math.ceil(totalRecords / limit);

    // Ana sorgu (sayfalama ile)
    const sql = `
      SELECT
        crs.*,
        cr.name as register_name,
        cr.location as register_location,
        u1.name as opener_name,
        u2.name as closer_name,
        (SELECT COALESCE(SUM(
          CASE
            WHEN transaction_type = 'withdrawal' THEN -amount
            WHEN transaction_type = 'deposit' THEN amount
            ELSE amount
          END
        ), 0)
         FROM payment_transactions
         WHERE cash_register_session_id = crs.id AND status = 'completed' AND tenant_id = ?) as total_transactions
      FROM
        cash_register_sessions crs
        JOIN cash_registers cr ON crs.cash_register_id = cr.id
        JOIN users u1 ON crs.opened_by = u1.username
        LEFT JOIN users u2 ON crs.closed_by = u2.username
      WHERE
        ${whereClause}
      ORDER BY
        crs.opened_at DESC
      LIMIT ? OFFSET ?
    `;

    const [sessions] = await conn.query(sql, [tenantId, ...queryParams, limit, offset]);

    // Get payment totals by payment type for each session
    for (const session of sessions) {
      // Regular payments by payment type
      const paymentTypesSql = `
        SELECT
          pt.payment_type_id,
          pt2.title as payment_type_name,
          COALESCE(SUM(pt.amount), 0) as total_amount
        FROM
          payment_transactions pt
          JOIN payment_types pt2 ON pt.payment_type_id = pt2.id
        WHERE
          pt.cash_register_session_id = ?
          AND pt.status = 'completed'
          AND pt.transaction_type = 'payment'
          AND pt.tenant_id = ?
        GROUP BY
          pt.payment_type_id, pt2.title
      `;

      // Withdrawals by payment type
      const withdrawalTypesSql = `
        SELECT
          pt.payment_type_id,
          pt2.title as payment_type_name,
          COALESCE(SUM(pt.amount), 0) as total_amount
        FROM
          payment_transactions pt
          JOIN payment_types pt2 ON pt.payment_type_id = pt2.id
        WHERE
          pt.cash_register_session_id = ?
          AND pt.status = 'completed'
          AND pt.transaction_type = 'withdrawal'
          AND pt.tenant_id = ?
        GROUP BY
          pt.payment_type_id, pt2.title
      `;

      // Total withdrawals
      const totalWithdrawalsSql = `
        SELECT
          COALESCE(SUM(pt.amount), 0) as total_withdrawals,
          COUNT(*) as withdrawal_count
        FROM
          payment_transactions pt
        WHERE
          pt.cash_register_session_id = ?
          AND pt.status = 'completed'
          AND pt.transaction_type = 'withdrawal'
          AND pt.tenant_id = ?
      `;

      // Total deposits
      const totalDepositsSql = `
        SELECT
          COALESCE(SUM(pt.amount), 0) as total_deposits,
          COUNT(*) as deposit_count
        FROM
          payment_transactions pt
        WHERE
          pt.cash_register_session_id = ?
          AND pt.status = 'completed'
          AND pt.transaction_type = 'deposit'
          AND pt.tenant_id = ?
      `;

      // Deposits by payment type
      const depositTypesSql = `
        SELECT
          pt.payment_type_id,
          pt2.title as payment_type_name,
          COALESCE(SUM(pt.amount), 0) as total_amount
        FROM
          payment_transactions pt
          JOIN payment_types pt2 ON pt.payment_type_id = pt2.id
        WHERE
          pt.cash_register_session_id = ?
          AND pt.status = 'completed'
          AND pt.transaction_type = 'deposit'
          AND pt.tenant_id = ?
        GROUP BY
          pt.payment_type_id, pt2.title
      `;

      const [paymentTotals] = await conn.query(paymentTypesSql, [session.id, tenantId]);
      const [withdrawalTotals] = await conn.query(withdrawalTypesSql, [session.id, tenantId]);
      const [totalWithdrawals] = await conn.query(totalWithdrawalsSql, [session.id, tenantId]);
      const [depositTotals] = await conn.query(depositTypesSql, [session.id, tenantId]);
      const [totalDeposits] = await conn.query(totalDepositsSql, [session.id, tenantId]);

      session.payment_totals_by_type = paymentTotals;
      session.withdrawal_totals_by_type = withdrawalTotals;
      session.deposit_totals_by_type = depositTotals;
      session.total_withdrawals = totalWithdrawals[0].total_withdrawals;
      session.withdrawal_count = totalWithdrawals[0].withdrawal_count;
      session.total_deposits = totalDeposits[0].total_deposits;
      session.deposit_count = totalDeposits[0].deposit_count;
    }

    return {
      data: sessions,
      pagination: {
        currentPage: page,
        totalPages: totalPages,
        totalRecords: totalRecords,
        limit: limit,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      }
    };
  } catch (error) {
    console.error("getCashRegisterSessionsDB error:", error);
    throw error;
  } finally {
    conn.release();
  }
};

/**
 * Belirli bir kasa oturumunu getirir
 * @param {number} sessionId - Oturum ID
 * @param {number} tenantId - Kiracı ID
 * @returns {Object} Oturum bilgileri
 */
exports.getCashRegisterSessionByIdDB = async (sessionId, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const sql = `
      SELECT
        crs.*,
        cr.name as register_name,
        cr.location as register_location,
        u1.name as opener_name,
        u2.name as closer_name,
        (SELECT COALESCE(SUM(
          CASE
            WHEN transaction_type = 'withdrawal' THEN -amount
            WHEN transaction_type = 'deposit' THEN amount
            ELSE amount
          END
        ), 0)
         FROM payment_transactions
         WHERE cash_register_session_id = crs.id AND status = 'completed' AND tenant_id = ?) as total_transactions
      FROM
        cash_register_sessions crs
        JOIN cash_registers cr ON crs.cash_register_id = cr.id
        JOIN users u1 ON crs.opened_by = u1.username
        LEFT JOIN users u2 ON crs.closed_by = u2.username
      WHERE
        crs.id = ? AND crs.tenant_id = ?
    `;

    const [sessions] = await conn.query(sql, [tenantId, sessionId, tenantId]);

    if (sessions.length === 0) {
      return null;
    }

    const session = sessions[0];

    // Regular payments by payment type
    const paymentTypesSql = `
      SELECT
        pt.payment_type_id,
        pt2.title as payment_type_name,
        COALESCE(SUM(pt.amount), 0) as total_amount
      FROM
        payment_transactions pt
        JOIN payment_types pt2 ON pt.payment_type_id = pt2.id
      WHERE
        pt.cash_register_session_id = ?
        AND pt.status = 'completed'
        AND pt.transaction_type = 'payment'
        AND pt.tenant_id = ?
      GROUP BY
        pt.payment_type_id, pt2.title
    `;

    // Withdrawals by payment type
    const withdrawalTypesSql = `
      SELECT
        pt.payment_type_id,
        pt2.title as payment_type_name,
        COALESCE(SUM(pt.amount), 0) as total_amount
      FROM
        payment_transactions pt
        JOIN payment_types pt2 ON pt.payment_type_id = pt2.id
      WHERE
        pt.cash_register_session_id = ?
        AND pt.status = 'completed'
        AND pt.transaction_type = 'withdrawal'
        AND pt.tenant_id = ?
      GROUP BY
        pt.payment_type_id, pt2.title
    `;

    // Total withdrawals
    const totalWithdrawalsSql = `
      SELECT
        COALESCE(SUM(pt.amount), 0) as total_withdrawals,
        COUNT(*) as withdrawal_count
      FROM
        payment_transactions pt
      WHERE
        pt.cash_register_session_id = ?
        AND pt.status = 'completed'
        AND pt.transaction_type = 'withdrawal'
        AND pt.tenant_id = ?
    `;

    // Total deposits
    const totalDepositsSql = `
      SELECT
        COALESCE(SUM(pt.amount), 0) as total_deposits,
        COUNT(*) as deposit_count
      FROM
        payment_transactions pt
      WHERE
        pt.cash_register_session_id = ?
        AND pt.status = 'completed'
        AND pt.transaction_type = 'deposit'
        AND pt.tenant_id = ?
    `;

    // Deposits by payment type
    const depositTypesSql = `
      SELECT
        pt.payment_type_id,
        pt2.title as payment_type_name,
        COALESCE(SUM(pt.amount), 0) as total_amount
      FROM
        payment_transactions pt
        JOIN payment_types pt2 ON pt.payment_type_id = pt2.id
      WHERE
        pt.cash_register_session_id = ?
        AND pt.status = 'completed'
        AND pt.transaction_type = 'deposit'
        AND pt.tenant_id = ?
      GROUP BY
        pt.payment_type_id, pt2.title
    `;

    const [paymentTotals] = await conn.query(paymentTypesSql, [sessionId, tenantId]);
    const [withdrawalTotals] = await conn.query(withdrawalTypesSql, [sessionId, tenantId]);
    const [totalWithdrawals] = await conn.query(totalWithdrawalsSql, [sessionId, tenantId]);
    const [depositTotals] = await conn.query(depositTypesSql, [sessionId, tenantId]);
    const [totalDeposits] = await conn.query(totalDepositsSql, [sessionId, tenantId]);

    // Birden fazla ödemesi olan siparişleri getir
    const multiplePaymentOrdersSql = `
      SELECT
        o.id as order_id,
        o.date as order_date,
        o.table_id,
        st.table_title,
        o.customer_id,
        c.name as customer_name,
        o.payment_status,
        o.delivery_type,
        o.username as created_by,
        u.name as created_by_name,
        COUNT(pt.id) as payment_count,
        CAST(COALESCE(SUM(pt.amount), 0) AS DECIMAL(10,2)) as total_paid_in_session
      FROM orders o
      LEFT JOIN store_tables st ON o.table_id = st.id
      LEFT JOIN customers c ON o.customer_id = c.phone AND o.tenant_id = c.tenant_id
      LEFT JOIN users u ON o.username = u.username AND o.tenant_id = u.tenant_id
      JOIN payment_transactions pt ON o.id = pt.order_id
        AND pt.cash_register_session_id = ?
        AND pt.transaction_type = 'payment'
        AND pt.status = 'completed'
        AND pt.tenant_id = ?
      WHERE o.tenant_id = ?
      GROUP BY o.id
      HAVING payment_count > 1
      ORDER BY o.date DESC
    `;

    const [multiplePaymentOrders] = await conn.query(multiplePaymentOrdersSql, [sessionId, tenantId, tenantId]);

    // Her sipariş için detaylı bilgileri getir
    const detailedMultiplePaymentOrders = [];

    for (const order of multiplePaymentOrders) {
      // Sipariş ürünlerini getir
      const orderItemsSql = `
        SELECT
          oi.id as order_item_id,
          oi.item_id,
          mi.title as item_title,
          oi.variant_id,
          miv.title as variant_title,
          CAST(oi.price AS DECIMAL(10,2)) as price,
          oi.quantity,
          CAST(oi.price * oi.quantity AS DECIMAL(10,2)) as total_price,
          oi.status,
          oi.notes,
          oi.addons,
          CASE
            WHEN oi.status = 'cancelled' THEN oi.cancelled_by
            WHEN oi.status = 'waste' THEN oi.waste_by
            WHEN oi.status = 'complimentary' THEN oi.complimentary_by
            ELSE NULL
          END as action_by,
          CASE
            WHEN oi.status = 'cancelled' THEN cr.title
            WHEN oi.status = 'waste' THEN wr.title
            WHEN oi.status = 'complimentary' THEN comp_r.title
            ELSE NULL
          END as reason_title
        FROM order_items oi
        LEFT JOIN menu_items mi ON oi.item_id = mi.id
        LEFT JOIN menu_item_variants miv ON oi.variant_id = miv.id AND oi.item_id = miv.item_id
        LEFT JOIN cancellation_reasons cr ON oi.cancelled_reason_id = cr.id
        LEFT JOIN waste_reasons wr ON oi.waste_reason_id = wr.id
        LEFT JOIN complimentary_reasons comp_r ON oi.complimentary_reason_id = comp_r.id
        WHERE oi.order_id = ? AND oi.tenant_id = ?
        ORDER BY oi.id
      `;

      // Sipariş indirimlerini getir
      const orderDiscountsSql = `
        SELECT
          od.id as discount_id,
          od.discount_type,
          CAST(od.discount_value AS DECIMAL(10,2)) as discount_value,
          CAST(CASE
            WHEN od.discount_type = 'percentage' THEN
              CASE
                WHEN od.order_item_id IS NULL THEN
                  (SELECT SUM(oi2.price * oi2.quantity) FROM order_items oi2
                   WHERE oi2.order_id = od.order_id AND oi2.status NOT IN ('cancelled')) * (od.discount_value / 100)
                ELSE
                  (SELECT oi2.price * oi2.quantity FROM order_items oi2 WHERE oi2.id = od.order_item_id) * (od.discount_value / 100)
              END
            WHEN od.discount_type = 'amount' THEN od.discount_value
            ELSE 0
          END AS DECIMAL(10,2)) as calculated_discount,
          od.order_item_id,
          mi.title as item_title,
          od.created_by as discount_by,
          u.name as discount_by_name
        FROM order_discounts od
        LEFT JOIN order_items oi ON od.order_item_id = oi.id
        LEFT JOIN menu_items mi ON oi.item_id = mi.id
        LEFT JOIN users u ON od.created_by = u.username AND u.tenant_id = od.tenant_id
        WHERE od.order_id = ? AND od.tenant_id = ?
      `;

      // Bu siparişin bu oturumdaki ödemelerini getir
      const orderPaymentsSql = `
        SELECT
          pt.id as payment_id,
          CAST(pt.amount AS DECIMAL(10,2)) as amount,
          pt.created_at as payment_date,
          pt.notes,
          pt.created_by as payment_by,
          u.name as payment_by_name,
          pt2.title as payment_type,
          pt2.icon as payment_icon
        FROM payment_transactions pt
        JOIN users u ON pt.created_by = u.username
        JOIN payment_types pt2 ON pt.payment_type_id = pt2.id
        WHERE pt.order_id = ?
          AND pt.cash_register_session_id = ?
          AND pt.tenant_id = ?
          AND pt.transaction_type = 'payment'
          AND pt.status = 'completed'
        ORDER BY pt.created_at ASC
      `;

      const [orderItems] = await conn.query(orderItemsSql, [order.order_id, tenantId]);
      const [orderDiscounts] = await conn.query(orderDiscountsSql, [order.order_id, tenantId]);
      const [orderPayments] = await conn.query(orderPaymentsSql, [order.order_id, sessionId, tenantId]);

      // Eklentileri JSON olarak parse et
      const itemsWithParsedAddons = orderItems.map(item => {
        try {
          if (item.addons) {
            item.addons = JSON.parse(item.addons);
          } else {
            item.addons = [];
          }
        } catch (e) {
          item.addons = [];
        }
        return item;
      });

      // Sipariş toplamlarını hesapla
      const orderTotal = orderItems.reduce((sum, item) => {
        if (item.status !== 'cancelled') {
          return sum + parseFloat(item.total_price);
        }
        return sum;
      }, 0);

      const totalDiscounts = orderDiscounts.reduce((sum, discount) => {
        return sum + parseFloat(discount.calculated_discount);
      }, 0);

      const totalPayments = orderPayments.reduce((sum, payment) => {
        return sum + parseFloat(payment.amount);
      }, 0);

      const remainingAmount = orderTotal - totalDiscounts - totalPayments;

      detailedMultiplePaymentOrders.push({
        ...order,
        order_total: parseFloat(orderTotal.toFixed(2)),
        total_discounts: parseFloat(totalDiscounts.toFixed(2)),
        total_payments_in_session: parseFloat(totalPayments.toFixed(2)),
        remaining_amount: parseFloat(remainingAmount.toFixed(2)),
        items: itemsWithParsedAddons,
        discounts: orderDiscounts,
        payments: orderPayments
      });
    }

    session.payment_totals_by_type = paymentTotals;
    session.withdrawal_totals_by_type = withdrawalTotals;
    session.deposit_totals_by_type = depositTotals;
    session.total_withdrawals = totalWithdrawals[0].total_withdrawals;
    session.withdrawal_count = totalWithdrawals[0].withdrawal_count;
    session.total_deposits = totalDeposits[0].total_deposits;
    session.deposit_count = totalDeposits[0].deposit_count;
    session.multiple_payment_orders = detailedMultiplePaymentOrders;

    // Banka detaylarını getir (eğer oturum kapatılmışsa)
    if (session.status === 'closed') {
      const { getCashRegisterSessionBankDetailsDB } = require("./bank.service");
      session.bank_details = await getCashRegisterSessionBankDetailsDB(sessionId, tenantId);
    }

    return session;
  } catch (error) {
    console.error("getCashRegisterSessionByIdDB error:", error);
    throw error;
  } finally {
    conn.release();
  }
};

/**
 * Kasa oturumundaki ödeme işlemlerini getirir
 * @param {number} sessionId - Oturum ID
 * @param {number} tenantId - Kiracı ID
 * @returns {Array} Ödeme işlemleri listesi
 */
exports.getSessionTransactionsDB = async (sessionId, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const sql = `
      SELECT
        pt.*,
        pt.created_by as username,
        u.name as user_name,
        o.id as order_id,
        o.table_id,
        st.table_title,
        pt2.title as payment_type,
        CASE
          WHEN pt.transaction_type = 'withdrawal' THEN 'Para Çıkışı'
          WHEN pt.transaction_type = 'deposit' THEN 'Para Girişi'
          ELSE 'Ödeme'
        END as transaction_type_display
      FROM
        payment_transactions pt
        JOIN users u ON pt.created_by = u.username
        LEFT JOIN orders o ON pt.order_id = o.id
        LEFT JOIN store_tables st ON o.table_id = st.id
        JOIN payment_types pt2 ON pt.payment_type_id = pt2.id
      WHERE
        pt.cash_register_session_id = ? AND pt.tenant_id = ?
      ORDER BY
        pt.created_at DESC
    `;

    const [transactions] = await conn.query(sql, [sessionId, tenantId]);
    return transactions;
  } catch (error) {
    console.error("getSessionTransactionsDB error:", error);
    throw error;
  } finally {
    conn.release();
  }
};

/**
 * Ödeme işlemi ekler
 * @param {Object} transactionData - Ödeme işlemi bilgileri
 * @param {number} tenantId - Kiracı ID
 * @returns {number} Eklenen işlemin ID'si
 */
exports.addPaymentTransactionDB = async (transactionData, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const {
      order_id,
      payment_type_id,
      amount,
      transaction_type,
      notes,
      created_by,
      invoice_id,
      cash_register_session_id
    } = transactionData;

    const sql = `
      INSERT INTO payment_transactions
        (order_id, payment_type_id, amount, transaction_type, notes, created_by, invoice_id, cash_register_session_id, tenant_id)
      VALUES
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const [result] = await conn.query(sql, [
      order_id,
      payment_type_id,
      amount,
      transaction_type || 'payment',
      notes,
      created_by,
      invoice_id,
      cash_register_session_id,
      tenantId
    ]);

    return result.insertId;
  } catch (error) {
    console.error("addPaymentTransactionDB error:", error);
    throw error;
  } finally {
    conn.release();
  }
};

/**
 * Ödeme işlemini günceller
 * @param {number} transactionId - İşlem ID
 * @param {Object} transactionData - Güncellenecek işlem bilgileri
 * @param {number} tenantId - Kiracı ID
 * @returns {boolean} İşlem başarılı mı?
 */
exports.updatePaymentTransactionDB = async (transactionId, transactionData, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const {
      payment_type_id,
      amount,
      transaction_type,
      status,
      notes,
      updated_by
    } = transactionData;

    const sql = `
      UPDATE payment_transactions
      SET
        payment_type_id = ?,
        amount = ?,
        transaction_type = ?,
        status = ?,
        notes = ?,
        updated_by = ?,
        updated_at = NOW()
      WHERE
        id = ? AND tenant_id = ?
    `;

    const [result] = await conn.query(sql, [
      payment_type_id,
      amount,
      transaction_type,
      status,
      notes,
      updated_by,
      transactionId,
      tenantId
    ]);

    return result.affectedRows > 0;
  } catch (error) {
    console.error("updatePaymentTransactionDB error:", error);
    throw error;
  } finally {
    conn.release();
  }
};

/**
 * Ödeme işlemini siler
 * @param {number} transactionId - İşlem ID
 * @param {string} deletedBy - Silen kullanıcı
 * @param {number} tenantId - Kiracı ID
 * @returns {boolean} İşlem başarılı mı?
 */
exports.deletePaymentTransactionDB = async (transactionId, deletedBy, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    // İşlemi iptal et (silmek yerine durumunu değiştir)
    const sql = `
      UPDATE payment_transactions
      SET
        status = 'cancelled',
        updated_by = ?,
        updated_at = NOW(),
        notes = CONCAT(IFNULL(notes, ''), ' [Deleted by ', ?, ' at ', NOW(), ']')
      WHERE
        id = ? AND tenant_id = ?
    `;

    const [result] = await conn.query(sql, [deletedBy, deletedBy, transactionId, tenantId]);
    return result.affectedRows > 0;
  } catch (error) {
    console.error("deletePaymentTransactionDB error:", error);
    throw error;
  } finally {
    conn.release();
  }
};

/**
 * Kasadan para çıkışı yapar
 * @param {Object} withdrawalData - Para çıkışı bilgileri
 * @param {number} tenantId - Kiracı ID
 * @returns {number} Eklenen işlemin ID'si
 */
exports.addCashWithdrawalDB = async (withdrawalData, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const {
      amount,
      reason,
      payment_type_id,
      created_by,
      cash_register_session_id
    } = withdrawalData;

    // Ödeme işlemi olarak kaydet (transaction_type = 'withdrawal')
    const sql = `
      INSERT INTO payment_transactions
        (payment_type_id, amount, transaction_type, notes, created_by, cash_register_session_id, tenant_id)
      VALUES
        (?, ?, 'withdrawal', ?, ?, ?, ?)
    `;

    const [result] = await conn.query(sql, [
      payment_type_id,
      amount,
      reason,
      created_by,
      cash_register_session_id,
      tenantId
    ]);

    return result.insertId;
  } catch (error) {
    console.error("addCashWithdrawalDB error:", error);
    throw error;
  } finally {
    conn.release();
  }
};

/**
 * Kasaya para girişi yapar
 * @param {Object} depositData - Para girişi bilgileri
 * @param {number} tenantId - Kiracı ID
 * @returns {number} Eklenen işlemin ID'si
 */
exports.addCashDepositDB = async (depositData, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const {
      amount,
      reason,
      payment_type_id,
      created_by,
      cash_register_session_id
    } = depositData;

    // Ödeme işlemi olarak kaydet (transaction_type = 'deposit')
    const sql = `
      INSERT INTO payment_transactions
        (payment_type_id, amount, transaction_type, notes, created_by, cash_register_session_id, tenant_id)
      VALUES
        (?, ?, 'deposit', ?, ?, ?, ?)
    `;

    const [result] = await conn.query(sql, [
      payment_type_id,
      amount,
      reason,
      created_by,
      cash_register_session_id,
      tenantId
    ]);

    return result.insertId;
  } catch (error) {
    console.error("addCashDepositDB error:", error);
    throw error;
  } finally {
    conn.release();
  }
};

/**
 * Her kasa için son oturumu getirir
 * @param {number} tenantId - Kiracı ID
 * @returns {Array} Her kasa için son oturum bilgileri
 */
exports.getLastSessionsForCashRegistersDB = async (tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const sql = `
      SELECT
        crs.*,
        cr.name as register_name,
        cr.location as register_location,
        u1.name as opener_name,
        u2.name as closer_name,
        (SELECT COALESCE(SUM(
          CASE
            WHEN transaction_type = 'withdrawal' THEN -amount
            WHEN transaction_type = 'deposit' THEN amount
            ELSE amount
          END
        ), 0)
        FROM payment_transactions
        WHERE cash_register_session_id = crs.id
        AND status = 'completed'
        AND tenant_id = ?) as total_transactions
      FROM cash_register_sessions crs
      JOIN cash_registers cr ON crs.cash_register_id = cr.id
      JOIN users u1 ON crs.opened_by = u1.username
      LEFT JOIN users u2 ON crs.closed_by = u2.username
      WHERE crs.id IN (
        SELECT MAX(id)
        FROM cash_register_sessions
        WHERE tenant_id = ?
        GROUP BY cash_register_id
      )
      AND crs.tenant_id = ?
      ORDER BY cr.name ASC
    `;

    const [sessions] = await conn.query(sql, [tenantId, tenantId, tenantId]);

    // Get payment totals by payment type for each session
    for (const session of sessions) {
      // Regular payments by payment type
      const paymentTypesSql = `
        SELECT
          pt.payment_type_id,
          pt2.title as payment_type_name,
          COALESCE(SUM(pt.amount), 0) as total_amount
        FROM payment_transactions pt
        JOIN payment_types pt2 ON pt.payment_type_id = pt2.id
        WHERE pt.cash_register_session_id = ?
        AND pt.status = 'completed'
        AND pt.transaction_type = 'payment'
        AND pt.tenant_id = ?
        GROUP BY pt.payment_type_id, pt2.title
      `;

      // Withdrawals by payment type
      const withdrawalTypesSql = `
        SELECT
          pt.payment_type_id,
          pt2.title as payment_type_name,
          COALESCE(SUM(pt.amount), 0) as total_amount
        FROM payment_transactions pt
        JOIN payment_types pt2 ON pt.payment_type_id = pt2.id
        WHERE pt.cash_register_session_id = ?
        AND pt.status = 'completed'
        AND pt.transaction_type = 'withdrawal'
        AND pt.tenant_id = ?
        GROUP BY pt.payment_type_id, pt2.title
      `;

      // Deposits by payment type
      const depositTypesSql = `
        SELECT
          pt.payment_type_id,
          pt2.title as payment_type_name,
          COALESCE(SUM(pt.amount), 0) as total_amount
        FROM payment_transactions pt
        JOIN payment_types pt2 ON pt.payment_type_id = pt2.id
        WHERE pt.cash_register_session_id = ?
        AND pt.status = 'completed'
        AND pt.transaction_type = 'deposit'
        AND pt.tenant_id = ?
        GROUP BY pt.payment_type_id, pt2.title
      `;

      // Total withdrawals
      const totalWithdrawalsSql = `
        SELECT
          COALESCE(SUM(pt.amount), 0) as total_withdrawals,
          COUNT(*) as withdrawal_count
        FROM payment_transactions pt
        WHERE pt.cash_register_session_id = ?
        AND pt.status = 'completed'
        AND pt.transaction_type = 'withdrawal'
        AND pt.tenant_id = ?
      `;

      // Total deposits
      const totalDepositsSql = `
        SELECT
          COALESCE(SUM(pt.amount), 0) as total_deposits,
          COUNT(*) as deposit_count
        FROM payment_transactions pt
        WHERE pt.cash_register_session_id = ?
        AND pt.status = 'completed'
        AND pt.transaction_type = 'deposit'
        AND pt.tenant_id = ?
      `;

      const [paymentTotals] = await conn.query(paymentTypesSql, [session.id, tenantId]);
      const [withdrawalTotals] = await conn.query(withdrawalTypesSql, [session.id, tenantId]);
      const [depositTotals] = await conn.query(depositTypesSql, [session.id, tenantId]);
      const [totalWithdrawals] = await conn.query(totalWithdrawalsSql, [session.id, tenantId]);
      const [totalDeposits] = await conn.query(totalDepositsSql, [session.id, tenantId]);

      session.payment_totals_by_type = paymentTotals;
      session.withdrawal_totals_by_type = withdrawalTotals;
      session.deposit_totals_by_type = depositTotals;
      session.total_withdrawals = totalWithdrawals[0].total_withdrawals;
      session.withdrawal_count = totalWithdrawals[0].withdrawal_count;
      session.total_deposits = totalDeposits[0].total_deposits;
      session.deposit_count = totalDeposits[0].deposit_count;
    }

    return sessions;
  } catch (error) {
    console.error("getLastSessionsForCashRegistersDB error:", error);
    throw error;
  } finally {
    conn.release();
  }
};
