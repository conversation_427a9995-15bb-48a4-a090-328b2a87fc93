const { getAllMenuItemsDB } = require("./menu_item.service");
const OpenAI = require('openai');

const client = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY, // OpenAI API anahtarınızı ekleyin
});

/**
 * Menüye dayalı sorular ve öneriler oluşturur
 */
exports.generateQuestionsAndRecommendations = async (menuItems) => {
  try {
    const menuDescription = menuItems
      .map(
        (item) =>
          `- ${item.title} (${item.category}): ${item.description || "Açıklama yok"}. Fiyat: ${item.price}`
      )
      .join("\n");

    const prompt = `
Bir restoran menüsü var. Menü şu şekilde:
${menuDescription}

1. Bu menüyü analiz ederek, müşteriye sormak için 3 farklı soru öner. Sorular müşteri tercihlerini anlamaya yönelik olmalı.
2. Bu sorulara göre önerilebilecek yemek veya içecek seçeneklerini hazırla.

Çıktı formatı şu şekilde olmalı:
{
  "questions": [
    {"id": "q1", "question": "Birinci soru metni", "options": ["Seçenek 1", "Seçenek 2"]},
    {"id": "q2", "question": "İkinci soru metni", "options": ["Seçenek 1", "Seçenek 2"]}
  ],
  "recommendations": [
    {"title": "Yemek veya içecek adı", "reason": "Bu önerinin nedeni"}
  ]
}
    `;

    const response = await client.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [{ role: "user", content: prompt }],
    });

    const parsedResponse = JSON.parse(response.choices[0].message.content.trim());
    return parsedResponse;
  } catch (error) {
    console.error("Yapay zeka önerileri oluşturulurken hata:", error);
    return {
      questions: [],
      recommendations: [],
    };
  }
};

/**
 * Müşteri yanıtlarına göre öneriler oluşturur
 */
exports.getFilteredRecommendations = async (menuItems, userAnswers) => {
  try {
    const menuDescription = menuItems
      .map(
        (item) =>
          `- ${item.title} (${item.category}): ${item.description || "Açıklama yok"}. Fiyat: ${item.price}`
      )
      .join("\n");

    const answersDescription = Object.entries(userAnswers)
      .map(([questionId, answer]) => `- ${questionId}: ${answer}`)
      .join("\n");

    const prompt = `
Bir restoran menüsü var. Menü şu şekilde:
${menuDescription}

Müşterinin verdiği yanıtlar:
${answersDescription}

Bu yanıtlara dayanarak önerilebilecek yemek veya içecek seçeneklerini hazırla.

Çıktı formatı şu şekilde olmalı:
{
  "recommendations": [
    {"title": "Yemek veya içecek adı", "reason": "Bu önerinin nedeni"}
  ]
}
    `;

    const response = await client.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [{ role: "user", content: prompt }],
    });

    const parsedResponse = JSON.parse(response.choices[0].message.content.trim());
    return parsedResponse.recommendations;
  } catch (error) {
    console.error("Yanıtlara dayalı öneriler oluşturulurken hata:", error);
    return [];
  }
};
