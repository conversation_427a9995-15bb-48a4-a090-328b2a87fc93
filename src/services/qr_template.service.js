const { getMySqlPromiseConnection } = require("../config/mysql.db")

/**
 * Tüm aktif QR menü şablonlarını getir
 */
exports.getAllQRTemplatesDB = async () => {
    const conn = await getMySqlPromiseConnection();
    try {
        const sql = `
            SELECT 
                id,
                name,
                description,
                preview_image,
                is_default,
                template_config
            FROM qr_menu_templates 
            WHERE is_active = 1
            ORDER BY is_default DESC, name ASC
        `;
        
        const [results] = await conn.query(sql);
        return results;
    } catch (error) {
        console.error("QR templates sorgusunda hata:", error);
        throw error;
    } finally {
        conn.release();
    }
};

/**
 * Belirli bir şablonu ID ile getir
 */
exports.getQRTemplateByIdDB = async (templateId) => {
    const conn = await getMySqlPromiseConnection();
    try {
        const sql = `
            SELECT 
                id,
                name,
                description,
                preview_image,
                is_default,
                template_config
            FROM qr_menu_templates 
            WHERE id = ? AND is_active = 1
            LIMIT 1
        `;
        
        const [results] = await conn.query(sql, [templateId]);
        return results[0] || null;
    } catch (error) {
        console.error("QR template sorgusunda hata:", error);
        throw error;
    } finally {
        conn.release();
    }
};

/**
 * Varsayılan şablonu getir
 */
exports.getDefaultQRTemplateDB = async () => {
    const conn = await getMySqlPromiseConnection();
    try {
        const sql = `
            SELECT 
                id,
                name,
                description,
                preview_image,
                is_default,
                template_config
            FROM qr_menu_templates 
            WHERE is_default = 1 AND is_active = 1
            LIMIT 1
        `;
        
        const [results] = await conn.query(sql);
        return results[0] || null;
    } catch (error) {
        console.error("Default QR template sorgusunda hata:", error);
        throw error;
    } finally {
        conn.release();
    }
};

/**
 * Tenant için seçili şablonu getir
 */
exports.getTenantQRTemplateDB = async (tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {
        const sql = `
            SELECT 
                t.id,
                t.name,
                t.description,
                t.preview_image,
                t.is_default,
                t.template_config
            FROM tenant_config tc
            LEFT JOIN qr_menu_templates t ON tc.template_id = t.id
            WHERE tc.tenant_id = ? AND (t.is_active = 1 OR t.id IS NULL)
            LIMIT 1
        `;
        
        const [results] = await conn.query(sql, [tenantId]);
        
        // Eğer tenant'ın şablonu yoksa varsayılan şablonu döndür
        if (!results[0] || !results[0].id) {
            return await this.getDefaultQRTemplateDB();
        }
        
        return results[0];
    } catch (error) {
        console.error("Tenant QR template sorgusunda hata:", error);
        throw error;
    } finally {
        conn.release();
    }
};

/**
 * Tenant için şablon seç
 */
exports.setTenantQRTemplateDB = async (tenantId, templateId) => {
    const conn = await getMySqlPromiseConnection();
    try {
        // Önce şablonun var olduğunu kontrol et
        const template = await this.getQRTemplateByIdDB(templateId);
        if (!template) {
            throw new Error("Geçersiz şablon ID");
        }

        // tenant_config kaydının var olduğundan emin ol
        const insertSql = `
            INSERT INTO tenant_config (tenant_id, template_id)
            VALUES (?, ?)
            ON DUPLICATE KEY UPDATE template_id = VALUES(template_id)
        `;
        
        await conn.query(insertSql, [tenantId, templateId]);
        return true;
    } catch (error) {
        console.error("Tenant QR template güncelleme hatası:", error);
        throw error;
    } finally {
        conn.release();
    }
};

/**
 * Yeni şablon oluştur (admin için)
 */
exports.createQRTemplateDB = async (name, description, templateConfig, previewImage = null) => {
    const conn = await getMySqlPromiseConnection();
    try {
        const sql = `
            INSERT INTO qr_menu_templates (name, description, preview_image, template_config)
            VALUES (?, ?, ?, ?)
        `;
        
        const [result] = await conn.query(sql, [name, description, previewImage, JSON.stringify(templateConfig)]);
        return result.insertId;
    } catch (error) {
        console.error("QR template oluşturma hatası:", error);
        throw error;
    } finally {
        conn.release();
    }
};

/**
 * Şablon güncelle (admin için)
 */
exports.updateQRTemplateDB = async (templateId, name, description, templateConfig, previewImage = null) => {
    const conn = await getMySqlPromiseConnection();
    try {
        const sql = `
            UPDATE qr_menu_templates 
            SET name = ?, description = ?, preview_image = ?, template_config = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        `;
        
        await conn.query(sql, [name, description, previewImage, JSON.stringify(templateConfig), templateId]);
        return true;
    } catch (error) {
        console.error("QR template güncelleme hatası:", error);
        throw error;
    } finally {
        conn.release();
    }
};

/**
 * Şablon sil (admin için)
 */
exports.deleteQRTemplateDB = async (templateId) => {
    const conn = await getMySqlPromiseConnection();
    try {
        // Varsayılan şablonu silemez
        const checkSql = `SELECT is_default FROM qr_menu_templates WHERE id = ?`;
        const [checkResult] = await conn.query(checkSql, [templateId]);
        
        if (checkResult[0]?.is_default) {
            throw new Error("Varsayılan şablon silinemez");
        }

        const sql = `UPDATE qr_menu_templates SET is_active = 0 WHERE id = ?`;
        await conn.query(sql, [templateId]);
        return true;
    } catch (error) {
        console.error("QR template silme hatası:", error);
        throw error;
    } finally {
        conn.release();
    }
};
