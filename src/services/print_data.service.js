const { getMySqlPromiseConnection } = require("../config/mysql.db");
const { encrypt } = require('../config/crypto');


exports.savePrintDataKitchenDB = async (tenantId, orderData) => {
  const conn = await getMySqlPromiseConnection();
  try {
    // Sipariş hangi kattan geldi? (tableID'den floor ID'yi bul)
    let floorId = null;
    if (orderData.tableID) {
      const [tableResult] = await conn.query(
        `SELECT floor FROM store_tables WHERE id = ? AND tenant_id = ? LIMIT 1`,
        [orderData.tableID, tenantId]
      );
      if (tableResult.length > 0) {
        floorId = tableResult[0].floor;
      }
    }

    // Kategori bazlı printer grupları oluştur
    const printerGroups = {};

    for (const item of orderData.items) {
      // Ürünün kategorisini al
      const categoryId = item.categoryId || null;

      // Öncelikle floor mapping üzerinden yazıcı yolunu bulmaya çalış
      let printerPath = null;

      // Eğer floorId varsa, floor ve category ID'ye göre yazıcıyı bul
      if (floorId) {
        printerPath = await getKitchenPrinterPath(tenantId, conn, floorId, categoryId);
      }

      // Eğer floor mapping üzerinden yazıcı bulunamazsa, item'daki printerPath'i kullan
      if (!printerPath || printerPath === "//localhost/printer") {
        printerPath = item.printerPath || "//localhost/printer";
      }

      if (!printerGroups[printerPath]) {
        printerGroups[printerPath] = [];
      }
      printerGroups[printerPath].push(item);
    }

    for (const [printerPath, items] of Object.entries(printerGroups)) {

      const orderDataForPrinter = { ...orderData, items };
      const xmlContent = generatePrintXML(orderDataForPrinter, printerPath, true);

      await savePrintData(tenantId, xmlContent, conn);
    }

    return true;
  } catch (error) {
    console.error("🚨 Mutfak fişi yazdırma hatası:", error);
    throw error;
  } finally {
    if (conn) conn.release();
  }
};

async function savePrintData(tenantId, xmlContent, conn) {
  try {
    const query = `INSERT INTO print_data (tenant_id, content, status) VALUES (?, ?, ?)`;
    const [result] = await conn.execute(query, [tenantId, xmlContent, 2]);

    if (global.io) {
      try {
        global.io.to(tenantId).emit('print-order', [{
          id: result.insertId,
          content: xmlContent,
          status: 2
        }]);
      } catch (socketError) {
        console.error('Socket gönderim hatası:', socketError);
      }
    } else {
    }
  } catch (error) {
    console.error("🚨 Veritabanına yazdırma verisi kaydedilirken hata:", error);
    throw error;
  }
}

exports.savePrintDataRecipetnDB = async (tenantId, orderData) => {
  const conn = await getMySqlPromiseConnection();
  try {
    // Sipariş hangi kattan geldi? (tableID'den floor ID'yi bul)
    let floorId = orderData.floorId || null;

    // Eğer floorId yoksa ve tableID varsa, tableID'den floor ID'yi bul
    if (!floorId && orderData.tableID) {
      const [tableResult] = await conn.query(
        `SELECT floor FROM store_tables WHERE id = ? AND tenant_id = ? LIMIT 1`,
        [orderData.tableID, tenantId]
      );
      if (tableResult.length > 0) {
        floorId = tableResult[0].floor;
      }
    }

    // Eğer floorId yoksa ve table_id varsa, table_id'den floor ID'yi bul
    if (!floorId && orderData.table_id) {
      const [tableResult] = await conn.query(
        `SELECT floor FROM store_tables WHERE id = ? AND tenant_id = ? LIMIT 1`,
        [orderData.table_id, tenantId]
      );
      if (tableResult.length > 0) {
        floorId = tableResult[0].floor;
      }
    }

    const cashierPrinterPath = await getRecipetPrinterPath(tenantId, conn, floorId);

    let orderId = orderData.order_id || orderData.id || orderData.orderId;

    if (Array.isArray(orderId) && orderId.length > 0) {
      orderId = orderId[0];
    }

    // Tek bir XML oluştur (QR kodu ve normal fiş birleşik olarak)
    const orderDataForRecipet = { ...orderData };
    // orderId varsa QR kod bilgisini ekle, yoksa QR kod oluşturma
    const withQRCode = orderId ? false : false;
    const xmlContent = generatePrintXML(orderDataForRecipet, cashierPrinterPath, false, withQRCode, orderId);

    // Yazdırma verisini kaydet
    await savePrintData(tenantId, xmlContent, conn);

    // **Eğer siparişin bir masaya bağlı olduğu belirtilmişse, masanın status değerini "locked" yap**
    if (orderData.table_id) {
      await lockTableStatus(tenantId, orderData.table_id, conn);
    }

    return true;
  } catch (error) {
    console.error("🚨 Hesap fişi yazdırma hatası:", error);
    throw error;
  } finally {
    if (conn) conn.release();
  }
};


async function lockTableStatus(tenantId, tableId, conn) {
  try {
    const query = `UPDATE store_tables SET table_status = 'locked' WHERE id = ? AND tenant_id = ?`;
    await conn.execute(query, [tableId, tenantId]);
  } catch (error) {
    console.error(`🚨 Masa status güncellenirken hata oluştu (table_id: ${tableId}):`, error);
  }
}

async function getRecipetPrinterPath(tenantId, conn, floorId = null) {
  try {
    let query;
    let params;

    if (floorId) {
      query = `
        SELECT p.path
        FROM floor_printer_mappings fpm
        JOIN printers p ON fpm.printer_id = p.id
        WHERE fpm.tenant_id = ?
        AND fpm.floor_id = ?
        AND fpm.printer_type = 'receipt'
        LIMIT 1
      `;
      params = [tenantId, floorId];
    } else {
      // Kat ID'si belirtilmemişse, varsayılan kasiyer yazıcısını kullan
      query = `SELECT path FROM printers WHERE tenant_id = ? AND type = 'cashier' LIMIT 1`;
      params = [tenantId];
    }

    const [rows] = await conn.execute(query, params);

    return rows.length > 0 ? rows[0].path : "//localhost/printer"; // Eğer yazıcı yoksa varsayılan kullanılır
  } catch (error) {
    console.error("Yazıcı yolu alınırken hata:", error);
    return "//localhost/printeer"; // Hata olursa varsayılan değeri döndür
  }
}



async function getKitchenPrinterPath(tenantId, conn, floorId = null, categoryId = null) { 
  try {
    let query;
    let params;

    // Eğer floor ID belirtilmişse
    if (floorId) {
      // Önce floor mapping'i kontrol et
      query = `
        SELECT p.path
        FROM floor_printer_mappings fpm
        JOIN printers p ON fpm.printer_id = p.id
        WHERE fpm.tenant_id = ?
        AND fpm.floor_id = ?
        AND fpm.category_id IS NULL
        AND fpm.printer_type = 'kitchen'
        LIMIT 1
      `;
      params = [tenantId, floorId];
      
      const [floorRows] = await conn.execute(query, params);
      
      // Floor mapping bulunursa, onu döndür
      if (floorRows.length > 0) {
        return floorRows[0].path;
      }
      
      // Floor mapping bulunamazsa ve categoryId varsa, kategori mapping'ini kontrol et
      if (categoryId) {
        // Kategoriyi bul ve printer_id'sini al
        query = `
          SELECT p.path
          FROM categories c
          JOIN printers p ON c.printer_id = p.id
          WHERE c.id = ?
          AND c.tenant_id = ?
          AND c.printer_id IS NOT NULL
          LIMIT 1
        `;
        params = [categoryId, tenantId];
        
        const [categoryRows] = await conn.execute(query, params);
        
        if (categoryRows.length > 0) {
          return categoryRows[0].path;
        }
      }
    }
    
    // Hiçbiri bulunamazsa, varsayılan mutfak yazıcısını kullan
    query = `SELECT path FROM printers WHERE tenant_id = ? AND type = 'kitchen' LIMIT 1`;
    params = [tenantId];
    
    const [rows] = await conn.execute(query, params);
    
    // Yazıcı bulunamazsa null döndür
    return rows.length > 0 ? rows[0].path : null;
    
  } catch (error) {
    console.error("Yazıcı yolu alınırken hata:", error);
    return null; // Hata olursa null döndür
  }
}

exports.getPrintDataService = async (tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const query = 'SELECT * FROM print_data WHERE tenant_id = ? AND status = 2';
    const [rows] = await conn.execute(query, [tenantId]);
    return rows;
  } catch (error) {
    console.error("Yazdırma verileri alınırken hata:", error);
    throw error;
  } finally {
    if (conn) conn.release();
  }
};

exports.updatePrintStatusService = async (printId, status) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const query = 'UPDATE print_data SET status = ? WHERE id = ?';
    await conn.execute(query, [status, printId]);
    return true;
  } catch (error) {
    console.error("Yazdırma durumu güncellenirken hata:", error);
    throw error;
  } finally {
    if (conn) conn.release();
  }
};

function generatePrintXML(orderData, printerPath, isKitchen = false, includeQRCode = false, orderId = null) {
  // Türkiye saati için tarih ve saat oluştur
  const now = new Date();
  // Sunucu zaten Türkiye saatinde çalışıyorsa, ekstra saat eklemeye gerek yok
  const tarih = now.toLocaleDateString('tr-TR', { timeZone: 'Europe/Istanbul' });
  const saat = now.toLocaleTimeString('tr-TR', { timeZone: 'Europe/Istanbul' });

  // QR kod bilgisi (receipt fişinde ve orderId varsa göster)
  let qrCodeTag = "";

  if (!isKitchen && includeQRCode && orderId) {
    qrCodeTag = `
  <paper-cut></paper-cut>
  <align mode="center">
    <line-feed></line-feed>
    <qr-code>${encrypt(orderId)}</qr-code>
    <line-feed></line-feed>
    <text-line>Yaptığınız harcamalardan Holly Puan kazanmak, masanızdan sipariş verip ödeme yapabilmek, Holly Chat ile sohbet edebilmek, kampanyalardan yararlanabilmek ve çok daha fazlası için Holly Stone uygulamasını indirip QR kodu okutabilirsiniz.</text-line>
  </align>`
  ;
  }

  // Müşteri bilgisi kontrolü (Boşsa veya undefined ise gösterme)
  let customerInfo = "";
  if (!isKitchen && orderData.customer) {
      const { name, address, phone } = orderData.customer;

      const validAddress = address && address !== "undefined" ? `Adres: ${address}` : "";
      const validPhone = phone && phone !== "undefined" ? `(${phone})` : "";

      if (name || validAddress || validPhone) {
          customerInfo = `<text-line>Müşteri: ${name || ''} ${validAddress} ${validPhone}</text-line>`;
      }
  }



  return `<?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <characterset>PC857_TURKISH</characterset>
  <interface>${printerPath}</interface>
  <type>epson</type>
  <line-character>-</line-character>
</configuration>
<document>
  <align mode="center">
    <logo/>
  </align>
  <line-feed></line-feed>
  <align mode="left">
    <text-line>Tarih  : ${tarih}:${saat}</text-line>
    <text-line>Masa   : ${orderData.table || ''}</text-line>
    <text-line>Garson : ${orderData.serverName || ''}</text-line>
    <text-line>Sipariş Numarası : ${Array.isArray(orderData.orderId) ? orderData.orderId[0] : orderData.orderId || ''}</text-line>
    ${isKitchen ? "" : customerInfo}
  </align>
  <line-feed></line-feed>
  <text>
    <text-line>Ürünler</text-line>
    <text-line>------------------------------------------------</text-line>
    ${orderData.items.map(item => {
      const maxLength = 30; // Ürün adının maksimum karakter uzunluğu
      const title = item.title;
      const firstLine = title.slice(0, maxLength); // İlk 23 karakter
      const secondLine = title.length > maxLength ? title.slice(maxLength) : ""; // Fazlalık varsa yeni satıra taşı

      // Adet ve fiyatı aynı satıra yaz
      const quantityAndPrice = `x${item.quantity}${isKitchen ? "" : ` - ${(item.price * item.quantity).toFixed(2)} TL`}`;

      return `
      <bold><text-line>${firstLine.padEnd(maxLength)} ${quantityAndPrice}</text-line></bold>
      ${secondLine ? `<bold><text-line>${secondLine}</text-line></bold>` : ""}
      ${item.variant ? `    <text-line>  * ${item.variant.title}</text-line>` : ''}
      ${item.addons && item.addons.length > 0 ? item.addons.map(addon =>
        `    <text-line>  * ${addon.title}</text-line>`).join('\n') : ''}
      ${item.notes ? `    <text-line>Not: ${item.notes}</text-line>` : ''}`;
  }).join('\n')}
      <text-line>------------------------------------------------</text-line>
  </text>


${!isKitchen ? `
  <text>
    <text-line>------------------------------------------------</text-line>
    ${orderData.subtotal ? `<text-line>Ara Toplam: ${orderData.subtotal.toFixed(2)} TL</text-line>` : ''}
    ${orderData.discountTotal && orderData.discountTotal > 0 ? `<text-line>İndirim: -${orderData.discountTotal.toFixed(2)} TL</text-line>` : ''}
    ${orderData.taxTotal && orderData.taxTotal > 0 ? `<text-line>KDV: ${orderData.taxTotal.toFixed(2)} TL</text-line>` : ''}
    ${orderData.totalPaid && orderData.totalPaid > 0 ? `<text-line>Geçmiş Ödeme: ${orderData.totalPaid.toFixed(2)} TL</text-line>` : ''}
    <text-line>------------------------------------------------</text-line>
  </text>

   <align mode="center">
  <double-height>
  <double-width>
  <text>
    <bold>
      <text-line>Toplam : ${(orderData.paymentTotal || orderData.subtotal || 0).toFixed(2)} TL</text-line>
    </bold>
  </text>
    </double-width>
</double-height>
  </align>

  <line-feed></line-feed>
  <text>
    <bold>
      <text-line>Bizi tercih ettiğniiz için teşekkür ederiz....</text-line>
    </bold>
  </text>
` : ''}
${!isKitchen && orderData.paymentMethod ? `
  <text>
    <text-line>Ödeme Türü: ${orderData.paymentMethod}</text-line>
  </text>
` : ''}
  ${qrCodeTag}
  <paper-cut></paper-cut>
  <beep></beep>
<beep></beep>

</document>`;
}

exports.savePrintDataReportDB = async (tenantId, reportData) => {
  const conn = await getMySqlPromiseConnection();
  try {
    // Kasiyer yazıcısının yolunu al
    const cashierPrinterPath = await getRecipetPrinterPath(tenantId, conn);

    const xmlContent = generateReportPrintXML(reportData, cashierPrinterPath);

    // Yazdırma verisini kaydet
    await savePrintData(tenantId, xmlContent, conn);

    return true;
  } catch (error) {
    console.error("🚨 Rapor yazdırma hatası:", error);
    throw error;
  } finally {
    if (conn) conn.release();
  }
};

function generateReportPrintXML(reportData, printerPath) {
  // Türkiye saati için tarih ve saat oluştur
  const { getTurkeyDateTime } = require("../utils/date.helper");
  const turkeyDateTime = getTurkeyDateTime();
  const [tarihPart, saatPart] = turkeyDateTime.split(' ');
  const tarih = new Date(tarihPart).toLocaleDateString('tr-TR');
  const saat = saatPart;

  // Rapor verilerini formatla
  const {
    cancelledOrdersAndItems,
    netRevenue,
    revenueTotal,
    creditTotal,
    getTotalDiscounts,
    wasteTotal,
    complimentaryTotal,
    totalPaymentsByPaymentTypes,
    averageOrderValue
  } = reportData;

  // Sayıları formatla - binlik ayıraçları ekle
  const formatNumber = (num) => {
    return parseFloat(num).toLocaleString('tr-TR', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });
  };

  // Ödeme tiplerini formatla
  const paymentTypesText = totalPaymentsByPaymentTypes.map(pt =>
    `<text-line>${pt.title}: ${formatNumber(pt.total)} TL</text-line>`
  ).join('\n');

  // İptal edilen siparişleri formatla
  let cancelledItemsText = '';
  if (cancelledOrdersAndItems && cancelledOrdersAndItems.length > 0) {
    cancelledItemsText = `
    <text-line>İptal Edilen Ürünler:</text-line>
    <text-line>------------------------------------------------</text-line>
    ${cancelledOrdersAndItems.map(item =>
      `<text-line>${item.item_title} x${item.quantity} - ${formatNumber(item.total_price)} TL</text-line>`
    ).join('\n')}
    <text-line>------------------------------------------------</text-line>
    `;
  }



  return `<?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <characterset>PC857_TURKISH</characterset>
  <interface>${printerPath}</interface>
  <type>epson</type>
  <line-character>-</line-character>
</configuration>
<document>
  <align mode="center">
    <logo/>
    <double-height>
      <double-width>
        <bold>
          <text-line size="5">Günlük Rapor</text-line>
        </bold>
      </double-width>
    </double-height>
  </align>
  <line-feed></line-feed>
  <align mode="left">
    <text-line>Tarih  : ${tarih} ${saat}</text-line>
  </align>
  <line-feed></line-feed>
  <text>
    <text-line>Finansal Özet</text-line>
    <text-line>------------------------------------------------</text-line>
    <text-line>Net Satış: ${formatNumber(netRevenue)} TL</text-line>
    <text-line>Toplam Satış: ${formatNumber(revenueTotal)} TL</text-line>
    <text-line>Ortalama Sipariş Değeri: ${formatNumber(averageOrderValue)} TL</text-line>
    <text-line>Kredili Satış: ${formatNumber(creditTotal)} TL</text-line>
    <text-line>Toplam İndirim: ${formatNumber(getTotalDiscounts)} TL</text-line>
    <text-line>Zayi Toplamı: ${formatNumber(wasteTotal)} TL</text-line>
    <text-line>İkram Toplamı: ${formatNumber(complimentaryTotal)} TL</text-line>
    <text-line>------------------------------------------------</text-line>
  </text>

  <text>
    <text-line>Ödeme Tipleri</text-line>
    <text-line>------------------------------------------------</text-line>
    ${paymentTypesText}
    <text-line>------------------------------------------------</text-line>
  </text>

  ${cancelledItemsText}

  <align mode="center">
    <text-line>*** Rapor Sonu ***</text-line>
  </align>
  <paper-cut></paper-cut>
  <beep></beep>
</document>`;
}

/**
 * mTSM Ödeme Print Data Kaydet
 */
exports.savePrintDataMTSMPaymentDB = async (tenantId, paymentData, printerPath) => {
  const conn = await getMySqlPromiseConnection();
  try {
    // mTSM ödeme print XML'i oluştur
    const printXML = generateMTSMPaymentPrintXML(paymentData, printerPath);

    // Print data'yı veritabanına kaydet (mevcut tablo yapısına uygun)
    const insertQuery = `INSERT INTO print_data (tenant_id, content, status) VALUES (?, ?, ?)`;
    await conn.query(insertQuery, [tenantId, printXML, 2]);

    // Socket ile print bildirimi gönder
    if (global.io) {
      try {
        global.io.to(tenantId).emit('print-order', [{
          id: Date.now(), // Geçici ID
          content: printXML,
          status: 2,
          tenant_id: tenantId
        }]);
      } catch (socketError) {
        console.error('Print socket hatası:', socketError);
      }
    }


  } catch (error) {
    console.error("🚨 mTSM ödeme print data kaydedilirken hata:", error);
    throw error;
  } finally {
    conn.release();
  }
};

/**
 * mTSM Ödeme Print XML oluştur
 */
function generateMTSMPaymentPrintXML(paymentData, printerPath) {
  // UTC+3 Türkiye saati için tarih ve saat oluştur
  const now = new Date();
  const turkeyTime = new Date(now.getTime() + (3 * 60 * 60 * 1000)); // UTC+3
  const tarih = turkeyTime.toLocaleDateString('tr-TR');
  const saat = turkeyTime.toLocaleTimeString('tr-TR', { hour12: false });

  return `<?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <characterset>PC857_TURKISH</characterset>
  <interface>${printerPath}</interface>
  <type>epson</type>
  <line-character>-</line-character>
</configuration>
<document>
  <align mode="center">
    <logo/>
    <bold><text-line>ÖDEME ALINDI</text-line></bold>
  </align>
  <line-feed></line-feed>
  <align mode="left">
    <text-line>Tarih  : ${tarih}</text-line>
    <text-line>Saat   : ${saat}</text-line>
    <text-line>Masa   : ${paymentData.tableName || 'Paket Sipariş'}</text-line>
    <text-line>Sipariş No : ${paymentData.orderId}</text-line>
    <text-line>Garson : ${paymentData.serverName || 'Sistem'}</text-line>
  </align>
  <line-feed></line-feed>
  <text>
    <text-line>Ödeme Detayları</text-line>
    <text-line>------------------------------------------------</text-line>
    ${paymentData.payments.map(payment =>
      `<text-line>${payment.type.padEnd(20)} ${payment.amount.toFixed(2).padStart(10)} TL</text-line>`
    ).join('')}
    <text-line>------------------------------------------------</text-line>
    <bold><text-line>TOPLAM ÖDEME${paymentData.totalAmount.toFixed(2).padStart(20)} TL</text-line></bold>
  </text>
  <line-feed></line-feed>
  <align mode="center">
    <text-line>*** Ödeme Tamamlandı ***</text-line>
    <text-line>Teşekkür Ederiz</text-line>
  </align>
  <paper-cut></paper-cut>
  <beep></beep>
</document>`;
}
