const { getMySqlPromiseConnection } = require("../config/mysql.db");

// Çevirileri getir
exports.getTranslationsDB = async (tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const sql = `
      SELECT id, object_type, object_id, language_code, translation, created_at, updated_at
      FROM translations
      WHERE tenant_id = ?
      ORDER BY language_code ASC
    `;
    const [result] = await conn.query(sql, [tenantId]);
    return result;
  } catch (error) {
    console.error("getTranslationsDB hata:", error);
    throw error;
  } finally {
    conn.release();
  }
};

// Tek bir çeviri ekle veya güncelle
exports.addOrUpdateTranslationDB = async (tenantId, objectType, objectId, languageCode, translation) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const sql = `
      INSERT INTO translations (tenant_id, object_type, object_id, language_code, translation)
      VALUES (?, ?, ?, ?, ?)
      ON DUPLICATE KEY UPDATE
        translation = VALUES(translation),
        updated_at = CURRENT_TIMESTAMP
    `;
    const [result] = await conn.query(sql, [tenantId, objectType, objectId, languageCode, translation]);
    return result.insertId || 0;
  } catch (error) {
    console.error("addOrUpdateTranslationDB hata:", error);
    throw error;
  } finally {
    conn.release();
  }
};

// Çoklu çeviri ekle veya güncelle (transaction içinde)
exports.addOrUpdateMultipleTranslationsDB = async (tenantId, objectType, objectId, translations) => {
  const conn = await getMySqlPromiseConnection();
  try {
    await conn.beginTransaction();
    
    const results = [];
    
    for (const [languageCode, translation] of Object.entries(translations)) {
      if (translation && translation.trim() !== '') {
        const sql = `
          INSERT INTO translations (tenant_id, object_type, object_id, language_code, translation)
          VALUES (?, ?, ?, ?, ?)
          ON DUPLICATE KEY UPDATE
            translation = VALUES(translation),
            updated_at = CURRENT_TIMESTAMP
        `;
        
        const [result] = await conn.query(sql, [tenantId, objectType, objectId, languageCode, translation]);
        results.push({ 
          language_code: languageCode, 
          id: result.insertId || 0,
          affected_rows: result.affectedRows 
        });
      }
    }
    
    await conn.commit();
    return results;
  } catch (error) {
    await conn.rollback();
    console.error("addOrUpdateMultipleTranslationsDB hata:", error);
    throw error;
  } finally {
    conn.release();
  }
};

// Çeviri sil
exports.deleteTranslationDB = async (id) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const sql = `
      DELETE FROM translations
      WHERE id = ?
    `;
    await conn.query(sql, [id]);
    return;
  } catch (error) {
    console.error("deleteTranslationDB hata:", error);
    throw error;
  } finally {
    conn.release();
  }
};

// Belirli bir nesne için tüm çevirileri sil
exports.deleteAllTranslationsForObjectDB = async (tenantId, objectType, objectId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const sql = `
      DELETE FROM translations
      WHERE tenant_id = ? AND object_type = ? AND object_id = ?
    `;
    const [result] = await conn.query(sql, [tenantId, objectType, objectId]);
    return result.affectedRows;
  } catch (error) {
    console.error("deleteAllTranslationsForObjectDB hata:", error);
    throw error;
  } finally {
    conn.release();
  }
};

exports.getTranslationsByTenant = async (tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const sql = `
      SELECT object_type, object_id, language_code, translation
      FROM translations
      WHERE tenant_id = ?
    `;
    const [result] = await conn.query(sql, [tenantId]);
    return result;
  } catch (error) {
    console.error("getTranslationsByTenant hata:", error);
    throw error;
  } finally {
    conn.release();
  }
};