const { getMySqlPromiseConnection } = require("../config/mysql.db");

exports.addRecipeDB = async ({
    menuItemId,
    name,
    portionSize,
    portionUnit,
    preparationTime,
    cookingTime,
    instructions,
    ingredients,
    tenantId,
    userId
}) => {
    const conn = await getMySqlPromiseConnection();
    try {
        await conn.beginTransaction();

        // Ana reçete kaydı
        const [recipeResult] = await conn.query(
            `INSERT INTO recipes (
                menu_item_id, name, portion_size, portion_unit,
                preparation_time, cooking_time, instructions,
                tenant_id, created_by
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
            [
                menuItemId, name, portionSize, portionUnit,
                preparationTime, cookingTime, instructions,
                tenantId, userId
            ]
        );

        const recipeId = recipeResult.insertId;
        let totalCost = 0;

        // Malzemeleri ekle
        for (const ingredient of ingredients) {
            // Malzeme maliyetini hesapla
            const [ingredientData] = await conn.query(
                `SELECT cost_price FROM ingredients WHERE id = ? AND tenant_id = ?`,
                [ingredient.ingredientId, tenantId]
            );

            const itemCost = ingredientData[0].cost_price * ingredient.quantity * 
                           (1 + (ingredient.wastePercentage || 0) / 100) *
                           (1 + (ingredient.cookingLossPercentage || 0) / 100);

            totalCost += itemCost;

            // Malzemeyi reçeteye ekle
            await conn.query(
                `INSERT INTO recipe_items (
                    recipe_id, ingredient_id, quantity, unit,
                    is_optional, waste_percentage, cooking_loss_percentage,
                    notes, cost, tenant_id
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                [
                    recipeId,
                    ingredient.ingredientId,
                    ingredient.quantity,
                    ingredient.unit,
                    ingredient.isOptional || false,
                    ingredient.wastePercentage || 0,
                    ingredient.cookingLossPercentage || 0,
                    ingredient.notes,
                    itemCost,
                    tenantId
                ]
            );
        }

        // Toplam maliyeti güncelle
        await conn.query(
            `UPDATE recipes SET cost_price = ? WHERE id = ? AND tenant_id = ?`,
            [totalCost, recipeId, tenantId]
        );

        // Menü ürününün maliyetini güncelle
        await conn.query(
            `UPDATE menu_items SET cost_price = ? WHERE id = ? AND tenant_id = ?`,
            [totalCost, menuItemId, tenantId]
        );

        await conn.commit();
        return { recipeId, totalCost };
    } catch (error) {
        await conn.rollback();
        throw error;
    } finally {
        conn.release();
    }
};

exports.getAllRecipesDB = async (tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {
        // Ana reçete bilgileri
        const [recipes] = await conn.query(
            `SELECT r.*, 
                    m.title as menu_item_name,
                    COUNT(ri.id) as ingredient_count
             FROM recipes r
             JOIN menu_items m ON r.menu_item_id = m.id
             LEFT JOIN recipe_items ri ON r.id = ri.recipe_id
             WHERE r.tenant_id = ?
             GROUP BY r.id
             ORDER BY r.name ASC`,
            [tenantId]
        );

        return recipes;
    } finally {
        conn.release();
    }
};

exports.deleteRecipeDB = async (recipeId, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {
        await conn.beginTransaction();

        // Reçete malzemelerini sil
        await conn.query(
            `DELETE FROM recipe_items 
             WHERE recipe_id = ? AND tenant_id = ?`,
            [recipeId, tenantId]
        );

        // Maliyet geçmişini sil
        await conn.query(
            `DELETE FROM recipe_cost_history 
             WHERE recipe_id = ? AND tenant_id = ?`,
            [recipeId, tenantId]
        );

        // Reçeteyi sil
        await conn.query(
            `DELETE FROM recipes 
             WHERE id = ? AND tenant_id = ?`,
            [recipeId, tenantId]
        );

        await conn.commit();
    } catch (error) {
        await conn.rollback();
        throw error;
    } finally {
        conn.release();
    }
};

exports.updateRecipeDB = async (recipeId, {
    name,
    portionSize,
    portionUnit,
    preparationTime,
    cookingTime,
    instructions,
    ingredients,
    tenantId
}) => {
    const conn = await getMySqlPromiseConnection();
    try {
        await conn.beginTransaction();

        // Reçete bilgilerini güncelle
        await conn.query(
            `UPDATE recipes SET
                name = ?, portion_size = ?, portion_unit = ?,
                preparation_time = ?, cooking_time = ?,
                instructions = ?, updated_at = CURRENT_TIMESTAMP
             WHERE id = ? AND tenant_id = ?`,
            [
                name, portionSize, portionUnit,
                preparationTime, cookingTime, instructions,
                recipeId, tenantId
            ]
        );

        // Mevcut malzemeleri sil
        await conn.query(
            `DELETE FROM recipe_items WHERE recipe_id = ? AND tenant_id = ?`,
            [recipeId, tenantId]
        );

        let totalCost = 0;

        // Yeni malzemeleri ekle
        for (const ingredient of ingredients) {
            const [ingredientData] = await conn.query(
                `SELECT cost_price FROM ingredients WHERE id = ? AND tenant_id = ?`,
                [ingredient.ingredientId, tenantId]
            );

            const itemCost = ingredientData[0].cost_price * ingredient.quantity * 
                           (1 + (ingredient.wastePercentage || 0) / 100) *
                           (1 + (ingredient.cookingLossPercentage || 0) / 100);

            totalCost += itemCost;

            await conn.query(
                `INSERT INTO recipe_items (
                    recipe_id, ingredient_id, quantity, unit,
                    is_optional, waste_percentage, cooking_loss_percentage,
                    notes, cost, tenant_id
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                [
                    recipeId,
                    ingredient.ingredientId,
                    ingredient.quantity,
                    ingredient.unit,
                    ingredient.isOptional || false,
                    ingredient.wastePercentage || 0,
                    ingredient.cookingLossPercentage || 0,
                    ingredient.notes,
                    itemCost,
                    tenantId
                ]
            );
        }

        // Reçete maliyetini güncelle
        await conn.query(
            `UPDATE recipes SET cost_price = ? WHERE id = ? AND tenant_id = ?`,
            [totalCost, recipeId, tenantId]
        );

        // Maliyet geçmişini kaydet
        const [currentCost] = await conn.query(
            `SELECT cost_price FROM recipes WHERE id = ? AND tenant_id = ?`,
            [recipeId, tenantId]
        );

        if (currentCost[0].cost_price !== totalCost) {
            await conn.query(
                `INSERT INTO recipe_cost_history (
                    recipe_id, previous_cost, new_cost, tenant_id
                ) VALUES (?, ?, ?, ?)`,
                [recipeId, currentCost[0].cost_price, totalCost, tenantId]
            );
        }

        // Menü ürününün maliyetini güncelle
        const [menuItemId] = await conn.query(
            `SELECT menu_item_id FROM recipes WHERE id = ? AND tenant_id = ?`,
            [recipeId, tenantId]
        );

        await conn.query(
            `UPDATE menu_items SET cost_price = ? WHERE id = ? AND tenant_id = ?`,
            [totalCost, menuItemId[0].menu_item_id, tenantId]
        );

        await conn.commit();
        return totalCost;
    } catch (error) {
        await conn.rollback();
        throw error;
    } finally {
        conn.release();
    }
};

exports.getRecipeDetailsDB = async (recipeId, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {
        // Ana reçete bilgileri
        const [recipe] = await conn.query(
            `SELECT r.*, m.title as menu_item_name
             FROM recipes r
             JOIN menu_items m ON r.menu_item_id = m.id
             WHERE r.id = ? AND r.tenant_id = ?`,
            [recipeId, tenantId]
        );

        if (!recipe.length) {
            throw new Error('Reçete bulunamadı');
        }

        // Reçete malzemeleri
        const [ingredients] = await conn.query(
            `SELECT ri.*, i.title as ingredient_name, i.cost_price as current_cost
             FROM recipe_items ri
             JOIN ingredients i ON ri.ingredient_id = i.id
             WHERE ri.recipe_id = ? AND ri.tenant_id = ?`,
            [recipeId, tenantId]
        );

        // Maliyet geçmişi
        const [costHistory] = await conn.query(
            `SELECT * FROM recipe_cost_history
             WHERE recipe_id = ? AND tenant_id = ?
             ORDER BY calculation_date DESC LIMIT 10`,
            [recipeId, tenantId]
        );

        return {
            ...recipe[0],
            ingredients,
            costHistory
        };
    } finally {
        conn.release();
    }
};

exports.checkRecipeStockDB = async (recipeId, quantity, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {
        const [ingredients] = await conn.query(
            `SELECT 
                ri.ingredient_id,
                ri.quantity * ? as needed_quantity,
                i.stock as available_stock,
                i.title as ingredient_name
             FROM recipe_items ri
             JOIN ingredients i ON ri.ingredient_id = i.id
             WHERE ri.recipe_id = ? AND ri.tenant_id = ?`,
            [quantity, recipeId, tenantId]
        );

        return ingredients.map(item => ({
            ingredientId: item.ingredient_id,
            ingredientName: item.ingredient_name,
            neededQuantity: item.needed_quantity,
            availableStock: item.available_stock,
            isAvailable: item.available_stock >= item.needed_quantity,
            shortage: Math.max(0, item.needed_quantity - item.available_stock)
        }));
    } finally {
        conn.release();
    }
};

exports.processRecipeStockDB = async (recipeId, quantity, referenceType, referenceId, tenantId, userId) => {
    const conn = await getMySqlPromiseConnection();
    try {
        await conn.beginTransaction();

        // Stok kontrolü
        const stockCheck = await this.checkRecipeStockDB(recipeId, quantity, tenantId);
        const insufficientStock = stockCheck.filter(item => !item.isAvailable);

        if (insufficientStock.length > 0) {
            throw new Error(`Yetersiz stok: ${insufficientStock.map(item => item.ingredientName).join(', ')}`);
        }

        // Her malzeme için stok düşümü yap
        for (const item of stockCheck) {
            await conn.query(
                `UPDATE ingredients 
                 SET stock = stock - ? 
                 WHERE id = ? AND tenant_id = ?`,
                [item.neededQuantity, item.ingredientId, tenantId]
            );

            // Stok hareketi kaydet
            await conn.query(
                `INSERT INTO ingredient_stock_movements (
                    ingredient_id, movement_type, quantity,
                    previous_stock, new_stock, unit_price,
                    reference_type, reference_id, notes,
                    tenant_id, created_by
                ) VALUES (?, 'OUT', ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                [
                    item.ingredientId,
                    item.neededQuantity,
                    item.availableStock,
                    item.availableStock - item.neededQuantity,
                    0, // unit_price bu noktada önemli değil
                    referenceType,
                    referenceId,
                    `Reçete kullanımı: ${quantity} adet`,
                    tenantId,
                    userId
                ]
            );
        }

        await conn.commit();
    } catch (error) {
        await conn.rollback();
        throw error;
    } finally {
        conn.release();
    }
};