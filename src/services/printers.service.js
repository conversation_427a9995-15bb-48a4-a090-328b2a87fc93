const { getMySqlPromiseConnection } = require("../config/mysql.db");

exports.addPrinterService = async (tenantId, name, path, type) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const query = `
      INSERT INTO printers (tenant_id, name, path, type, created_at, updated_at)
      VALUES (?, ?, ?, ?, NOW(), NOW())
    `;
    
    await conn.execute(query, [tenantId, name, path, type]);

    return true;
  } catch (error) {
    console.error("Yazıcı eklenirken hata:", error);
    throw error;
  } finally {
    if (conn) conn.release();
  }
};

exports.getPrintersService = async (tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const query = 'SELECT id, tenant_id, name, path, type, created_at, updated_at FROM printers WHERE tenant_id = ?';
    const [rows] = await conn.execute(query, [tenantId]);
    return rows;
  } catch (error) {
    console.error("<PERSON><PERSON>ıcılar alınırken hata:", error);
    throw error;
  } finally {
    if (conn) conn.release();
  }
};

exports.updatePrinterService = async (printerId, name, path, type) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const query = `
      UPDATE printers 
      SET name = ?, path = ?, type = ?, updated_at = NOW()
      WHERE id = ?
    `;
    await conn.execute(query, [name, path, type, printerId]);
    return true;
  } catch (error) {
    console.error("Yazıcı güncellenirken hata:", error);
    throw error;
  } finally {
    if (conn) conn.release();
  }
};

exports.deletePrinterService = async (printerId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const query = 'DELETE FROM printers WHERE id = ?';
    await conn.execute(query, [printerId]);
    return true;
  } catch (error) {
    console.error("Yazıcı silinirken hata:", error);
    throw error;
  } finally {
    if (conn) conn.release();
  }
};
