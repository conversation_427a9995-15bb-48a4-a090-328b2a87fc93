const { getMySqlPromiseConnection } = require("../config/mysql.db");
const crypto = require('crypto');

/**
 * Yeni API key oluştur
 */
exports.generateApiKeyDB = async (tenantId, name, description = null) => {
  const conn = await getMySqlPromiseConnection();
  try {
    // API key'i boş bırak, SQL trigger otomatik oluşturacak
    const [result] = await conn.query(`
      INSERT INTO mtsm_api_keys
      (tenant_id, name, description, is_active, created_at)
      VALUES (?, ?, ?, 1, NOW())
    `, [tenantId, name, description]);

    // Oluşturulan kaydı geri al (api_key ile birlikte)
    const [createdRecord] = await conn.query(`
      SELECT id, name, description, api_key, is_active, created_at
      FROM mtsm_api_keys
      WHERE id = ?
    `, [result.insertId]);

    const apiKeyRecord = createdRecord[0];

    console.log(`✅ API Key oluşturuldu - Tenant: ${tenantId}, Name: ${name}, Key: ${apiKeyRecord.api_key.substring(0, 8)}...`);

    return {
      id: apiKeyRecord.id,
      name: apiKeyRecord.name,
      description: apiKeyRecord.description,
      api_key: apiKeyRecord.api_key,
      is_active: apiKeyRecord.is_active,
      created_at: apiKeyRecord.created_at
    };

  } catch (error) {
    console.error("generateApiKeyDB error:", error);
    throw error;
  } finally {
    conn.release();
  }
};

/**
 * Tenant'ın API key'lerini listele
 */
exports.getApiKeysDB = async (tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const [rows] = await conn.query(`
      SELECT 
        id,
        name,
        description,
        api_key,
        is_active,
        created_at,
        last_used_at
      FROM mtsm_api_keys
      WHERE tenant_id = ?
      ORDER BY created_at DESC
    `, [tenantId]);

    // API key'leri güvenlik için kısalt (sadece ilk 8 karakter göster)
    return rows.map(row => ({
      ...row,
      api_key_preview: row.api_key.substring(0, 8) + '...',
      api_key: undefined // Tam API key'i gizle
    }));

  } catch (error) {
    console.error("getApiKeysDB error:", error);
    throw error;
  } finally {
    conn.release();
  }
};

/**
 * API key'i yeniden oluştur
 */
exports.regenerateApiKeyDB = async (tenantId, keyId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    // Önce key'in bu tenant'a ait olduğunu kontrol et
    const [existing] = await conn.query(`
      SELECT id, name, description FROM mtsm_api_keys
      WHERE id = ? AND tenant_id = ?
    `, [keyId, tenantId]);

    if (existing.length === 0) {
      return null;
    }

    // Yeni API key oluştur (SQL fonksiyonu kullan)
    const [newKeyResult] = await conn.query(`
      UPDATE mtsm_api_keys
      SET api_key = generate_api_key(), updated_at = NOW()
      WHERE id = ? AND tenant_id = ?
    `, [keyId, tenantId]);

    // Güncellenmiş kaydı al
    const [updatedRecord] = await conn.query(`
      SELECT api_key FROM mtsm_api_keys
      WHERE id = ? AND tenant_id = ?
    `, [keyId, tenantId]);

    const newApiKey = updatedRecord[0].api_key;

    console.log(`🔄 API Key yenilendi - Tenant: ${tenantId}, ID: ${keyId}, New Key: ${newApiKey.substring(0, 8)}...`);

    return {
      id: keyId,
      name: existing[0].name,
      description: existing[0].description,
      api_key: newApiKey,
      updated_at: new Date()
    };

  } catch (error) {
    console.error("regenerateApiKeyDB error:", error);
    throw error;
  } finally {
    conn.release();
  }
};

/**
 * API key'i sil
 */
exports.deleteApiKeyDB = async (tenantId, keyId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const [result] = await conn.query(`
      DELETE FROM mtsm_api_keys 
      WHERE id = ? AND tenant_id = ?
    `, [keyId, tenantId]);

    if (result.affectedRows === 0) {
      return false;
    }

    console.log(`🗑️ API Key silindi - Tenant: ${tenantId}, ID: ${keyId}`);
    return true;

  } catch (error) {
    console.error("deleteApiKeyDB error:", error);
    throw error;
  } finally {
    conn.release();
  }
};

/**
 * API key kullanım zamanını güncelle
 */
exports.updateApiKeyUsageDB = async (apiKey) => {
  const conn = await getMySqlPromiseConnection();
  try {
    await conn.query(`
      UPDATE mtsm_api_keys 
      SET last_used_at = NOW()
      WHERE api_key = ?
    `, [apiKey]);

  } catch (error) {
    console.error("updateApiKeyUsageDB error:", error);
    // Bu hata kritik değil, devam et
  } finally {
    conn.release();
  }
};

/**
 * Güvenli API key oluştur
 */
function generateSecureApiKey() {
  // Format: mtsm_[32 karakter random string]
  const randomBytes = crypto.randomBytes(24);
  const randomString = randomBytes.toString('base64')
    .replace(/[+/=]/g, '') // URL-safe karakterler
    .substring(0, 32);
  
  return `mtsm_${randomString}`;
}
