const { getMySqlPromiseConnection } = require("../config/mysql.db");

// Feedback ekleme
exports.addFeedBacksDB = async (feedback_type, message, tenant_id) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const sql = `
      INSERT INTO feedbacks 
      (feedback_type, message, tenant_id)
      VALUES (?, ?, ?);
    `;
    const [result] = await conn.query(sql, [feedback_type, message, tenant_id]);

    return result.insertId;
  } catch (error) {
    console.error("Feedback eklenirken hata:", error);
    throw error;
  } finally {
    conn.release();
  }
};

exports.listCampaignsDB = async (tenantId, currentDate) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const sql = `
      SELECT * FROM campaigns 
      WHERE tenant_id = ? 
      AND start_date <= ? 
      AND end_date >= ?;
    `;
    const [rows] = await conn.query(sql, [tenantId, currentDate, currentDate]);
    return rows;
  } catch (error) {
    console.error("Kampanyalar listelenirken hata:", error);
    throw error;
  } finally {
    conn.release();
  }
};

// Feedback silme
exports.deleteFeedBacksDB = async (feedbackId, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const sql = `
      DELETE FROM feedbacks 
      WHERE id = ? AND tenant_id = ?;
    `;
    await conn.query(sql, [feedbackId, tenantId]);
    return;
  } catch (error) {
    console.error("Feedback silinirken hata:", error);
    throw error;
  } finally {
    conn.release();
  }
};

// Kampanya ekleme
exports.addCampaignDB = async (campaign_name, description, start_date, end_date, tenant_id) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const sql = `
      INSERT INTO campaigns 
      (campaign_name, description, start_date, end_date, tenant_id)
      VALUES (?, ?, ?, ?, ?);
    `;
    const [result] = await conn.query(sql, [campaign_name, description, start_date, end_date, tenant_id]);

    return result.insertId;
  } catch (error) {
    console.error("Kampanya eklenirken hata:", error);
    throw error;
  } finally {
    conn.release();
  }
};

// Kampanya silme
exports.deleteCampaignDB = async (campaignId, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const sql = `
      DELETE FROM campaigns 
      WHERE id = ? AND tenant_id = ?;
    `;
    await conn.query(sql, [campaignId, tenantId]);
    return;
  } catch (error) {
    console.error("Kampanya silinirken hata:", error);
    throw error;
  } finally {
    conn.release();
  }
};

// Kampanya listeleme
exports.listCampaignsDB = async (tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const sql = `
      SELECT * FROM campaigns 
      WHERE tenant_id = ?;
    `;
    const [rows] = await conn.query(sql, [tenantId]);
    return rows;
  } catch (error) {
    console.error("Kampanyalar listelenirken hata:", error);
    throw error;
  } finally {
    conn.release();
  }
};
