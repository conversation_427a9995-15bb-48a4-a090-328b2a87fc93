const { getMySqlPromiseConnection } = require("../config/mysql.db");
const { getUserFloorRestrictionsDB } = require("./user.service");

/**
 * Tüm katları getirir
 * @param {number} tenantId - Kiracı ID
 * @returns {Array} Katlar listesi
 */
exports.getFloorsDB = async (tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const [floors] = await conn.query(
      `SELECT id, title, description, is_active, tenant_id, waiter_username,
       created_at, updated_at
       FROM floor
       WHERE tenant_id = ?
       ORDER BY id ASC`,
      [tenantId]
    );
    return floors;
  } catch (error) {
    console.error("Error in getFloorsDB:", error);
    throw error;
  } finally {
    conn.release();
  }
};

/**
 * Kullanıcının erişim izni olan katları getirir
 * @param {string} username - <PERSON><PERSON><PERSON><PERSON>ı adı
 * @param {number} tenantId - Kiracı ID
 * @returns {Array} Kullanıcının erişim izni olan katlar listesi
 */
exports.getUserAccessibleFloorsDB = async (username, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    // Kullanıcının erişim kısıtlaması olan katları al
    const restrictedFloorIds = await getUserFloorRestrictionsDB(username, tenantId);

    // Tüm katları al
    const [allFloors] = await conn.query(
      `SELECT id, title, description, is_active, tenant_id, waiter_username,
       created_at, updated_at
       FROM floor
       WHERE tenant_id = ?
       ORDER BY id ASC`,
      [tenantId]
    );

    // Kullanıcının erişim izni olan katları filtrele
    const accessibleFloors = allFloors.filter(floor => !restrictedFloorIds.includes(floor.id));

    return accessibleFloors;
  } catch (error) {
    console.error("Error in getUserAccessibleFloorsDB:", error);
    throw error;
  } finally {
    conn.release();
  }
};

/**
 * ID'ye göre kat getirir
 * @param {number} id - Kat ID
 * @param {number} tenantId - Kiracı ID
 * @returns {Object} Kat bilgisi
 */
exports.getFloorByIdDB = async (id, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const [floors] = await conn.query(
      `SELECT id, title, description, is_active, tenant_id, waiter_username,
       created_at, updated_at
       FROM floor
       WHERE id = ? AND tenant_id = ?`,
      [id, tenantId]
    );

    if (floors.length === 0) {
      return null;
    }

    return floors[0];
  } catch (error) {
    console.error("Error in getFloorByIdDB:", error);
    throw error;
  } finally {
    conn.release();
  }
};

/**
 * Yeni kat ekler
 * @param {string} title - Kat başlığı
 * @param {string} description - Açıklama
 * @param {boolean} isActive - Aktif durumu
 * @param {string} waiterUsername - Garson kullanıcı adı
 * @param {number} tenantId - Kiracı ID
 * @returns {number} Eklenen katın ID'si
 */
exports.addFloorDB = async (title, description, isActive, waiterUsername, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const [result] = await conn.query(
      `INSERT INTO floor (title, description, is_active, waiter_username, tenant_id, created_at, updated_at)
       VALUES (?, ?, ?, ?, ?, NOW(), NOW())`,
      [title, description, isActive, waiterUsername, tenantId]
    );

    return result.insertId;
  } catch (error) {
    console.error("Error in addFloorDB:", error);
    throw error;
  } finally {
    conn.release();
  }
};

/**
 * Kat bilgilerini günceller
 * @param {number} id - Kat ID
 * @param {string} title - Kat başlığı
 * @param {string} description - Açıklama
 * @param {boolean} isActive - Aktif durumu
 * @param {string} waiterUsername - Garson kullanıcı adı
 * @param {number} tenantId - Kiracı ID
 * @returns {boolean} Güncelleme başarılı mı
 */
exports.updateFloorDB = async (id, title, description, isActive, waiterUsername, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const [result] = await conn.query(
      `UPDATE floor
       SET title = ?, description = ?, is_active = ?, waiter_username = ?, updated_at = NOW()
       WHERE id = ? AND tenant_id = ?`,
      [title, description, isActive, waiterUsername, id, tenantId]
    );

    return result.affectedRows > 0;
  } catch (error) {
    console.error("Error in updateFloorDB:", error);
    throw error;
  } finally {
    conn.release();
  }
};

/**
 * Kat siler
 * @param {number} id - Kat ID
 * @param {number} tenantId - Kiracı ID
 * @returns {boolean} Silme başarılı mı
 */
exports.deleteFloorDB = async (id, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    // Önce bu kata bağlı masalar var mı kontrol et
    const [tables] = await conn.query(
      `SELECT COUNT(*) as table_count FROM store_tables WHERE floor = ? AND tenant_id = ?`,
      [id, tenantId]
    );

    if (tables[0].table_count > 0) {
      throw new Error("Bu kata bağlı masalar var. Önce masaları silmelisiniz.");
    }

    const [result] = await conn.query(
      `DELETE FROM floor WHERE id = ? AND tenant_id = ?`,
      [id, tenantId]
    );

    return result.affectedRows > 0;
  } catch (error) {
    console.error("Error in deleteFloorDB:", error);
    throw error;
  } finally {
    conn.release();
  }
};
