const { getMySqlPromiseConnection } = require("../config/mysql.db")

exports.getOrdersDB = async (tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {

    const sql = `
    SELECT
      o.id,
      o.date,
      o.delivery_type,
      o.customer_type,
      o.customer_id,
      c.\`name\` AS customer_name,
      o.table_id,
      o.floor_id,
      st.table_title,
      st.\`floor\`,
      f.title AS floor_name,
      o.status,
      o.payment_status,
      o.token_no,
      o.username, -- orders tablosundaki username'i çekiyoruz
      u.\`name\` AS user_name -- users tablosundan name'i alıyoruz
    FROM
      orders o
      LEFT JOIN customers c ON o.customer_id = c.phone AND c.tenant_id = o.tenant_id
      LEFT JOIN store_tables st ON o.table_id = st.id
      LEFT JOIN floor f ON o.floor_id = f.id
      LEFT JOIN users u ON o.username = u.username AND u.tenant_id = o.tenant_id -- Users tablosunu ekledik
    WHERE
       o.status NOT IN ('completed', 'cancelled')
     AND o.tenant_id = ?
    `;

    const [kitchenOrders] = await conn.query(sql, [tenantId]);

    let kitchenOrdersItems = [];
    let addons = [];
    let partialPayments = [];
    let orderDiscounts = [];

    if(kitchenOrders.length > 0) {
      const orderIds = kitchenOrders.map(o=>o.id).join(",");
      const sql2 = `
      SELECT
        oi.id,
        oi.order_id,
        oi.item_id,
        mi.title AS item_title,
        oi.variant_id,
        miv.title as variant_title,
        CASE
          WHEN oi.status = 'complimentary' THEN 0.00
          ELSE oi.price
        END as price,
        oi.quantity,
        oi.status,
        oi.date,
        oi.addons,
        oi.notes,
        oi.username,
        u.name AS user_name
      FROM
        order_items oi
        LEFT JOIN menu_items mi ON oi.item_id = mi.id
        LEFT join menu_item_variants miv ON oi.item_id = miv.item_id AND oi.variant_id = miv.id
        LEFT JOIN users u ON oi.username = u.username AND u.tenant_id = oi.tenant_id

      WHERE oi.order_id IN (${orderIds})
      `
      const [kitchenOrdersItemsResult] = await conn.query(sql2);
      kitchenOrdersItems = kitchenOrdersItemsResult;

      const addonIds = [...new Set([...kitchenOrdersItems.flatMap((o)=>o.addons?JSON.parse(o?.addons):[])])].join(",");
      const [addonsResult] = addonIds ? await conn.query(`SELECT id, item_id, title FROM menu_item_addons WHERE id IN (${addonIds});`):[]
      addons = addonsResult;

      const sql3 = `
        SELECT
          pt.id,
          pt.order_id,
          pt.payment_type_id,
          pt.amount,
          pt.transaction_type,
          pt.status,
          pt.notes as product_name,
          pt.created_by,
          pt.created_at,
          pt.updated_by,
          pt.updated_at,
          pt.invoice_id,
          pt.cash_register_session_id,
          pt.tenant_id,
          pty.title as payment_type,
          pty.icon as payment_icon,
          u.name as user_name
        FROM payment_transactions pt
        JOIN payment_types pty ON pt.payment_type_id = pty.id
        LEFT JOIN users u ON pt.created_by = u.username
        WHERE pt.order_id IN (${orderIds})
          AND pt.tenant_id = ?
          AND pt.status = 'completed'
          AND pt.transaction_type = 'payment'
        ORDER BY pt.created_at ASC
      `;
      const [partialPaymentsResult] = await conn.query(sql3, [tenantId]);
      partialPayments = partialPaymentsResult;

      // Sipariş indirimlerini getir
      const sql4 = `
        SELECT
          od.id,
          od.order_id,
          od.order_item_id,
          od.discount_type,
          CAST(od.discount_value AS DECIMAL(10,2)) as discount_value,
          CAST(CASE
            WHEN od.discount_type = 'percentage' THEN
              CASE
                WHEN od.order_item_id IS NULL THEN
                  (SELECT SUM(oi2.price * oi2.quantity) FROM order_items oi2
                   WHERE oi2.order_id = od.order_id AND oi2.status NOT IN ('cancelled')) * (od.discount_value / 100)
                ELSE
                  (SELECT oi2.price * oi2.quantity FROM order_items oi2 WHERE oi2.id = od.order_item_id) * (od.discount_value / 100)
              END
            WHEN od.discount_type = 'amount' THEN od.discount_value
            ELSE 0
          END AS DECIMAL(10,2)) as calculated_discount,
          od.created_by as discount_by,
          od.created_at as discount_date,
          u.name as discount_by_name,
          mi.title as item_title
        FROM order_discounts od
        LEFT JOIN order_items oi ON od.order_item_id = oi.id
        LEFT JOIN menu_items mi ON oi.item_id = mi.id
        LEFT JOIN users u ON od.created_by = u.username AND u.tenant_id = od.tenant_id
        WHERE od.order_id IN (${orderIds}) AND od.tenant_id = ?
        ORDER BY od.created_at ASC
      `;
      const [orderDiscountsResult] = await conn.query(sql4, [tenantId]);
      orderDiscounts = orderDiscountsResult;

    }
    return {
      kitchenOrders,
      kitchenOrdersItems,
      addons,
      partialPayments,
      orderDiscounts
    }

  } catch (error) {
    console.error(error);
    throw error;
  } finally {
    conn.release();
  }
};


exports.updateTableStatusDB = async (tableId, status, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const [result] = await conn.query(
      `UPDATE store_tables
         SET table_status = ?
       WHERE id = ? AND tenant_id = ?`,
      [status, tableId, tenantId]
    );
    return result;
  } finally {
    conn.release();
  }
};


exports.updateOrderTableAndStatusDB = async (orderId, newTableId, tenantId, floorId = null) => {
  const conn = await getMySqlPromiseConnection();
  try {
    await conn.beginTransaction();

    // Yeni masanın mevcut durumunu kontrol et
    const [newTableStatusResult] = await conn.query(
      `SELECT table_status FROM store_tables WHERE id = ? AND tenant_id = ?`,
      [newTableId, tenantId]
    );

    if (newTableStatusResult.length === 0) {
      throw new Error("Yeni masa bulunamadı");
    }

    const newTableStatus = newTableStatusResult[0].table_status;

    // Yeni masa doluysa veya kilitliyse işlemi durdur
    if (newTableStatus === 'busy' || newTableStatus === 'locked') {
      return { status: "failed", message: "Seçilen masa dolu veya kilitli" };
    }

    // Siparişin eski masasını bul
    const [oldTableResult] = await conn.query(
      `SELECT table_id FROM orders WHERE id = ? AND tenant_id = ?`,
      [orderId, tenantId]
    );

    if (oldTableResult.length === 0) {
      throw new Error("Sipariş bulunamadı");
    }

    const oldTableId = oldTableResult[0].table_id;

    // Eski masayı boşalt ve order_ids alanını NULL yap
    await conn.query(
      `UPDATE store_tables SET table_status = 'empty', order_ids = NULL WHERE id = ? AND tenant_id = ?`,
      [oldTableId, tenantId]
    );

    // Siparişi yeni masaya taşı
    if (floorId) {
      await conn.query(
        `UPDATE orders SET table_id = ?, floor_id = ? WHERE id = ? AND tenant_id = ?`,
        [newTableId, floorId, orderId, tenantId]
      );
    } else {
      await conn.query(
        `UPDATE orders SET table_id = ? WHERE id = ? AND tenant_id = ?`,
        [newTableId, orderId, tenantId]
      );
    }

    // Yeni masayı 'busy' yap ve order_ids alanına sipariş numarasını ekle
    await conn.query(
      `UPDATE store_tables SET table_status = 'busy', order_ids = ? WHERE id = ? AND tenant_id = ?`,
      [orderId, newTableId, tenantId]
    );

    await conn.commit();
    return { status: "success", message: "Masa başarıyla değiştirildi" };
  } catch (error) {
    await conn.rollback();
    console.error("Masa ve sipariş güncellenirken hata oluştu:", error);
    throw error;
  } finally {
    conn.release();
  }
};


exports.savePartialPaymentforOrdersDB = async (payments, orderData, tenantId, username) => {
  const conn = await getMySqlPromiseConnection();
  try {
    await conn.beginTransaction();

    // Store settings'den cash register ayarını kontrol et
    const [storeSettings] = await conn.query(
      `SELECT cash_register_enabled FROM store_details WHERE tenant_id = ? LIMIT 1`,
      [tenantId]
    );

    const cashRegisterEnabled = storeSettings.length > 0 ? storeSettings[0].cash_register_enabled : 1;

    // Kullanıcının aktif kasa oturumunu kontrol et (sadece cash register aktifse)
    let activeSessionId = null;
    if (cashRegisterEnabled) {
      const [activeSession] = await conn.query(
        `SELECT id FROM cash_register_sessions
         WHERE status = 'open' AND tenant_id = ?
         LIMIT 1`,
        [tenantId]
      );

      if (activeSession.length > 0) {
        activeSessionId = activeSession[0].id;
      }
    }

    // Sipariş bilgilerini al
    const orderIds = orderData.orderIds || [];
    const orderIdsText = orderIds.join(',');

    // Sipariş detaylarını getir
    const [orderDetails] = await conn.query(
      `SELECT
        o.id,
        o.table_id,
        st.table_title,
        o.payment_status,
        o.token_no,
        CAST(COALESCE(SUM(oi.price * oi.quantity), 0) AS DECIMAL(10,2)) as order_total
      FROM orders o
      LEFT JOIN store_tables st ON o.table_id = st.id
      LEFT JOIN order_items oi ON o.id = oi.order_id AND oi.status NOT IN ('cancelled')
      WHERE o.id IN (${orderIdsText}) AND o.tenant_id = ?
      GROUP BY o.id, o.table_id, st.table_title, o.payment_status, o.token_no`,
      [tenantId]
    );

    // Mevcut ödemeleri kontrol et
    const [existingPayments] = await conn.query(
      `SELECT order_id, CAST(COALESCE(SUM(amount), 0) AS DECIMAL(10,2)) as total_paid
      FROM payment_transactions
      WHERE order_id IN (${orderIdsText}) AND tenant_id = ? AND status = 'completed' AND transaction_type = 'payment'
      GROUP BY order_id`,
      [tenantId]
    );

    const paidAmounts = {};
    existingPayments.forEach(payment => {
      paidAmounts[payment.order_id] = parseFloat(payment.total_paid);
    });

    for (const payment of payments) {
      if (!payment.orderId) {
        throw new Error(`Eksik orderId: ${JSON.stringify(payment)}`);
      }

      // Bu sipariş için bilgileri bul
      const orderInfo = orderDetails.find(order => order.id === payment.orderId);
      const orderTotal = orderInfo ? parseFloat(orderInfo.order_total) : 0;
      const previouslyPaid = paidAmounts[payment.orderId] || 0;
      const newTotalPaid = previouslyPaid + parseFloat(payment.amount);

      // Ödeme durumunu belirle
      let paymentStatus = 'partial';
      if (newTotalPaid >= orderTotal) {
        paymentStatus = 'full';
      }

      // Notes oluştur
      const notes = [
        `Sipariş #${payment.orderId}`,
        orderInfo?.table_title ? `Masa: ${orderInfo.table_title}` : null,
        orderInfo?.token_no ? `Token: ${orderInfo.token_no}` : null,
        `Tutar: ${payment.amount}₺`,
        `Toplam: ${orderTotal}₺`,
        `Önceki Ödeme: ${previouslyPaid}₺`,
        `Yeni Toplam: ${newTotalPaid}₺`,
        `Durum: ${paymentStatus === 'full' ? 'Tam Ödeme' : 'Kısmi Ödeme'}`
      ].filter(Boolean).join(' | ');

      // Payment transactions tablosuna kaydet
      // Cash register pasifse activeSessionId null olarak kaydet
      await conn.query(
        `INSERT INTO payment_transactions
         (order_id, payment_type_id, amount, transaction_type, notes, created_by, cash_register_session_id, tenant_id)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          payment.orderId,
          payment.method,
          payment.amount,
          'payment',
          notes,
          username || 'system',
          cashRegisterEnabled ? activeSessionId : null,
          tenantId
        ]
      );
    }

    await conn.commit();
    return true;
  } catch (error) {
    await conn.rollback();
    console.error("Error saving partial payments:", error);
    throw error;
  } finally {
    conn.release();
  }
};


exports.savePartialPaymentDB = async (payments, tenantId, username) => {
  const conn = await getMySqlPromiseConnection();
  try {
    await conn.beginTransaction();

    // Store settings'den cash register ayarını kontrol et
    const [storeSettings] = await conn.query(
      `SELECT cash_register_enabled FROM store_details WHERE tenant_id = ? LIMIT 1`,
      [tenantId]
    );

    const cashRegisterEnabled = storeSettings.length > 0 ? storeSettings[0].cash_register_enabled : 1;

    // Kullanıcının aktif kasa oturumunu kontrol et (sadece cash register aktifse)
    let activeSessionId = null;
    if (username && cashRegisterEnabled) {
      const [activeSession] = await conn.query(
        `SELECT id FROM cash_register_sessions
         WHERE opened_by = ? AND status = 'open' AND tenant_id = ?
         LIMIT 1`,
        [username, tenantId]
      );

      if (activeSession.length > 0) {
        activeSessionId = activeSession[0].id;
      }
    }

    // Sipariş bilgilerini toplu olarak al
    const orderIds = [...new Set(payments.map(p => p.order_id))];
    const orderIdsText = orderIds.join(',');

    const [orderDetails] = await conn.query(
      `SELECT
        o.id,
        o.table_id,
        st.table_title,
        o.payment_status,
        o.token_no,
        CAST(COALESCE(SUM(oi.price * oi.quantity), 0) AS DECIMAL(10,2)) as order_total
      FROM orders o
      LEFT JOIN store_tables st ON o.table_id = st.id
      LEFT JOIN order_items oi ON o.id = oi.order_id AND oi.status NOT IN ('cancelled')
      WHERE o.id IN (${orderIdsText}) AND o.tenant_id = ?
      GROUP BY o.id, o.table_id, st.table_title, o.payment_status, o.token_no`,
      [tenantId]
    );

    // Mevcut ödemeleri kontrol et
    const [existingPayments] = await conn.query(
      `SELECT order_id, CAST(COALESCE(SUM(amount), 0) AS DECIMAL(10,2)) as total_paid
      FROM payment_transactions
      WHERE order_id IN (${orderIdsText}) AND tenant_id = ? AND status = 'completed' AND transaction_type = 'payment'
      GROUP BY order_id`,
      [tenantId]
    );

    const paidAmounts = {};
    existingPayments.forEach(payment => {
      paidAmounts[payment.order_id] = parseFloat(payment.total_paid);
    });

    for (const payment of payments) {
      if (!payment.order_id) {
        throw new Error(`Eksik order_id: ${JSON.stringify(payment)}`);
      }

      // Bu sipariş için bilgileri bul
      const orderInfo = orderDetails.find(order => order.id === payment.order_id);
      const orderTotal = orderInfo ? parseFloat(orderInfo.order_total) : 0;
      const previouslyPaid = paidAmounts[payment.order_id] || 0;
      const newTotalPaid = previouslyPaid + parseFloat(payment.amount);

      // Ödeme durumunu belirle
      let paymentStatus = 'partial';
      if (newTotalPaid >= orderTotal) {
        paymentStatus = 'full';
      }

      // Detaylı notes oluştur
      const notes = [
        `Sipariş #${payment.order_id}`,
        orderInfo?.table_title ? `Masa: ${orderInfo.table_title}` : null,
        orderInfo?.token_no ? `Token: ${orderInfo.token_no}` : null,
        `Tutar: ${payment.amount}₺`,
        `Toplam: ${orderTotal}₺`,
        `Önceki Ödeme: ${previouslyPaid}₺`,
        `Yeni Toplam: ${newTotalPaid}₺`,
        `Durum: ${paymentStatus === 'full' ? 'Tam Ödeme' : 'Kısmi Ödeme'}`
      ].filter(Boolean).join(' | ');

      // Payment transactions tablosuna kaydet
      // Cash register pasifse activeSessionId null olarak kaydet
      await conn.query(
        `INSERT INTO payment_transactions
         (order_id, payment_type_id, amount, transaction_type, notes, created_by, cash_register_session_id, tenant_id)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          payment.order_id,
          payment.method,
          payment.amount,
          'payment',
          notes,
          username,
          cashRegisterEnabled ? activeSessionId : null,
          tenantId
        ]
      );
    }

    // İlgili siparişlerin ödeme durumunu güncelle
    const updatedOrderIds = payments.map(payment => payment.order_id);
    await conn.query(
      `UPDATE orders SET payment_status = 'partial' WHERE id IN (?) AND tenant_id = ?`,
      [updatedOrderIds, tenantId]
    );

    await conn.commit();
    return true;
  } catch (error) {
    await conn.rollback();
    throw error;
  } finally {
    conn.release();
  }
};


exports.getPartialPaymentsDB = async (orderIds, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const sql = `
      SELECT
        pt.id,
        pt.order_id,
        pt.payment_type_id,
        pt.amount,
        pt.transaction_type,
        pt.status,
        pt.notes,
        pt.created_by,
        pt.created_at,
        pt.updated_by,
        pt.updated_at,
        pt.invoice_id,
        pt.cash_register_session_id,
        pt.tenant_id,
        pty.title as payment_type,
        pty.icon as payment_icon,
        u.name as user_name
      FROM payment_transactions pt
      JOIN payment_types pty ON pt.payment_type_id = pty.id
      LEFT JOIN users u ON pt.created_by = u.username
      WHERE pt.order_id IN (?)
        AND pt.tenant_id = ?
        AND pt.status = 'completed'
        AND pt.transaction_type = 'payment'
      ORDER BY pt.created_at ASC
    `;

    const [payments] = await conn.query(sql, [orderIds, tenantId]);
    return payments;
  } finally {
    conn.release();
  }
};

exports.updateOrderItemStatusDB = async (orderItemId, status, tenantId, username, reasonId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    let sql;
    let params;

    if (status === 'cancelled') {
      sql = `
      UPDATE order_items SET
      status = ?,
      cancelled_by = ?,
      cancelled_reason_id = ?
      WHERE id = ? AND tenant_id = ?;
      `;
      params = [status, username, reasonId, orderItemId, tenantId];
    } else if (status === 'complimentary') {
      sql = `
      UPDATE order_items SET
      status = ?,
      complimentary_by = ?,
      complimentary_reason_id = ?
      WHERE id = ? AND tenant_id = ?;
      `;
      params = [status, username, reasonId, orderItemId, tenantId];
    } else if (status === 'waste') {
      sql = `
      UPDATE order_items SET
      status = ?,
      waste_by = ?,
      waste_reason_id = ?
      WHERE id = ? AND tenant_id = ?;
      `;
      params = [status, username, reasonId, orderItemId, tenantId];
    } else {
      sql = `
      UPDATE order_items SET
      status = ?
      WHERE id = ? AND tenant_id = ?;
      `;
      params = [status, orderItemId, tenantId];
    }

    await conn.query(sql, params);

    return;
  } catch (error) {
    console.error(error);
    throw error;
  } finally {
    conn.release();
}
};

exports.updateOrderItemPriceDB = async (orderItemId, newPrice, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {

    const sql = `
    UPDATE order_items SET
    price = ?
    WHERE id = ? AND tenant_id = ?;
    `;

    await conn.query(sql, [newPrice, orderItemId, tenantId]);

    return;
  } catch (error) {
    console.error(error);
    throw error;
  } finally {
    conn.release();
}
};


exports.cancelOrderDB = async (orderIds, reasonId, notes, username, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    // Transaction başlat
    await conn.beginTransaction();

    // orderIds'i virgülle ayrılmış metin haline getir
    const orderIdsText = orderIds.join(",");

    // 1. Orders tablosunu güncelle (siparişi cancelled yap)
    const sqlUpdateOrders = `
      UPDATE orders
      SET status = 'cancelled'
      WHERE id IN (${orderIdsText}) AND tenant_id = ?;
    `;
    await conn.query(sqlUpdateOrders, [tenantId]);

    // 2. order_items tablosunu güncelle (ilgili siparişin tüm kalemlerini cancelled yap)
    const sqlUpdateOrderItems = `
      UPDATE order_items
      SET status = 'cancelled', cancelled_by = ?, cancelled_reason_id = ?
      WHERE order_id IN (${orderIdsText}) AND tenant_id = ?;
    `;
    await conn.query(sqlUpdateOrderItems, [username, reasonId, tenantId]);

    // 3. store_tables tablosunu güncelle
    //    Sadece table_id dolu (NULL olmayan) siparişleri hedefliyoruz.
    const sqlUpdateTables = `
      UPDATE store_tables
      SET table_status = 'empty',
          order_ids = NULL
      WHERE tenant_id = ?
        AND id IN (
          SELECT table_id
          FROM orders
          WHERE id IN (${orderIdsText})
            AND tenant_id = ?
            AND table_id IS NOT NULL
        );
    `;
    await conn.query(sqlUpdateTables, [tenantId, tenantId]);

    // 4. İptal nedenini kaydet
    if (reasonId) {
      // Her sipariş için iptal nedenini kaydet
      for (const orderId of orderIds) {
        const sqlInsertReason = `
          INSERT INTO order_cancellation_reasons
          (order_id, reason_id, notes, created_by, tenant_id)
          VALUES (?, ?, ?, ?, ?);
        `;
        await conn.query(sqlInsertReason, [orderId, reasonId, notes || null, username, tenantId]);
      }
    }

    // Transaction'ı tamamla (commit)
    await conn.commit();

  } catch (error) {
    // Hata durumunda tüm değişiklikleri geri al (rollback)
    await conn.rollback();
    console.error("cancelOrderDB Error:", error);
    throw error;
  } finally {
    // Bağlantıyı serbest bırak
    conn.release();
  }
};



exports.completeOrderDB = async (orderIds, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {

    const orderIdsText = orderIds.join(",");

    const sql = `
    UPDATE orders SET
    status = 'completed'
    WHERE id IN (${orderIdsText}) AND tenant_id = ?;
    `;

    await conn.query(sql, [tenantId]);

    return;
  } catch (error) {
    console.error(error);
    throw error;
  } finally {
    conn.release();
}
};

exports.saveDiscountDB = async (discountData, tenantId, username) => {
  const conn = await getMySqlPromiseConnection();
  try {
      await conn.beginTransaction();

      const { order_id, order_item_id, discount_type, discount_value } = discountData;

      const sql = `
          INSERT INTO order_discounts
          (order_id, order_item_id, discount_type, discount_value, tenant_id, created_by)
          VALUES (?, ?, ?, ?, ?, ?)
      `;

      await conn.query(sql, [
          order_id,
          order_item_id || null,
          discount_type,
          discount_value,
          tenantId,
          username
      ]);

      await conn.commit();
      return true;
  } catch (error) {
      await conn.rollback();
      throw error;
  } finally {
      conn.release();
  }
};

exports.getOrdersPaymentSummaryDB = async (orderIdsToFindSummary, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {

    const sql = `
    SELECT
      o.id,
      o.date,
      o.delivery_type,
      o.customer_type,
      o.customer_id,
      c. \`name\` AS customer_name,
      o.table_id,
      st.table_title,
      st. \`floor\`,
      o.status,
      o.payment_status,
      o.token_no
    FROM
      orders o
      LEFT JOIN customers c ON o.customer_id = c.phone AND c.tenant_id = o.tenant_id
      LEFT JOIN store_tables st ON o.table_id = st.id
    WHERE
      o.status NOT IN ('completed', 'cancelled')
  AND o.id IN (${orderIdsToFindSummary}) 
  AND o.tenant_id = ?
    `;

    const [kitchenOrders] = await conn.query(sql, [tenantId]);

    let kitchenOrdersItems = [];
    let addons = [];

    if(kitchenOrders.length > 0) {
      const orderIds = kitchenOrders.map(o=>o.id).join(",");
      const sql2 = `
      SELECT
        oi.id,
        oi.order_id,
        oi.item_id,
        mi.title AS item_title,
        oi.variant_id,
        miv.title as variant_title,
        miv.price as variant_price,
        CASE
          WHEN oi.status = 'complimentary' THEN 0.00
          ELSE oi.price
        END AS order_item_price,  -- complimentary ise 0.00, değilse gerçek fiyat
        mi.tax_id,
        t.title as tax_title,
        t.rate as tax_rate,
        t.type as tax_type,
        oi.quantity,
        oi.status,
        oi.date,
        oi.addons,
        oi.notes,
        oi.username,
        u.name AS user_name
      FROM
        order_items oi
        LEFT JOIN menu_items mi ON oi.item_id = mi.id
        LEFT JOIN menu_item_variants miv ON oi.item_id = miv.item_id AND oi.variant_id = miv.id
        LEFT JOIN taxes t ON mi.tax_id = t.id
        LEFT JOIN users u ON oi.username = u.username AND u.tenant_id = oi.tenant_id

      WHERE oi.order_id IN (${orderIds}) AND oi.status NOT IN ('cancelled', 'waste')
      `
      const [kitchenOrdersItemsResult] = await conn.query(sql2);
      kitchenOrdersItems = kitchenOrdersItemsResult;

      const addonIds = [...new Set([...kitchenOrdersItems.flatMap((o)=>o.addons?JSON.parse(o?.addons):[])])].join(",");
      const [addonsResult] = addonIds ? await conn.query(`SELECT id, item_id, title, price FROM menu_item_addons WHERE id IN (${addonIds});`):[]
      addons = addonsResult;
    }

    const [discounts] = await conn.query(`
      SELECT * FROM order_discounts
      WHERE order_id IN (${orderIdsToFindSummary})
      AND tenant_id = ?
  `, [tenantId]);


    return {
      kitchenOrders,
      kitchenOrdersItems,
      addons,
      discounts
    }

  } catch (error) {
    console.error(error);
    throw error;
  } finally {
    conn.release();
}
};

exports.createInvoiceDB = async (subtotal, taxTotal, total, date, selectedPaymentType, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    await conn.beginTransaction();

    let invoiceId = 0;

    const [invoiceSequence] = await conn.query("SELECT sequence_no FROM invoice_sequences WHERE tenant_id = ? LIMIT 1 FOR UPDATE", [tenantId]);
    invoiceId = invoiceSequence[0]?.sequence_no || 0;

    invoiceId += 1;

    const sql = `
    INSERT INTO invoices
    (id, sub_total, tax_total, total, created_at, payment_type_id, tenant_id)
    VALUES
    (?, ?, ?, ?, ?, ?, ?)
    `;

    await conn.query(sql, [invoiceId, subtotal, taxTotal, total, date, selectedPaymentType, tenantId]);

    await conn.query("INSERT INTO invoice_sequences ( sequence_no, tenant_id) VALUES (?, ?) ON DUPLICATE KEY UPDATE sequence_no = VALUES(sequence_no);", [invoiceId, tenantId]);

    await conn.commit();

    return invoiceId;
  } catch (error) {
    console.error(error);
    await conn.rollback();
    throw error;
  } finally {
    conn.release();
  }
}


exports.completeOrdersAndSaveCariIdDB = async (orderIds, tenantId, total, customer) => {
  const conn = await getMySqlPromiseConnection();
  try {
    // customer parametresi zaten telefon numarasını içeriyorsa, bunu customerPhone değişkenine atıyoruz.
    const customerPhone = customer; // Eğer customer doğrudan telefon numarasıysa

    console.log("Tenant ID:", tenantId, "Customer (phone):", customerPhone);

    // Müşteriyi sorgula...
    const [customerRows] = await conn.query(
      `SELECT * FROM customers WHERE phone = ? AND tenant_id = ?`,
      [customerPhone, tenantId]
    );
    if (customerRows.length === 0) {
      throw new Error("Müşteri bulunamadı!");
    }
    const customerRecord = customerRows[0];
    if (customerRecord.allow_credit !== 1) {
      throw new Error("Bu müşteri için kredi işlemi onaylı değil!");
    }

    // Sipariş güncellemeleri ve diğer işlemler...
    const orderIdsText = orderIds.join(",");
    const sqlUpdateOrders = `
      UPDATE orders
      SET status = 'completed',
          payment_status = 'credit',
          customer_id = ?,
          remaining_balance = ?,
          closed_at = NOW()
      WHERE id IN (${orderIdsText}) AND tenant_id = ?;
    `;
    await conn.query(sqlUpdateOrders, [customerPhone, total, tenantId]);

    const sqlUpdateTables = `
      UPDATE store_tables
      SET table_status = 'empty', order_ids = NULL
      WHERE id IN (
        SELECT table_id FROM orders WHERE id IN (${orderIdsText}) AND table_id IS NOT NULL
      );
    `;
    await conn.query(sqlUpdateTables);

    const newBalance = customerRecord.balance - total;
    const sqlUpdateCustomer = `
      UPDATE customers
      SET balance = ?
      WHERE phone = ? AND tenant_id = ?;
    `;
    await conn.query(sqlUpdateCustomer, [newBalance, customerPhone, tenantId]);

    return;
  } catch (error) {
    console.error("completeOrdersAndSaveCariDetails hata:", error);
    throw error;
  } finally {
    conn.release();
  }
};



exports.completeOrdersAndSaveInvoiceIdDB = async (orderIds, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const orderIdsText = orderIds.join(",");

    // 1. Orders tablosunu güncelle
    const sqlUpdateOrders = `
    UPDATE orders
    SET status = 'completed', payment_status = 'paid'
    WHERE id IN (${orderIdsText}) AND tenant_id = ?;
    `;

    await conn.query(sqlUpdateOrders, [tenantId]);

    // 2. store_tables tablosunu güncelle
    const sqlUpdateTables = `
    UPDATE store_tables
    SET table_status = 'empty', order_ids = NULL
    WHERE id IN (
      SELECT table_id FROM orders WHERE id IN (${orderIdsText}) AND table_id IS NOT NULL
    );
    `;

    await conn.query(sqlUpdateTables);

    return;
  } catch (error) {
    console.error(error);
    throw error;
  } finally {
    conn.release();
  }
};

exports.updateOrderCustomerDB = async (orderId, customerData, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    await conn.beginTransaction();

    const { phone, name, customerType } = customerData;

    // Önce müşteri var mı kontrol et
    const [existingCustomer] = await conn.query(
      `SELECT phone, name FROM customers WHERE phone = ? AND tenant_id = ?`,
      [phone, tenantId]
    );

    // Müşteri yoksa oluştur
    if (existingCustomer.length === 0) {
      await conn.query(
        `INSERT INTO customers (phone, name, tenant_id) VALUES (?, ?, ?)`,
        [phone, name, tenantId]
      );
    } else {
      // Müşteri varsa adını güncelle (eğer farklıysa)
      if (existingCustomer[0].name !== name) {
        await conn.query(
          `UPDATE customers SET name = ? WHERE phone = ? AND tenant_id = ?`,
          [name, phone, tenantId]
        );
      }
    }

    // Orders tablosunu güncelle
    await conn.query(
      `UPDATE orders SET customer_id = ?, customer_type = ? WHERE id = ? AND tenant_id = ?`,
      [phone, customerType, orderId, tenantId]
    );

    await conn.commit();
    return { success: true };
  } catch (error) {
    await conn.rollback();
    console.error("updateOrderCustomerDB error:", error);
    throw error;
  } finally {
    conn.release();
  }
};

exports.updateOrderItemAsComplimentaryDB = async (orderItemId, reasonId, tenantId, username) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const sql = `
    UPDATE order_items SET
    status = 'complimentary',
    complimentary_reason_id = ?,
    complimentary_by = ?
    WHERE id = ? AND tenant_id = ?
    `;

    await conn.query(sql, [reasonId, username, orderItemId, tenantId]);

    return;
  } catch (error) {
    console.error(error);
    throw error;
  } finally {
    conn.release();
  }
};

exports.updateOrderItemAsWasteDB = async (orderItemId, reasonId, tenantId, username) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const sql = `
    UPDATE order_items SET
    status = 'waste',
    waste_reason_id = ?,
    waste_by = ?
    WHERE id = ? AND tenant_id = ?
    `;

    await conn.query(sql, [reasonId, username, orderItemId, tenantId]);

    return;
  } catch (error) {
    console.error(error);
    throw error;
  } finally {
    conn.release();
  }
};

/**
 * Sipariş öğesini bir masadan diğerine taşır
 * @param {number} orderItemId - Taşınacak sipariş öğesi ID
 * @param {number} targetTableId - Hedef masa ID
 * @param {number} tenantId - Kiracı ID
 * @returns {Object} İşlem sonucu
 */
exports.moveOrderItemToTableDB = async (orderItemId, targetTableId, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    await conn.beginTransaction();

    // 1. Sipariş öğesini ve bağlı olduğu siparişi al
    const [orderItemResult] = await conn.query(
      `SELECT oi.*, o.table_id as source_table_id
       FROM order_items oi
       JOIN orders o ON oi.order_id = o.id
       WHERE oi.id = ? AND oi.tenant_id = ?`,
      [orderItemId, tenantId]
    );

    if (orderItemResult.length === 0) {
      throw new Error("Sipariş öğesi bulunamadı");
    }

    const orderItem = orderItemResult[0];
    const sourceOrderId = orderItem.order_id;
    const sourceTableId = orderItem.source_table_id;

    // 2. Kaynak masadaki sipariş öğelerinin sayısını kontrol et
    const [itemCountResult] = await conn.query(
      `SELECT COUNT(*) as item_count FROM order_items WHERE order_id = ? AND tenant_id = ?`,
      [sourceOrderId, tenantId]
    );

    if (itemCountResult[0].item_count <= 1) {
      return {
        status: "failed",
        message: "Son ürünü taşıyamazsınız. Bunun yerine masa taşıma yapınız."
      };
    }

    // 3. Hedef masanın durumunu kontrol et
    const [targetTableResult] = await conn.query(
      `SELECT table_status, order_ids FROM store_tables WHERE id = ? AND tenant_id = ?`,
      [targetTableId, tenantId]
    );

    if (targetTableResult.length === 0) {
      throw new Error("Hedef masa bulunamadı");
    }

    const targetTable = targetTableResult[0];
    let targetOrderId;

    // 4. Hedef masada sipariş var mı kontrol et
    if (targetTable.table_status === 'busy' && targetTable.order_ids) {
      // Hedef masada sipariş varsa, o siparişi kullan
      targetOrderId = targetTable.order_ids;
    } else {
      // Hedef masada sipariş yoksa, yeni sipariş oluştur
      const [sourceOrderResult] = await conn.query(
        `SELECT * FROM orders WHERE id = ? AND tenant_id = ?`,
        [sourceOrderId, tenantId]
      );

      if (sourceOrderResult.length === 0) {
        throw new Error("Kaynak sipariş bulunamadı");
      }

      const sourceOrder = sourceOrderResult[0];

      // Yeni sipariş oluştur
      const [newOrderResult] = await conn.query(
        `INSERT INTO orders (
          customer_id, customer_type, delivery_type,
          table_id, payment_status,
          token_no, tenant_id, username
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          sourceOrder.customer_id,
          sourceOrder.customer_type,
          sourceOrder.delivery_type,
          targetTableId,
          'pending',
          sourceOrder.token_no,
          tenantId,
          sourceOrder.username
        ]
      );

      targetOrderId = newOrderResult.insertId;

      // Hedef masayı güncelle
      await conn.query(
        `UPDATE store_tables SET table_status = 'busy', order_ids = ? WHERE id = ? AND tenant_id = ?`,
        [targetOrderId, targetTableId, tenantId]
      );
    }

    // 5. Sipariş öğesini yeni siparişe taşı
    await conn.query(
      `UPDATE order_items SET order_id = ? WHERE id = ? AND tenant_id = ?`,
      [targetOrderId, orderItemId, tenantId]
    );

    await conn.commit();
    return {
      status: "success",
      message: "Sipariş öğesi başarıyla taşındı",
      sourceTableId,
      targetTableId,
      sourceOrderId,
      targetOrderId
    };
  } catch (error) {
    await conn.rollback();
    console.error("Sipariş öğesi taşınırken hata oluştu:", error);
    throw error;
  } finally {
    conn.release();
  }
};

/**
 * İki masayı birleştirir. Kaynak masadaki sipariş öğelerini hedef masadaki siparişe taşır.
 * @param {number} sourceTableId - Kaynak masa ID
 * @param {number} targetTableId - Hedef masa ID
 * @param {number} tenantId - Kiracı ID
 * @returns {Object} İşlem sonucu
 */
exports.mergeTablesDB = async (sourceTableId, targetTableId, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    await conn.beginTransaction();

    // 1. Her iki masanın da dolu olduğunu kontrol et
    const [tablesResult] = await conn.query(
      `SELECT id, table_status, order_ids FROM store_tables
       WHERE id IN (?, ?) AND tenant_id = ?`,
      [sourceTableId, targetTableId, tenantId]
    );

    if (tablesResult.length !== 2) {
      throw new Error("Bir veya iki masa bulunamadı");
    }

    const sourceTable = tablesResult.find(t => t.id == sourceTableId);
    const targetTable = tablesResult.find(t => t.id == targetTableId);

    if (!sourceTable || !targetTable) {
      throw new Error("Bir veya iki masa bulunamadı");
    }

    if (sourceTable.table_status !== 'busy' || !sourceTable.order_ids) {
      return {
        status: "failed",
        message: "Kaynak masa boş, birleştirme yapılamaz"
      };
    }

    if (targetTable.table_status !== 'busy' || !targetTable.order_ids) {
      return {
        status: "failed",
        message: "Hedef masa boş, birleştirme yapılamaz"
      };
    }

    const sourceOrderId = sourceTable.order_ids;
    const targetOrderId = targetTable.order_ids;

    // 2. Kaynak masada kısmi ödeme olup olmadığını kontrol et
    const [partialPaymentsResult] = await conn.query(
      `SELECT COUNT(*) as payment_count FROM partial_payments
       WHERE order_id = ? AND tenant_id = ?`,
      [sourceOrderId, tenantId]
    );

    if (partialPaymentsResult[0].payment_count > 0) {
      return {
        status: "failed",
        message: "Kaynak masada kısmi ödeme var, birleştirme yapılamaz"
      };
    }

    // 3. Kaynak masadaki sipariş öğelerini hedef masadaki siparişe taşı
    await conn.query(
      `UPDATE order_items SET order_id = ? WHERE order_id = ? AND tenant_id = ?`,
      [targetOrderId, sourceOrderId, tenantId]
    );

    // 4. Kaynak masadaki siparişi sil
    await conn.query(
      `DELETE FROM orders WHERE id = ? AND tenant_id = ?`,
      [sourceOrderId, tenantId]
    );

    // 5. Kaynak masayı boş duruma getir
    await conn.query(
      `UPDATE store_tables SET table_status = 'empty', order_ids = NULL WHERE id = ? AND tenant_id = ?`,
      [sourceTableId, tenantId]
    );

    await conn.commit();
    return {
      status: "success",
      message: "Masalar başarıyla birleştirildi",
      sourceTableId,
      targetTableId,
      sourceOrderId,
      targetOrderId
    };
  } catch (error) {
    await conn.rollback();
    console.error("Masalar birleştirilirken hata oluştu:", error);
    throw error;
  } finally {
    conn.release();
  }
};


