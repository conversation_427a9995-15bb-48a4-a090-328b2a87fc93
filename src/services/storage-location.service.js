const { getMySqlPromiseConnection } = require("../config/mysql.db");

/**
 * Depo/Bar Lokasyonları Modülü Service
 * Bu modül işletmelerin farklı depo ve barlarını yönetmesini sağlar
 */

// ============================================================================
// MODÜL AYARLARI
// ============================================================================

/**
 * Tenant için modül ayarlarını getirir
 * @param {number} tenantId - Kiracı ID
 * @returns {Object} Modül ayarları
 */
exports.getStorageLocationSettingsDB = async (tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const sql = `
      SELECT 
        is_enabled,
        default_location_id,
        auto_deduct_from_floor,
        allow_negative_stock,
        require_location_for_orders,
        settings
      FROM storage_location_settings 
      WHERE tenant_id = ?
    `;
    
    const [rows] = await conn.query(sql, [tenantId]);

    // Eğer ayar yoksa varsayılan değerleri döndür
    if (rows.length === 0) {
      return {
        is_enabled: false,
        default_location_id: null,
        auto_deduct_from_floor: true,
        allow_negative_stock: false,
        require_location_for_orders: false,
        settings: null
      };
    }

    // MySQL tinyint(1) değerlerini boolean'a çevir
    const settings = rows[0];
    return {
      is_enabled: Boolean(settings.is_enabled),
      default_location_id: settings.default_location_id,
      auto_deduct_from_floor: Boolean(settings.auto_deduct_from_floor),
      allow_negative_stock: Boolean(settings.allow_negative_stock),
      require_location_for_orders: Boolean(settings.require_location_for_orders),
      settings: settings.settings ? JSON.parse(settings.settings) : null
    };
  } catch (error) {
    console.error("getStorageLocationSettingsDB error:", error);
    throw error;
  } finally {
    conn.release();
  }
};

/**
 * Tenant için modül ayarlarını günceller
 * @param {number} tenantId - Kiracı ID
 * @param {Object} settings - Ayarlar
 * @returns {boolean} İşlem başarılı mı?
 */
exports.updateStorageLocationSettingsDB = async (tenantId, settings) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const {
      is_enabled,
      default_location_id,
      auto_deduct_from_floor,
      allow_negative_stock,
      require_location_for_orders,
      settings: extraSettings
    } = settings;

    const sql = `
      INSERT INTO storage_location_settings 
      (tenant_id, is_enabled, default_location_id, auto_deduct_from_floor, 
       allow_negative_stock, require_location_for_orders, settings)
      VALUES (?, ?, ?, ?, ?, ?, ?)
      ON DUPLICATE KEY UPDATE
        is_enabled = VALUES(is_enabled),
        default_location_id = VALUES(default_location_id),
        auto_deduct_from_floor = VALUES(auto_deduct_from_floor),
        allow_negative_stock = VALUES(allow_negative_stock),
        require_location_for_orders = VALUES(require_location_for_orders),
        settings = VALUES(settings),
        updated_at = CURRENT_TIMESTAMP
    `;

    const [result] = await conn.query(sql, [
      tenantId,
      is_enabled,
      default_location_id,
      auto_deduct_from_floor,
      allow_negative_stock,
      require_location_for_orders,
      extraSettings ? JSON.stringify(extraSettings) : null
    ]);

    return result.affectedRows > 0;
  } catch (error) {
    console.error("updateStorageLocationSettingsDB error:", error);
    throw error;
  } finally {
    conn.release();
  }
};

// ============================================================================
// LOKASYON YÖNETİMİ
// ============================================================================

/**
 * Tüm depo/bar lokasyonlarını getirir
 * @param {number} tenantId - Kiracı ID
 * @param {Object} filters - Filtreler (type, is_active)
 * @returns {Array} Lokasyonlar listesi
 */
exports.getStorageLocationsDB = async (tenantId, filters = {}) => {
  const conn = await getMySqlPromiseConnection();
  try {
    let whereConditions = ["sl.tenant_id = ?"];
    let queryParams = [tenantId];

    if (filters.type) {
      whereConditions.push("sl.type = ?");
      queryParams.push(filters.type);
    }

    if (filters.is_active !== undefined) {
      whereConditions.push("sl.is_active = ?");
      queryParams.push(filters.is_active);
    }

    const whereClause = whereConditions.join(" AND ");

    const sql = `
      SELECT 
        sl.*,
        COUNT(DISTINCT slf.floor_id) as connected_floors_count,
        COUNT(DISTINCT sli.inventory_item_id) as inventory_items_count,
        COALESCE(SUM(sli.quantity), 0) as total_stock_value
      FROM storage_locations sl
      LEFT JOIN storage_location_floors slf ON sl.id = slf.storage_location_id 
        AND slf.is_active = 1 AND slf.tenant_id = sl.tenant_id
      LEFT JOIN storage_location_inventory sli ON sl.id = sli.storage_location_id 
        AND sli.tenant_id = sl.tenant_id
      WHERE ${whereClause}
      GROUP BY sl.id
      ORDER BY sl.is_default DESC, sl.type, sl.name
    `;

    const [rows] = await conn.query(sql, queryParams);
    return rows;
  } catch (error) {
    console.error("getStorageLocationsDB error:", error);
    throw error;
  } finally {
    conn.release();
  }
};

/**
 * Belirli bir lokasyonu getirir
 * @param {number} locationId - Lokasyon ID
 * @param {number} tenantId - Kiracı ID
 * @returns {Object|null} Lokasyon bilgileri
 */
exports.getStorageLocationByIdDB = async (locationId, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const sql = `
      SELECT 
        sl.*,
        COUNT(DISTINCT slf.floor_id) as connected_floors_count,
        COUNT(DISTINCT sli.inventory_item_id) as inventory_items_count,
        COALESCE(SUM(sli.quantity), 0) as total_stock_value
      FROM storage_locations sl
      LEFT JOIN storage_location_floors slf ON sl.id = slf.storage_location_id 
        AND slf.is_active = 1 AND slf.tenant_id = sl.tenant_id
      LEFT JOIN storage_location_inventory sli ON sl.id = sli.storage_location_id 
        AND sli.tenant_id = sl.tenant_id
      WHERE sl.id = ? AND sl.tenant_id = ?
      GROUP BY sl.id
    `;

    const [rows] = await conn.query(sql, [locationId, tenantId]);
    return rows.length > 0 ? rows[0] : null;
  } catch (error) {
    console.error("getStorageLocationByIdDB error:", error);
    throw error;
  } finally {
    conn.release();
  }
};

/**
 * Yeni depo/bar lokasyonu oluşturur
 * @param {Object} locationData - Lokasyon bilgileri
 * @param {number} tenantId - Kiracı ID
 * @returns {number} Oluşturulan lokasyonun ID'si
 */
exports.createStorageLocationDB = async (locationData, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    await conn.beginTransaction();

    const {
      name,
      type,
      description,
      location_code,
      is_default,
      capacity_info
    } = locationData;

    // Eğer varsayılan olarak işaretleniyorsa, diğerlerini varsayılan olmaktan çıkar
    if (is_default) {
      await conn.query(
        "UPDATE storage_locations SET is_default = 0 WHERE tenant_id = ?",
        [tenantId]
      );
    }

    const sql = `
      INSERT INTO storage_locations 
      (name, type, description, location_code, is_default, capacity_info, tenant_id)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `;

    const [result] = await conn.query(sql, [
      name,
      type,
      description || null,
      location_code || null,
      is_default || false,
      capacity_info ? JSON.stringify(capacity_info) : null,
      tenantId
    ]);

    await conn.commit();
    return result.insertId;
  } catch (error) {
    await conn.rollback();
    console.error("createStorageLocationDB error:", error);
    throw error;
  } finally {
    conn.release();
  }
};

/**
 * Depo/bar lokasyonunu günceller
 * @param {number} locationId - Lokasyon ID
 * @param {Object} locationData - Güncellenecek veriler
 * @param {number} tenantId - Kiracı ID
 * @returns {boolean} İşlem başarılı mı?
 */
exports.updateStorageLocationDB = async (locationId, locationData, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    await conn.beginTransaction();

    const {
      name,
      type,
      description,
      location_code,
      is_active,
      is_default,
      capacity_info
    } = locationData;

    // Eğer varsayılan olarak işaretleniyorsa, diğerlerini varsayılan olmaktan çıkar
    if (is_default) {
      await conn.query(
        "UPDATE storage_locations SET is_default = 0 WHERE tenant_id = ? AND id != ?",
        [tenantId, locationId]
      );
    }

    const sql = `
      UPDATE storage_locations 
      SET name = ?, type = ?, description = ?, location_code = ?, 
          is_active = ?, is_default = ?, capacity_info = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ? AND tenant_id = ?
    `;

    const [result] = await conn.query(sql, [
      name,
      type,
      description || null,
      location_code || null,
      is_active !== undefined ? is_active : true,
      is_default || false,
      capacity_info ? JSON.stringify(capacity_info) : null,
      locationId,
      tenantId
    ]);

    await conn.commit();
    return result.affectedRows > 0;
  } catch (error) {
    await conn.rollback();
    console.error("updateStorageLocationDB error:", error);
    throw error;
  } finally {
    conn.release();
  }
};

/**
 * Depo/bar lokasyonunu siler
 * @param {number} locationId - Lokasyon ID
 * @param {number} tenantId - Kiracı ID
 * @returns {boolean} İşlem başarılı mı?
 */
exports.deleteStorageLocationDB = async (locationId, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    // Önce bu lokasyonun varsayılan olup olmadığını kontrol et
    const [location] = await conn.query(
      "SELECT is_default FROM storage_locations WHERE id = ? AND tenant_id = ?",
      [locationId, tenantId]
    );

    if (location.length === 0) {
      throw new Error("Lokasyon bulunamadı");
    }

    if (location[0].is_default) {
      throw new Error("Varsayılan lokasyon silinemez");
    }

    // Stok hareketi var mı kontrol et
    const [movements] = await conn.query(
      "SELECT COUNT(*) as count FROM storage_location_movements WHERE storage_location_id = ? AND tenant_id = ?",
      [locationId, tenantId]
    );

    if (movements[0].count > 0) {
      throw new Error("Bu lokasyonda stok hareketleri bulunduğu için silinemez");
    }

    const sql = "DELETE FROM storage_locations WHERE id = ? AND tenant_id = ?";
    const [result] = await conn.query(sql, [locationId, tenantId]);

    return result.affectedRows > 0;
  } catch (error) {
    console.error("deleteStorageLocationDB error:", error);
    throw error;
  } finally {
    conn.release();
  }
};

// ============================================================================
// FLOOR-LOKASYON EŞLEŞTİRME
// ============================================================================

/**
 * Floor-lokasyon eşleştirmelerini getirir
 * @param {number} tenantId - Kiracı ID
 * @param {number} floorId - Floor ID (opsiyonel)
 * @param {number} locationId - Lokasyon ID (opsiyonel)
 * @returns {Array} Eşleştirmeler listesi
 */
exports.getStorageLocationFloorsDB = async (tenantId, floorId = null, locationId = null) => {
  const conn = await getMySqlPromiseConnection();
  try {
    let whereConditions = ["slf.tenant_id = ?"];
    let queryParams = [tenantId];

    if (floorId) {
      whereConditions.push("slf.floor_id = ?");
      queryParams.push(floorId);
    }

    if (locationId) {
      whereConditions.push("slf.storage_location_id = ?");
      queryParams.push(locationId);
    }

    const whereClause = whereConditions.join(" AND ");

    const sql = `
      SELECT
        slf.*,
        sl.name as location_name,
        sl.type as location_type,
        sl.location_code,
        f.title as floor_title,
        f.description as floor_description
      FROM storage_location_floors slf
      JOIN storage_locations sl ON slf.storage_location_id = sl.id
      JOIN floors f ON slf.floor_id = f.id
      WHERE ${whereClause}
      ORDER BY slf.floor_id, slf.priority ASC
    `;

    const [rows] = await conn.query(sql, queryParams);
    return rows;
  } catch (error) {
    console.error("getStorageLocationFloorsDB error:", error);
    throw error;
  } finally {
    conn.release();
  }
};

/**
 * Floor-lokasyon eşleştirmesi oluşturur
 * @param {Object} mappingData - Eşleştirme bilgileri
 * @param {number} tenantId - Kiracı ID
 * @returns {number} Oluşturulan eşleştirmenin ID'si
 */
exports.createStorageLocationFloorMappingDB = async (mappingData, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const {
      storage_location_id,
      floor_id,
      priority,
      is_active
    } = mappingData;

    const sql = `
      INSERT INTO storage_location_floors
      (storage_location_id, floor_id, priority, is_active, tenant_id)
      VALUES (?, ?, ?, ?, ?)
    `;

    const [result] = await conn.query(sql, [
      storage_location_id,
      floor_id,
      priority || 1,
      is_active !== undefined ? is_active : true,
      tenantId
    ]);

    return result.insertId;
  } catch (error) {
    console.error("createStorageLocationFloorMappingDB error:", error);
    throw error;
  } finally {
    conn.release();
  }
};

/**
 * Floor-lokasyon eşleştirmesini günceller
 * @param {number} mappingId - Eşleştirme ID
 * @param {Object} mappingData - Güncellenecek veriler
 * @param {number} tenantId - Kiracı ID
 * @returns {boolean} İşlem başarılı mı?
 */
exports.updateStorageLocationFloorMappingDB = async (mappingId, mappingData, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const {
      priority,
      is_active
    } = mappingData;

    const sql = `
      UPDATE storage_location_floors
      SET priority = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ? AND tenant_id = ?
    `;

    const [result] = await conn.query(sql, [
      priority || 1,
      is_active !== undefined ? is_active : true,
      mappingId,
      tenantId
    ]);

    return result.affectedRows > 0;
  } catch (error) {
    console.error("updateStorageLocationFloorMappingDB error:", error);
    throw error;
  } finally {
    conn.release();
  }
};

/**
 * Floor-lokasyon eşleştirmesini siler
 * @param {number} mappingId - Eşleştirme ID
 * @param {number} tenantId - Kiracı ID
 * @returns {boolean} İşlem başarılı mı?
 */
exports.deleteStorageLocationFloorMappingDB = async (mappingId, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const sql = "DELETE FROM storage_location_floors WHERE id = ? AND tenant_id = ?";
    const [result] = await conn.query(sql, [mappingId, tenantId]);

    return result.affectedRows > 0;
  } catch (error) {
    console.error("deleteStorageLocationFloorMappingDB error:", error);
    throw error;
  } finally {
    conn.release();
  }
};

// ============================================================================
// LOKASYON BAZLI STOK YÖNETİMİ
// ============================================================================

/**
 * Floor ID'sine göre hangi lokasyondan stok düşüleceğini belirler
 * @param {number} floorId - Floor ID
 * @param {number} tenantId - Kiracı ID
 * @returns {Object|null} Lokasyon bilgileri
 */
exports.getLocationForFloorDB = async (floorId, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const sql = `
      SELECT
        sl.id,
        sl.name,
        sl.type,
        sl.location_code,
        slf.priority
      FROM storage_location_floors slf
      JOIN storage_locations sl ON slf.storage_location_id = sl.id
      WHERE slf.floor_id = ?
        AND slf.tenant_id = ?
        AND slf.is_active = 1
        AND sl.is_active = 1
      ORDER BY slf.priority ASC
      LIMIT 1
    `;

    const [rows] = await conn.query(sql, [floorId, tenantId]);
    return rows.length > 0 ? rows[0] : null;
  } catch (error) {
    console.error("getLocationForFloorDB error:", error);
    throw error;
  } finally {
    conn.release();
  }
};

/**
 * Lokasyon bazlı stok miktarlarını getirir
 * @param {number} locationId - Lokasyon ID
 * @param {number} tenantId - Kiracı ID
 * @param {Object} filters - Filtreler
 * @returns {Array} Stok listesi
 */
exports.getLocationInventoryDB = async (locationId, tenantId, filters = {}) => {
  const conn = await getMySqlPromiseConnection();
  try {
    let whereConditions = ["sli.storage_location_id = ?", "sli.tenant_id = ?"];
    let queryParams = [locationId, tenantId];

    if (filters.low_stock) {
      whereConditions.push("sli.quantity <= COALESCE(sli.min_quantity_threshold, ii.min_quantity_threshold, 0)");
    }

    if (filters.out_of_stock) {
      whereConditions.push("sli.quantity <= 0");
    }

    if (filters.inventory_item_id) {
      whereConditions.push("sli.inventory_item_id = ?");
      queryParams.push(filters.inventory_item_id);
    }

    const whereClause = whereConditions.join(" AND ");

    const sql = `
      SELECT
        sli.*,
        ii.title as item_title,
        ii.unit,
        ii.min_quantity_threshold as global_min_threshold,
        CASE
          WHEN sli.quantity <= 0 THEN 'out'
          WHEN sli.quantity <= COALESCE(sli.min_quantity_threshold, ii.min_quantity_threshold, 0) THEN 'low'
          ELSE 'in'
        END as status
      FROM storage_location_inventory sli
      JOIN inventory_items ii ON sli.inventory_item_id = ii.id
      WHERE ${whereClause}
      ORDER BY ii.title
    `;

    const [rows] = await conn.query(sql, queryParams);
    return rows;
  } catch (error) {
    console.error("getLocationInventoryDB error:", error);
    throw error;
  } finally {
    conn.release();
  }
};

/**
 * Lokasyon bazlı stok hareketi yapar
 * @param {Object} movementData - Hareket bilgileri
 * @param {number} tenantId - Kiracı ID
 * @param {string} username - İşlemi yapan kullanıcı
 * @returns {boolean} İşlem başarılı mı?
 */
exports.addLocationStockMovementDB = async (movementData, tenantId, username) => {
  const conn = await getMySqlPromiseConnection();
  try {
    await conn.beginTransaction();

    const {
      storage_location_id,
      inventory_item_id,
      movement_type,
      quantity,
      reference_type,
      reference_id,
      from_location_id,
      to_location_id,
      notes,
      unit_cost
    } = movementData;

    // Mevcut stok miktarını al
    const [currentStock] = await conn.query(
      `SELECT quantity FROM storage_location_inventory
       WHERE storage_location_id = ? AND inventory_item_id = ? AND tenant_id = ? FOR UPDATE`,
      [storage_location_id, inventory_item_id, tenantId]
    );

    let previousQuantity = 0;
    if (currentStock.length > 0) {
      previousQuantity = parseFloat(currentStock[0].quantity);
    }

    // Hareket türüne göre yeni miktarı hesapla
    let deltaQuantity = 0;
    switch (movement_type) {
      case 'IN':
      case 'TRANSFER_IN':
        deltaQuantity = parseFloat(quantity);
        break;
      case 'OUT':
      case 'TRANSFER_OUT':
      case 'WASTAGE':
        deltaQuantity = -parseFloat(quantity);
        break;
      case 'ADJUSTMENT':
        deltaQuantity = parseFloat(quantity); // Adjustment pozitif veya negatif olabilir
        break;
      default:
        throw new Error("Geçersiz hareket türü");
    }

    const newQuantity = previousQuantity + deltaQuantity;

    // Negatif stok kontrolü (ayarlara göre)
    const [settings] = await conn.query(
      "SELECT allow_negative_stock FROM storage_location_settings WHERE tenant_id = ?",
      [tenantId]
    );

    if (newQuantity < 0 && (!settings.length || !settings[0].allow_negative_stock)) {
      throw new Error("Yetersiz stok! Mevcut miktar: " + previousQuantity);
    }

    // Stok miktarını güncelle veya oluştur
    const upsertStockSql = `
      INSERT INTO storage_location_inventory
      (storage_location_id, inventory_item_id, quantity, tenant_id)
      VALUES (?, ?, ?, ?)
      ON DUPLICATE KEY UPDATE
        quantity = VALUES(quantity),
        last_movement_date = CURRENT_TIMESTAMP
    `;

    await conn.query(upsertStockSql, [
      storage_location_id,
      inventory_item_id,
      newQuantity,
      tenantId
    ]);

    // Hareket kaydını oluştur
    const movementSql = `
      INSERT INTO storage_location_movements
      (storage_location_id, inventory_item_id, movement_type, quantity,
       previous_quantity, new_quantity, reference_type, reference_id,
       from_location_id, to_location_id, notes, unit_cost, total_cost,
       created_by, tenant_id)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const totalCost = unit_cost ? parseFloat(unit_cost) * parseFloat(quantity) : null;

    await conn.query(movementSql, [
      storage_location_id,
      inventory_item_id,
      movement_type,
      Math.abs(deltaQuantity),
      previousQuantity,
      newQuantity,
      reference_type || null,
      reference_id || null,
      from_location_id || null,
      to_location_id || null,
      notes || null,
      unit_cost || null,
      totalCost,
      username,
      tenantId
    ]);

    await conn.commit();
    return true;
  } catch (error) {
    await conn.rollback();
    console.error("addLocationStockMovementDB error:", error);
    throw error;
  } finally {
    conn.release();
  }
};

/**
 * Lokasyonlar arası stok transferi yapar
 * @param {Object} transferData - Transfer bilgileri
 * @param {number} tenantId - Kiracı ID
 * @param {string} username - İşlemi yapan kullanıcı
 * @returns {boolean} İşlem başarılı mı?
 */
exports.transferStockBetweenLocationsDB = async (transferData, tenantId, username) => {
  const conn = await getMySqlPromiseConnection();
  try {
    await conn.beginTransaction();

    const {
      from_location_id,
      to_location_id,
      inventory_item_id,
      quantity,
      notes
    } = transferData;

    // Kaynak lokasyondan stok düş
    await this.addLocationStockMovementDB({
      storage_location_id: from_location_id,
      inventory_item_id,
      movement_type: 'TRANSFER_OUT',
      quantity,
      reference_type: 'transfer',
      to_location_id,
      notes: notes || 'Lokasyon transferi - çıkış'
    }, tenantId, username);

    // Hedef lokasyona stok ekle
    await this.addLocationStockMovementDB({
      storage_location_id: to_location_id,
      inventory_item_id,
      movement_type: 'TRANSFER_IN',
      quantity,
      reference_type: 'transfer',
      from_location_id,
      notes: notes || 'Lokasyon transferi - giriş'
    }, tenantId, username);

    await conn.commit();
    return true;
  } catch (error) {
    await conn.rollback();
    console.error("transferStockBetweenLocationsDB error:", error);
    throw error;
  } finally {
    conn.release();
  }
};

/**
 * Lokasyon bazlı stok hareketlerini getirir
 * @param {number} tenantId - Kiracı ID
 * @param {Object} filters - Filtreler
 * @returns {Array} Hareket listesi
 */
exports.getLocationStockMovementsDB = async (tenantId, filters = {}) => {
  const conn = await getMySqlPromiseConnection();
  try {
    let whereConditions = ["slm.tenant_id = ?"];
    let queryParams = [tenantId];

    if (filters.storage_location_id) {
      whereConditions.push("slm.storage_location_id = ?");
      queryParams.push(filters.storage_location_id);
    }

    if (filters.inventory_item_id) {
      whereConditions.push("slm.inventory_item_id = ?");
      queryParams.push(filters.inventory_item_id);
    }

    if (filters.movement_type) {
      whereConditions.push("slm.movement_type = ?");
      queryParams.push(filters.movement_type);
    }

    if (filters.from_date) {
      whereConditions.push("slm.created_at >= ?");
      queryParams.push(filters.from_date);
    }

    if (filters.to_date) {
      whereConditions.push("slm.created_at <= ?");
      queryParams.push(filters.to_date);
    }

    const whereClause = whereConditions.join(" AND ");

    const sql = `
      SELECT
        slm.*,
        sl.name as location_name,
        sl.type as location_type,
        sl.location_code,
        ii.title as item_title,
        ii.unit,
        from_sl.name as from_location_name,
        to_sl.name as to_location_name,
        u.name as created_by_name
      FROM storage_location_movements slm
      JOIN storage_locations sl ON slm.storage_location_id = sl.id
      JOIN inventory_items ii ON slm.inventory_item_id = ii.id
      LEFT JOIN storage_locations from_sl ON slm.from_location_id = from_sl.id
      LEFT JOIN storage_locations to_sl ON slm.to_location_id = to_sl.id
      LEFT JOIN users u ON slm.created_by = u.username AND u.tenant_id = slm.tenant_id
      WHERE ${whereClause}
      ORDER BY slm.created_at DESC
      LIMIT ${filters.limit || 100}
    `;

    const [rows] = await conn.query(sql, queryParams);
    return rows;
  } catch (error) {
    console.error("getLocationStockMovementsDB error:", error);
    throw error;
  } finally {
    conn.release();
  }
};

/**
 * Sipariş için floor bazlı stok düşümü yapar
 * @param {number} orderId - Sipariş ID
 * @param {number} floorId - Floor ID
 * @param {Array} recipeItems - Reçete kalemleri
 * @param {number} tenantId - Kiracı ID
 * @param {string} username - İşlemi yapan kullanıcı
 * @returns {boolean} İşlem başarılı mı?
 */
exports.deductStockForOrderDB = async (orderId, floorId, recipeItems, tenantId, username) => {
  const conn = await getMySqlPromiseConnection();
  try {
    // Önce modül aktif mi kontrol et
    const settings = await this.getStorageLocationSettingsDB(tenantId);
    if (!settings.is_enabled || !settings.auto_deduct_from_floor) {
      return false; // Modül kapalı, normal stok sistemi kullanılacak
    }

    // Floor için lokasyon bul
    const location = await this.getLocationForFloorDB(floorId, tenantId);
    if (!location) {
      // Floor için lokasyon bulunamadı, varsayılan lokasyonu kullan
      if (settings.default_location_id) {
        location = { id: settings.default_location_id };
      } else {
        return false; // Varsayılan lokasyon da yok, normal sistem kullanılacak
      }
    }

    await conn.beginTransaction();

    // Her reçete kalemi için stok düş
    for (const item of recipeItems) {
      await this.addLocationStockMovementDB({
        storage_location_id: location.id,
        inventory_item_id: item.inventory_item_id,
        movement_type: 'OUT',
        quantity: item.quantity,
        reference_type: 'order',
        reference_id: orderId,
        notes: `Sipariş #${orderId} için otomatik düşüm`
      }, tenantId, username);
    }

    await conn.commit();
    return true;
  } catch (error) {
    await conn.rollback();
    console.error("deductStockForOrderDB error:", error);
    throw error;
  } finally {
    conn.release();
  }
};

/**
 * Lokasyon bazlı stok özet raporu
 * @param {number} tenantId - Kiracı ID
 * @returns {Array} Özet rapor
 */
exports.getLocationStockSummaryDB = async (tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const sql = `
      SELECT
        sl.id,
        sl.name,
        sl.type,
        sl.location_code,
        COUNT(sli.inventory_item_id) as total_items,
        SUM(CASE WHEN sli.quantity > COALESCE(sli.min_quantity_threshold, ii.min_quantity_threshold, 0) THEN 1 ELSE 0 END) as in_stock_items,
        SUM(CASE WHEN sli.quantity <= COALESCE(sli.min_quantity_threshold, ii.min_quantity_threshold, 0) AND sli.quantity > 0 THEN 1 ELSE 0 END) as low_stock_items,
        SUM(CASE WHEN sli.quantity <= 0 THEN 1 ELSE 0 END) as out_of_stock_items,
        COALESCE(SUM(sli.quantity * COALESCE(slm.unit_cost, 0)), 0) as estimated_value
      FROM storage_locations sl
      LEFT JOIN storage_location_inventory sli ON sl.id = sli.storage_location_id
      LEFT JOIN inventory_items ii ON sli.inventory_item_id = ii.id
      LEFT JOIN (
        SELECT storage_location_id, inventory_item_id, unit_cost,
               ROW_NUMBER() OVER (PARTITION BY storage_location_id, inventory_item_id ORDER BY created_at DESC) as rn
        FROM storage_location_movements
        WHERE unit_cost IS NOT NULL AND tenant_id = ?
      ) slm ON sli.storage_location_id = slm.storage_location_id
           AND sli.inventory_item_id = slm.inventory_item_id
           AND slm.rn = 1
      WHERE sl.tenant_id = ? AND sl.is_active = 1
      GROUP BY sl.id
      ORDER BY sl.is_default DESC, sl.name
    `;

    const [rows] = await conn.query(sql, [tenantId, tenantId]);
    return rows;
  } catch (error) {
    console.error("getLocationStockSummaryDB error:", error);
    throw error;
  } finally {
    conn.release();
  }
};
