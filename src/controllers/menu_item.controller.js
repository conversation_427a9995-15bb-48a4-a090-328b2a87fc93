const {
    addMenuItemDB,
    updateMenuItemDB,
    deleteMenuItemDB,
    addMenuItemAddonDB,
    updateMenuItemAddonDB,
    deleteMenuItemAddonDB,
    getMenuItemAddonsDB,
    getAllAddonsDB,
    addMenuItemVariantDB,
    updateMenuItemVariantDB,
    deleteMenuItemVariantDB,
    getMenuItemVariantsDB,
    getAllVariantsDB,
    getAllMenuItemsDB,
    getMenuItemDB,
    updateMenuItemImageDB,
    changeMenuItemVisibilityDB,
    changeMenuItemQRVisibilityDB,
    updateMenuItemActiveHoursDB,
    getRecipeItemsDB,
    addRecipeItemDB,
    deleteRecipeItemDB,
    updateRecipeItemDB,

    updateCategoryOrderDB,
    updateMenuItemOrderDB,
    addMenuBulkDB,
    updateMenuItemsBulkDB,
    updateMenuItemBulkDB,

} = require("../services/menu_item.service");

const path = require("path")
const fs = require("fs");
const { getInventoryItemsDB } = require("../services/inventory.service");


exports.addMenuItem = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const {title, description, price, netPrice, taxId, categoryId} = req.body;

        if(!(title && price)) {
            return res.status(400).json({
                success: false,
                message: "Lütfen gerekli alanları doldurunuz!" // Translate message
            });
        }

        const menuItemId = await addMenuItemDB(title, description, price, netPrice, taxId, categoryId, tenantId);

        return res.status(200).json({
            success: true,
            message: "Ürün başarıyla eklendi.", // Translate message
            menuItemId
        })
    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin." // Translate message
        });
    }
};

exports.updateMenuItem = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const id = req.params.id;
        const {title, description, price, netPrice, taxId, categoryId} = req.body;

        if(!(title && price)) {
            return res.status(400).json({
                success: false,
                message: req.__("menu_item_provide_required_details") // Translate message
            });
        }

        await updateMenuItemDB(id, title, description, price, netPrice, taxId, categoryId, tenantId);

        return res.status(200).json({
            success: true,
            message: "ürün başarıyla güncellendi." // Translate message
        })
    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "ürün güncellenemedi. Lütfen daha sonra tekrar deneyin." // Translate message
        });
    }
};

exports.uploadMenuItemPhoto = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const id = req.params.id;

        const file = req.files.image;

        const imagePath = path.join(__dirname, `../../public/${tenantId}/`) + id;

        if(!fs.existsSync(path.join(__dirname, `../../public/${tenantId}/`))) {
            fs.mkdirSync(path.join(__dirname, `../../public/${tenantId}/`));
        }

        const imageURL = `/public/${tenantId}/${id}`;

        await file.mv(imagePath);
        await updateMenuItemImageDB(id, imageURL, tenantId);

        return res.status(200).json({
            success: true,
            message: "Menu Item Image Uploaded.",
            imageURL: imageURL
        })
    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin."
        });
    }
};

exports.removeMenuItemPhoto = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const id = req.params.id;
        const imagePath = path.join(__dirname, `../../public/${tenantId}/`) + id;

        fs.unlinkSync(imagePath)

        await updateMenuItemImageDB(id, null, tenantId);

        return res.status(200).json({
            success: true,
            message: "Menu Item Image Removed.",
        })
    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin."
        });
    }
};

exports.deleteMenuItem = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const id = req.params.id;

        await deleteMenuItemDB(id, tenantId);

        return res.status(200).json({
            success: true,
            message: "Menu Item Deleted."
        });
    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin."
        });
    }
};

exports.changeMenuItemVisibility = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const id = req.params.id;
        const isEnabled = req.body.isEnabled;

        await changeMenuItemVisibilityDB(id, isEnabled, tenantId);

        return res.status(200).json({
            success: true,
            message: "Menü öğesi başarıyla güncellendi." // Translate message
        });
    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin." // Translate message
        });
    }
};

exports.changeMenuItemQRVisibility = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const id = req.params.id;
        const isVisibleInQrMenu = req.body.isVisibleInQRMenu;

        if (isVisibleInQrMenu === undefined || isVisibleInQrMenu === null) {
            return res.status(400).json({
                success: false,
                message: "isVisibleInQrMenu alanı gereklidir."
            });
        }

        await changeMenuItemQRVisibilityDB(id, isVisibleInQrMenu, tenantId);

        return res.status(200).json({
            success: true,
            message: "QR menü görünürlüğü başarıyla güncellendi."
        });
    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin."
        });
    }
};

exports.updateMenuItemActiveHours = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const id = req.params.id;
        const { activeStartTime, activeEndTime } = req.body;

        // Saat formatını kontrol et (HH:MM:SS)
        const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$/;

        if (activeStartTime && !timeRegex.test(activeStartTime)) {
            return res.status(400).json({
                success: false,
                message: "activeStartTime formatı HH:MM:SS olmalıdır."
            });
        }

        if (activeEndTime && !timeRegex.test(activeEndTime)) {
            return res.status(400).json({
                success: false,
                message: "activeEndTime formatı HH:MM:SS olmalıdır."
            });
        }

        await updateMenuItemActiveHoursDB(id, activeStartTime || null, activeEndTime || null, tenantId);

        return res.status(200).json({
            success: true,
            message: "Ürün aktif saatleri başarıyla güncellendi."
        });
    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin."
        });
    }
};

exports.getAllMenuItems = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const [menuItems, addons, variants] = await Promise.all([
            getAllMenuItemsDB(tenantId),
            getAllAddonsDB(tenantId),
            getAllVariantsDB(tenantId)
        ]);

        const formattedMenuItems = menuItems.map(item => {
            const itemAddons = addons.filter(addon => addon.item_id == item.id);
            const itemVariants = variants.filter(variant => variant.item_id == item.id);

            return {
                ...item,
                addons: [...itemAddons],
                variants: [...itemVariants],
            }
        })

        return res.status(200).json(formattedMenuItems);
    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin." // Translate message
        });
    }
};

exports.getMenuItem = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const id = req.params.id;

        const [menuItem, addons, variants, recipeItems, inventoryItemsResult] = await Promise.all([
            getMenuItemDB(id, tenantId),
            getMenuItemAddonsDB(id, tenantId),
            getMenuItemVariantsDB(id, tenantId),
            getRecipeItemsDB(id, tenantId), //Menu item Recipe Items
            getInventoryItemsDB('all' /**status */, tenantId)
        ]);

        const formattedMenuItem = {
            ...menuItem,
            addons: [...addons],
            variants: [...variants],
            recipeItems: [...recipeItems],
        }

        const { items: inventoryItems } = inventoryItemsResult;

        return res.status(200).json({formattedMenuItem, inventoryItems});
    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin." // Translate message
        });
    }
};

/* Addons */
exports.addMenuItemAddon = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const itemId = req.params.id;
        const {title, price} = req.body;

        if(!(title)) {
            return res.status(400).json({
                success: false,
                message: "Please provide required details: title"
            });
        }

        const menuItemAddonId = await addMenuItemAddonDB(itemId, title, price, tenantId);

        return res.status(200).json({
            success: true,
            message: "Menu Item Addon Added.",
            addonId: menuItemAddonId
        })
    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin."
        });
    }
};
exports.updateMenuItemAddon = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const itemId = req.params.id;
        const addonId = req.params.addonId;
        const {title, price} = req.body;

        if(!(title)) {
            return res.status(400).json({
                success: false,
                message: "Please provide required details: title"
            });
        }

        await updateMenuItemAddonDB(itemId, addonId, title, price, tenantId);

        return res.status(200).json({
            success: true,
            message: "Menu Item Addon Updated.",
        })
    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin."
        });
    }
};
exports.deleteMenuItemAddon = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const itemId = req.params.id;
        const addonId = req.params.addonId;

        await deleteMenuItemAddonDB(itemId, addonId, tenantId);

        return res.status(200).json({
            success: true,
            message: "Menu Item Addon Deleted.",
        });
    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin."
        });
    }
};
exports.getMenuItemAddons = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const itemId = req.params.id;

        const itemAddons = await getMenuItemAddonsDB(itemId, tenantId);

        if(itemAddons.length == 0) {
            return res.status(404).json({
                success: false,
                message: "No addons found for this item!"
            });
        }

        return res.status(200).json(itemAddons);
    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin."
        });
    }
};
exports.getAllAddons = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const addons = await getAllAddonsDB(tenantId);

        return res.status(200).json(addons);
    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin."
        });
    }
};
/* Addons */


/* Variants */
exports.addMenuItemVariant = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const itemId = req.params.id;
        const {title, price} = req.body;

        if(!(title)) {
            return res.status(400).json({
                success: false,
                message: "Please provide required details: title"
            });
        }

        const menuItemVariantId = await addMenuItemVariantDB(itemId, title, price, tenantId);

        return res.status(200).json({
            success: true,
            message: "Menu Item Variant Added.",
            variantId: menuItemVariantId
        })
    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin."
        });
    }
};
exports.updateMenuItemVariant = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const itemId = req.params.id;
        const variantId = req.params.variantId;
        const {title, price} = req.body;

        if(!(title)) {
            return res.status(400).json({
                success: false,
                message: "Please provide required details: title"
            });
        }

        await updateMenuItemVariantDB(itemId, variantId, title, price, tenantId);

        return res.status(200).json({
            success: true,
            message: "Menu Item Variant Updated."
        })
    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin."
        });
    }
};
exports.deleteMenuItemVariant = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const itemId = req.params.id;
        const variantId = req.params.variantId;

        await deleteMenuItemVariantDB(itemId, variantId, tenantId);

        return res.status(200).json({
            success: true,
            message: "Menu Item Variant Deleted."
        })
    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin."
        });
    }
};
exports.getMenuItemVariants = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const itemId = req.params.id;

        const itemVariants = await getMenuItemVariantsDB(itemId, tenantId);

        if(itemVariants.length == 0) {
            return res.status(404).json({
                success: false,
                message: "No variants found for this item!"
            });
        }

        return res.status(200).json(itemVariants);
    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin."
        });
    }
};
exports.getAllVariants = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const allVariants = await getAllVariantsDB(tenantId);

        return res.status(200).json(allVariants);
    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin."
        });
    }
};
/* Variants */

exports.updateMenuItemBulk = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const updateData = req.body;

        if (!updateData || Object.keys(updateData).length === 0) {
            return res.status(400).json({
                success: false,
                message: "Please provide valid menu items data for bulk update."
            });
        }

        // updateMenuItemBulkDB fonksiyonunu çağır
        const result = await updateMenuItemBulkDB(updateData, tenantId);

        return res.status(200).json({
            success: true,
            message: `${Object.keys(updateData).length} menu items successfully updated.`,
            updatedItems: result
        });
    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin."
        });
    }
};

/** Recipes */
exports.addRecipeItem = async (req, res) => {
    try {
      const tenantId = req.user.tenant_id;
      const menuItemId = req.params.id;
      const { variantId, addonId, ingredientId, quantity } = req.body;

      if (!menuItemId || !variantId && !addonId && !ingredientId) {
        return res.status(400).json({
          success: false,
          message: "Please provide all required data"
        });
      }

      if (isNaN(quantity) || quantity <= 0) {
        return res.status(400).json({
          success: false,
          message: "Please provide a valid quantity."
        });
      }

      const recipeItemId = await addRecipeItemDB(menuItemId, variantId, addonId, ingredientId, quantity, tenantId);

      return res.status(200).json({
        success: true,
        message: "Recipe Item Added.",
        recipeItemId: recipeItemId
      });
    } catch (error) {
      console.error(error);

      if (error.errno === 1062) {
        return res.status(400).json({
          success: false,
          message: "Recipe item already exists. Please update the quantity if needed."
        });
      }

      return res.status(500).json({
        success: false,
        message: "Something went wrong! Please try again later."
      });
    }
  };

  exports.updateRecipeItem = async (req, res) => {
    try {
      const tenantId = req.user.tenant_id;
      const menuItemId = req.params.id;
      const recipeItemId = req.params.recipeItemId;
      const { variantId, addonId, ingredientId, quantity } = req.body;

      if (!menuItemId || !recipeItemId || (!variantId && !addonId && !ingredientId)) {
        return res.status(400).json({
          success: false,
          message: "Please provide all required data.",
        });
      }

      if (isNaN(quantity) || quantity <= 0) {
        return res.status(400).json({
          success: false,
          message: "Please provide a valid quantity.",
        });
      }

      await updateRecipeItemDB(recipeItemId, menuItemId, variantId, addonId, ingredientId, quantity, tenantId);

      return res.status(200).json({
        success: true,
        message: "Recipe Item Updated.",
      });
    } catch (error) {
      console.error(error);

      if (error.errno === 1062) {
        return res.status(400).json({
          success: false,
          message: "Recipe item already exists. Please update the quantity if needed."
        });
      }

      return res.status(500).json({
        success: false,
        message: "Something went wrong! Please try again later.",
      });
    }
  };


  exports.getRecipeItems = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const menuItemId = req.params.id;

        const recipeItems = await getRecipeItemsDB(menuItemId, tenantId);

        return res.status(200).json(recipeItems);
    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Something went wrong! Please try again later."
        });
    }
 };

 exports.deleteRecipeItem = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const itemId = req.params.id;
        const recipeItemId = req.params.recipeItemId;

        const {variant = null, addon = null} = req.query;

        await deleteRecipeItemDB(itemId, recipeItemId, variant, addon, tenantId);

        return res.status(200).json({
            success: true,
            message: "Recipe Item Deleted.",
        });
    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Something went wrong! Please try later!"
        });
    }
};

/** Recipes*/


exports.updateMenuItemOrder = async (req, res) => {
    try {
      const tenantId = req.user.tenant_id;
      const { order } = req.body; // Yeni sıralama dizisi

      if (!order || !Array.isArray(order)) {
        return res.status(400).json({
          success: false,
          message: "Lütfen geçerli bir sıralama dizisi sağlayın.",
        });
      }

      const result = await updateMenuItemOrderDB(order, tenantId);

      return res.status(200).json({
        success: true,
        message: result.message,
      });
    } catch (error) {
      console.error("Error in updateMenuItemOrder:", error);
      return res.status(500).json({
        success: false,
        message: "Bir şeyler ters gitti! Lütfen tekrar deneyin.",
      });
    }
  };


  exports.BulkUploadMenu = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const { items } = req.body; // Toplu gönderilecek menü öğeleri

        if (!Array.isArray(items) || items.length === 0) {
            return res.status(400).json({
                success: false,
                message: "Please provide valid menu items for bulk upload.",
            });
        }

        // addMenuBulkDB fonksiyonuna menü öğelerini ve tenantId'yi gönder
        const result = await addMenuBulkDB(items, tenantId);

        return res.status(200).json({
            success: true,
            message: `${items.length} menu items successfully added.`,
            affectedRows: result.affectedRows,
        });
    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin.",
        });
    }
};

exports.BulkUpdateMenuItems = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const { menuItems } = req.body; // Toplu güncelleme için gönderilen veriler

        if (!Array.isArray(menuItems) || menuItems.length === 0) {
            return res.status(400).json({
                success: false,
                message: "Please provide valid menu items for bulk update.",
            });
        }

        await updateMenuItemsBulkDB(menuItems, tenantId);

        return res.status(200).json({
            success: true,
            message: `${menuItems.length} menu items successfully updated.`,
        });
    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin.",
        });
    }
};

exports.updateCategoryOrder = async (req, res) => {
    try {
      const tenantId = req.user.tenant_id; // Kullanıcıdan tenant_id alınır
      const { order } = req.body; // Yeni sıralama dizisi

      if (!order || !Array.isArray(order)) {
        return res.status(400).json({
          success: false,
          message: "Lütfen geçerli bir sıralama dizisi sağlayın.",
        });
      }

      const result = await updateCategoryOrderDB(order, tenantId);

      return res.status(200).json({
        success: true,
        message: result.message,
      });
    } catch (error) {
      console.error("Error in updateCategoryOrder:", error);
      return res.status(500).json({
        success: false,
        message: "Bir şeyler ters gitti! Lütfen tekrar deneyin.",
      });
    }
};
