const { 
  getMTSMSettingsDB, 
  saveMTSMSettingsDB, 
  loginToMTSM, 
  isTokenValid,
  getMTSMMatches 
} = require("../services/mtsm.service");

// mTSM Ayarlarını Getir
exports.getMTSMSettings = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    
    const settings = await getMTSMSettingsDB(tenantId);
    
    if (!settings) {
      return res.status(404).json({
        success: false,
        message: "mTSM entegrasyon ayarları bulunamadı"
      });
    }

    // Şifreyi response'da gösterme
    const { password, access_token, refresh_token, ...safeSettings } = settings;
    
    // Token geçerliliğini kontrol et
    const tokenValid = await isTokenValid(tenantId);
    
    return res.status(200).json({
      success: true,
      data: {
        ...safeSettings,
        hasCredentials: !!password,
        tokenValid
      }
    });

  } catch (error) {
    console.error("getMTSMSettings error:", error);
    return res.status(500).json({
      success: false,
      message: "Bir hata oluştu! Lütfen daha sonra tekrar deneyin."
    });
  }
};

// mTSM Ayarlarını Kaydet
exports.saveMTSMSettings = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const { 
      serverUrl, 
      email, 
      password, 
      integrationType,
      webhookOrderListUrl,
      webhookOrderDetailUrl,
      webhookOrderUpdateUrl 
    } = req.body;

    // Validation
    if (!serverUrl || !email || !password) {
      return res.status(400).json({
        success: false,
        message: "Server URL, email ve şifre alanları zorunludur"
      });
    }

    if (!['API', 'WEBHOOK'].includes(integrationType)) {
      return res.status(400).json({
        success: false,
        message: "Entegrasyon tipi API veya WEBHOOK olmalıdır"
      });
    }

    // Webhook tipinde URL'ler zorunlu
    if (integrationType === 'WEBHOOK') {
      if (!webhookOrderListUrl || !webhookOrderDetailUrl || !webhookOrderUpdateUrl) {
        return res.status(400).json({
          success: false,
          message: "Webhook entegrasyonu için tüm webhook URL'leri gereklidir"
        });
      }
    }

    await saveMTSMSettingsDB(tenantId, {
      serverUrl,
      email,
      password,
      integrationType,
      webhookOrderListUrl,
      webhookOrderDetailUrl,
      webhookOrderUpdateUrl
    });

    return res.status(200).json({
      success: true,
      message: "mTSM ayarları başarıyla kaydedildi"
    });

  } catch (error) {
    console.error("saveMTSMSettings error:", error);
    return res.status(500).json({
      success: false,
      message: "Bir hata oluştu! Lütfen daha sonra tekrar deneyin."
    });
  }
};

// mTSM Bağlantısını Test Et
exports.testMTSMConnection = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;

    const result = await loginToMTSM(tenantId);

    if (result.success) {
      return res.status(200).json({
        success: true,
        message: "mTSM bağlantısı başarılı! Token alındı.",
        data: {
          tokenReceived: !!result.accessToken,
          expiresAt: result.expiresAt
        }
      });
    } else {
      return res.status(400).json({
        success: false,
        message: "mTSM bağlantısı başarısız"
      });
    }

  } catch (error) {
    console.error("testMTSMConnection error:", error);
    
    let errorMessage = "Bağlantı testi başarısız";
    
    if (error.response) {
      // mTSM API'den gelen hata
      if (error.response.status === 400) {
        errorMessage = "Kullanıcı adı veya şifre hatalı";
      } else if (error.response.status === 401) {
        errorMessage = "Yetkilendirme hatası";
      } else {
        errorMessage = `mTSM API Hatası: ${error.response.status}`;
      }
    } else if (error.code === 'ECONNREFUSED') {
      errorMessage = "mTSM sunucusuna bağlanılamıyor";
    } else if (error.code === 'ETIMEDOUT') {
      errorMessage = "Bağlantı zaman aşımına uğradı";
    }

    return res.status(400).json({
      success: false,
      message: errorMessage
    });
  }
};

// mTSM Eşleştirmeleri Getir
exports.getMTSMMatchList = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;

    const matches = await getMTSMMatches(tenantId);

    return res.status(200).json({
      success: true,
      data: matches
    });

  } catch (error) {
    console.error("getMTSMMatchList error:", error);
    return res.status(500).json({
      success: false,
      message: "Eşleştirmeler getirilemedi"
    });
  }
};

// mTSM Entegrasyon Durumu
exports.getMTSMStatus = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;

    const settings = await getMTSMSettingsDB(tenantId);
    
    if (!settings) {
      return res.status(200).json({
        success: true,
        data: {
          configured: false,
          connected: false,
          integrationType: null
        }
      });
    }

    const tokenValid = await isTokenValid(tenantId);

    return res.status(200).json({
      success: true,
      data: {
        configured: true,
        connected: tokenValid,
        integrationType: settings.integration_type,
        serverUrl: settings.server_url,
        email: settings.email
      }
    });

  } catch (error) {
    console.error("getMTSMStatus error:", error);
    return res.status(500).json({
      success: false,
      message: "Durum bilgisi alınamadı"
    });
  }
};
