const { nanoid } = require("nanoid");
const { getStoreSettingDB, getTenantConfigDB, deleteTenantDataDB ,updateTenantConfigDB, getFeedBacksDB, setStoreSettingDB, uploadStoreImageDB, deleteStoreImageDB, updateSlidersItemImageDB, removeSlidersItemImageDB, getPrintSettingDB, setPrintSettingDB, getTaxesDB, addTaxDB, updateTaxDB, deleteTaxDB, getTaxDB, addPaymentTypeDB, getPaymentTypesDB, updatePaymentTypeDB, deletePaymentTypeDB, togglePaymentTypeDB, addStoreTableDB, bulkAddStoreTablesDB, getStoreTablesDB, updateStoreTableDB, deleteStoreTableDB, addCategoryDB, getCategoriesDB, updateCategoryDB, updateCategoryImageDB, deleteCategoryDB, getQRMenuCodeDB, updateQRMenuCodeDB, enableMaliModeDB, disableMaliModeDB, getMaliModeStatusDB, getYazarkasaSettingsDB, updateYazarkasaSettingsDB, getYazarkasaDevicesDB, addYazarkasaDeviceDB, updateYazarkasaDeviceDB, deleteYazarkasaDeviceDB, getYazarkasaDeviceByIdDB } = require("../services/settings.service");

const path = require("path")
const fs = require("fs");


exports.clearTenantData = async (req, res) => {
    try {
      const tenantId = req.user.tenant_id;
      const { tables } = req.body;

      if (!Array.isArray(tables) || tables.length === 0) {
        return res.status(400).json({ success: false, message: 'At least one table must be specified' });
      }

      const result = await deleteTenantDataDB(tenantId, tables);
      return res.status(200).json({ success: true, deleted: result });
    } catch(err) {
      console.error('Error clearing tenant data:', err);
      return res.status(500).json({ success: false, message: err.message });
    }
  };

exports.getStoreDetails = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const result = await getStoreSettingDB(tenantId);

        const storeSettings = {
            storeImage : result?.store_image || null,
            storeName: result?.store_name || null,
            address: result?.address || null,
            phone: result?.phone || null,
            email: result?.email || null,
            currency: result?.currency || null,
            image: result?.image || null,
            slides: result?.slides || null,  // Slides alanını ekledik
            isQRMenuEnabled: result?.is_qr_menu_enabled || false,
            isQROrderEnabled: result?.is_qr_order_enabled || false,
            uniqueQRCode: result?.unique_qr_code || null,
            defaultOrderType: result?.default_order_type || 'dinein',
            dineInEnabled: result?.dine_in_enabled || false,
            deliveryEnabled: result?.delivery_enabled || false,
            takeawayEnabled: result?.takeaway_enabled || false,
            imagesPostActive: result?.images_post_active || false,
        };

        return res.status(200).json(storeSettings);
    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin."
        });
    }
};

exports.updateTenantConfig = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id; // Controller içinde çözümleme yapılıyor
        if (!tenantId) {
            return res.status(400).json({
                success: false,
                message: "Tenant ID bulunamadı.",
            });
        }

        const configData = req.body; // Frontend'den gelen yapılandırma verileri
        const result = await updateTenantConfigDB(tenantId, configData); // tenantId ve configData'yı servise gönder

        return res.status(200).json(result);
    } catch (error) {
        console.error("Tenant config güncellenirken hata:", error);
        return res.status(500).json({
            success: false,
            message: "Bir hata oluştu! Lütfen tekrar deneyin.",
        });
    }
};

exports.getTenantConfig = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;

        if (!tenantId) {
            return res.status(400).json({
                success: false,
                message: "Tenant ID bulunamadı.",
            });
        }

        const tenantConfig = await getTenantConfigDB(tenantId);

        if (!tenantConfig) {
            return res.status(404).json({
                success: false,
                message: "Konfigürasyon bulunamadı.",
            });
        }

        return res.status(200).json({
            success: true,
            data: tenantConfig,
        });
    } catch (error) {
        console.error("Tenant config alırken hata:", error);
        return res.status(500).json({
            success: false,
            message: "Bir hata oluştu! Lütfen daha sonra tekrar deneyin.",
        });
    }
};
exports.getfeedBacks = async (req, res) => {
    try {
      const tenantId = req.user.tenant_id; // Kullanıcı bilgisi doğrulanmışsa

      if (!tenantId) {
        return res.status(400).json({
          success: false,
          message: "Tenant ID bulunamadı.",
        });
      }

      const feedbacks = await getFeedBacksDB(tenantId);

      return res.status(200).json({
        success: true,
        data: feedbacks,
      });
    } catch (error) {
      console.error("Geri bildirimleri alırken hata:", error);
      return res.status(500).json({
        success: false,
        message: "Bir hata oluştu! Lütfen daha sonra tekrar deneyin.",
      });
    }
  };

exports.uploadStoreImage = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;

        const file = req.files.store_image;

        const uniqueId = nanoid();

        const imagePath = path.join(__dirname, `../../public/${tenantId}/`) + uniqueId;

        if(!fs.existsSync(path.join(__dirname, `../../public/${tenantId}/`))) {
            fs.mkdirSync(path.join(__dirname, `../../public/${tenantId}/`));
        }

        const imageURL = `/public/${tenantId}/${uniqueId}`;

        await file.mv(imagePath);
        await uploadStoreImageDB(imageURL, uniqueId, tenantId);

        return res.status(200).json({
            success: true,
            message: "Store Image Uploaded.",
            imageURL: imageURL
        })
    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin."
        });
    }
};

exports.deleteStoreImage = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const uniqueId = req.body.uniqueId;

        if(!uniqueId){
            return res.status(200).json({
                success: false,
                message: "Invalid Request.",
            })
        }

        const imagePath = path.join(__dirname, `../../public/${tenantId}/`) + uniqueId;

        fs.unlinkSync(imagePath)

        await deleteStoreImageDB(null, uniqueId, tenantId);

        return res.status(200).json({
            success: true,
            message: "Store Image Removed.",
        })
    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin."
        });
    }
};

exports.uploadSlidesItemPhoto = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;

        if (!req.files || !req.files.image) {
            return res.status(400).json({
                success: false,
                message: "No image file uploaded!"
            });
        }

        const file = req.files.image;
        const fileName = `slider_${Date.now()}${path.extname(file.name)}`;
        const imagePath = path.join(__dirname, `../../public/${tenantId}/`) + fileName;

        const uploadDir = path.join(__dirname, `../../public/${tenantId}/`);
        if (!fs.existsSync(uploadDir)) {
            fs.mkdirSync(uploadDir, { recursive: true });
        }

        const imageURL = `/public/${tenantId}/${fileName}`;

        await file.mv(imagePath);
        await updateSlidersItemImageDB(imageURL, tenantId);

        return res.status(200).json({
            success: true,
            message: "Slider Image Uploaded.",
            imageURL: imageURL
        });
    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin."
        });
    }
};

exports.removeSlidesItemPhoto = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const { imageUrl } = req.body; // Silinecek resmin URL'ini alalım

        if (!imageUrl) {
            return res.status(400).json({
                success: false,
                message: "Image URL is required!"
            });
        }

        // Dosyayı fiziksel olarak silelim
        const imagePath = path.join(__dirname, `../../${imageUrl}`);
        if (fs.existsSync(imagePath)) {
            fs.unlinkSync(imagePath);
        }

        // Veritabanından kaldıralım
        await removeSlidersItemImageDB(imageUrl, tenantId);

        return res.status(200).json({
            success: true,
            message: "Slider Image Removed."
        });
    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin."
        });
    }
};

exports.setStoreDetails = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const storeName = req.body.storeName;
        const address = req.body.address;
        const phone = req.body.phone;
        const email = req.body.email;
        const currency = req.body.currency;
        const defaultOrderType = req.body.defaultOrderType;
        const dineInEnabled = req.body.dineInEnabled;
        const deliveryEnabled = req.body.deliveryEnabled;
        const takeawayEnabled = req.body.takeawayEnabled;
        const isQRMenuEnabled = req.body.isQRMenuEnabled;
        const isQROrderEnabled = req.body.isQROrderEnabled;
        const facebook = req.body.facebook;
        const instagram = req.body.instagram;
        const twitter = req.body.twitter;
        const whatsapp = req.body.whatsapp;
        const uniqueQRCode = nanoid();

        const qrCodeExists = await getQRMenuCodeDB(tenantId);
        if(qrCodeExists) {
            await setStoreSettingDB(storeName, address, phone, email, currency, defaultOrderType, dineInEnabled, deliveryEnabled, takeawayEnabled, isQRMenuEnabled,isQROrderEnabled , facebook, instagram, twitter, whatsapp,  uniqueQRCode, tenantId);
        } else {
            await updateQRMenuCodeDB(uniqueQRCode, tenantId);
            await setStoreSettingDB(storeName, address, phone, email, currency, defaultOrderType, dineInEnabled, deliveryEnabled, takeawayEnabled, isQRMenuEnabled, isQROrderEnabled, facebook, instagram, twitter, whatsapp, uniqueQRCode, tenantId);
        }

        return res.status(200).json({
            success: true,
            message: "Details Saved Successfully."
        });
    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin."
        });
    }
};

exports.getPrintSettings = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const result = await getPrintSettingDB(tenantId);

        const printSettings = {
            pageFormat: result?.page_format || null,
            header: result?.header || null,
            footer: result?.footer || null,
            showNotes: result?.show_notes || null,
            isEnablePrint: result?.is_enable_print || null,
            showStoreDetails: result?.show_store_details || null,
            showCustomerDetails: result?.show_customer_details || null,
            printToken: result?.print_token || null
        };

        return res.status(200).json(printSettings);
    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin."
        });
    }
};

exports.setPrintSettings = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;

        const pageFormat = req.body.pageFormat;
        const header = req.body.header;
        const footer = req.body.footer;
        const showNotes = req.body.showNotes;
        const isEnablePrint = req.body.isEnablePrint;
        const showStoreDetails = req.body.showStoreDetails;
        const showCustomerDetails = req.body.showCustomerDetails;
        const printToken = req.body.printToken;

        await setPrintSettingDB(pageFormat, header, footer, showNotes, isEnablePrint, showStoreDetails, showCustomerDetails, printToken, tenantId);

        return res.status(200).json({
            success: true,
            message: "Details Saved Successfully."
        });
    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin."
        });
    }
};

exports.getAllTaxes = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const result = await getTaxesDB(tenantId);
        return res.status(200).json(result);
    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin."
        });
    }
};

exports.getTax = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const taxId = req.params.id;
        const result = await getTaxDB(taxId, tenantId);
        return res.status(200).json(result);
    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin."
        });
    }
};

exports.addTax = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const title = req.body.title;
        const taxRate = req.body.rate;
        const type = req.body.type;

        if(!(title && taxRate && type)) {
            return res.status(400).json({
                success: false,
                message: "Please provide required details!"
            });
        }

        const taxId = await addTaxDB(title, taxRate, type, tenantId);
        return res.status(200).json({
            success: true,
            message: `Tax Details Added.`,
            taxId
        })
    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin."
        });
    }
};

exports.updateTax = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const taxId = req.params.id;
        const title = req.body.title;
        const taxRate = req.body.rate;
        const type = req.body.type;

        if(!(title && taxRate && type)) {
            return res.status(400).json({
                success: false,
                message: "Please provide required details!"
            });
        }

        await updateTaxDB(taxId, title, taxRate, type, tenantId);
        return res.status(200).json({
            success: true,
            message: `Tax Details Updated.`,
            taxId
        })
    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin."
        });
    }
};

exports.deletTax = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const taxId = req.params.id;

        await deleteTaxDB(taxId, tenantId);
        return res.status(200).json({
            success: true,
            message: `Tax Detail Removed.`,
            taxId
        })
    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin."
        });
    }
};


exports.addPaymentType = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const title = req.body.title;
        const isActive = req.body.isActive;
        const icon = req.body.icon;
        const hideOnPos = req.body.hideOnPos || 0;
        const isCash = req.body.isCash || 0;
        const isMali = req.body.isMali || 0;

        if(!(title)) {
            return res.status(400).json({
                success: false,
                message: "Please provide required details!"
            });
        }

        const id = await addPaymentTypeDB(title, isActive, tenantId, icon, hideOnPos, isCash, isMali);
        return res.status(200).json({
            success: true,
            message: `Payment Type Added.`,
            id
        })
    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin."
        });
    }
};

exports.getAllPaymentTypes = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const forPOS = req.query.forPOS === 'true';
        const result = await getPaymentTypesDB(false, tenantId, forPOS);
        return res.status(200).json(result);
    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin."
        });
    }
};

exports.updatePaymentType = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const id = req.params.id;
        const title = req.body.title;
        const isActive = req.body.isActive;
        const icon = req.body.icon;
        const hideOnPos = req.body.hideOnPos || 0;
        const isCash = req.body.isCash || 0;
        const isMali = req.body.isMali || 0;

        if(!(title)) {
            return res.status(400).json({
                success: false,
                message: "Please provide required details!"
            });
        }

        await updatePaymentTypeDB(id, title, isActive, tenantId, icon, hideOnPos, isCash, isMali);
        return res.status(200).json({
            success: true,
            message: `Payment Type Updated.`,
        })
    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin."
        });
    }
};

exports.togglePaymentType = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const id = req.params.id;
        const isActive = req.body.isActive;

        await togglePaymentTypeDB(id, isActive, tenantId);
        return res.status(200).json({
            success: true,
            message: `Payment Type Status Updated.`,
        })
    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin."
        });
    }
};

exports.deletePaymentType = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const id = req.params.id;

        await deletePaymentTypeDB(id, tenantId);
        return res.status(200).json({
            success: true,
            message: `Payment Type Deleted.`,
            id
        })
    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin."
        });
    }
};

exports.addStoreTable = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const title = req.body.title;
        const floor = req.body.floor;
        const seatingCapacity = req.body.seatingCapacity;

        if(!(title && floor && seatingCapacity)) {
            return res.status(400).json({
                success: false,
                message: "Please provide required details!"
            });
        }

        if(seatingCapacity < 0) {
            return res.status(400).json({
                success: false,
                message: "Please provide valid seating capacity count!"
            });
        }

        const id = await addStoreTableDB(title, floor, seatingCapacity, tenantId);
        return res.status(200).json({
            success: true,
            message: `Store Table Added.`,
            id
        })
    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin."
        });
    }
};

/**
 * Toplu masa ekler
 */
exports.bulkAddStoreTables = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const { baseTitle, count, floor, seatingCapacity } = req.body;

        if (!(baseTitle && count && floor && seatingCapacity)) {
            return res.status(400).json({
                success: false,
                message: "Lütfen gerekli bilgileri sağlayın!"
            });
        }

        if (count <= 0) {
            return res.status(400).json({
                success: false,
                message: "Masa sayısı en az 1 olmalıdır!"
            });
        }

        if (seatingCapacity < 0) {
            return res.status(400).json({
                success: false,
                message: "Oturma kapasitesi geçerli bir sayı olmalıdır!"
            });
        }

        const tableIds = await bulkAddStoreTablesDB(baseTitle, count, floor, seatingCapacity, tenantId);

        return res.status(200).json({
            success: true,
            message: `${count} adet masa başarıyla eklendi.`,
            tableIds
        });
    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Bir hata oluştu! Lütfen daha sonra tekrar deneyin!"
        });
    }
};

exports.getAllStoreTables = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const result = await getStoreTablesDB(tenantId);
        return res.status(200).json(result);
    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin."
        });
    }
};

exports.updateStoreTable = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const id = req.params.id;
        const title = req.body.title;
        const floor = req.body.floor;
        const seatingCapacity = req.body.seatingCapacity;

        if(!(title && floor && seatingCapacity)) {
            return res.status(400).json({
                success: false,
                message: "Please provide required details!"
            });
        }

        await updateStoreTableDB(id, title, floor, seatingCapacity, tenantId);
        return res.status(200).json({
            success: true,
            message: `Store Table Details Updated.`,
        });
    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin."
        });
    }
};

exports.deleteStoreTable = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const id = req.params.id;

        await deleteStoreTableDB(id, tenantId);
        return res.status(200).json({
            success: true,
            message: `Store Table Details Deleted.`,
        });
    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin."
        });
    }
};


exports.addCategory = async (req, res) => {
    try {
      const tenantId = req.user.tenant_id;
      // İstekten title ve parent_id alınır (parent_id opsiyoneldir)
      const { title, parent_id } = req.body;

      if (!title) {
        return res.status(400).json({
          success: false,
          message: "Lütfen gerekli bilgileri sağlayın!"
        });
      }

      // parent_id gönderilmemişse NULL olarak kaydedilecek
      const id = await addCategoryDB(title, tenantId, parent_id);
      return res.status(200).json({
        success: true,
        message: `Kategori başarıyla eklendi.`,
        id
      });
    } catch (error) {
      console.error(error);
      return res.status(500).json({
        success: false,
        message: "Bir hata oluştu, lütfen daha sonra tekrar deneyin!"
      });
    }
  };


exports.getCategories = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const result = await getCategoriesDB(tenantId);
        return res.status(200).json(result);
    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin."
        });
    }
};

exports.updateCategory = async (req, res) => {
    try {
      const tenantId = req.user.tenant_id;
      const id = req.params.id;
      // title, printerId ve parent_id bilgileri istekte alınır.
      const { title, printerId, parent_id } = req.body;

      if (!title) {
        return res.status(400).json({
          success: false,
          message: "Lütfen gerekli bilgileri sağlayın!"
        });
      }

      const printerValue = (printerId && printerId !== "") ? printerId : null;

      console.log("Güncellenen Kategori:", { id, title, printerValue, tenantId, parent_id });

      await updateCategoryDB(id, title, printerValue, tenantId, parent_id);

      return res.status(200).json({
        success: true,
        message: `Kategori başarıyla güncellendi.`
      });
    } catch (error) {
      console.error("Kategori güncellenirken hata oluştu:", error);
      return res.status(500).json({
        success: false,
        message: "Kategori güncellenirken hata oluştu, lütfen tekrar deneyin!",
        error: error.message
      });
    }
  };




exports.uploadCategoryPhoto = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const id = req.params.id;

        const file = req.files.image;

        const imagePath = path.join(__dirname, `../../public/${tenantId}/`) + id;

        if(!fs.existsSync(path.join(__dirname, `../../public/${tenantId}/`))) {
            fs.mkdirSync(path.join(__dirname, `../../public/${tenantId}/`));
        }

        const imageURL = `/public/${tenantId}/${id}`;

        await file.mv(imagePath);
        await updateCategoryImageDB(id, imageURL, tenantId);

        return res.status(200).json({
            success: true,
            message: "Kategori Resmi Başarıyla Yüklendi.",
            imageURL: imageURL
        })
    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin."
        });
    }
};

exports.deleteCategory = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const id = req.params.id;

        await deleteCategoryDB(id, tenantId);
        return res.status(200).json({
            success: true,
            message: `Category Deleted.`,
        });
    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin."
        });
    }
};

// Mali Mode Controller Fonksiyonları
exports.enableMaliMode = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;

        await enableMaliModeDB(tenantId);

        return res.status(200).json({
            success: true,
            message: "Başarılı"
        });
    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Tekrar dene!"
        });
    }
};

exports.disableMaliMode = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;

        await disableMaliModeDB(tenantId);

        return res.status(200).json({
            success: true,
            message: "Başarılı."
        });
    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Tekrar dene!"
        });
    }
};

exports.getMaliModeStatus = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;

        const isActive = await getMaliModeStatusDB(tenantId);

        return res.status(200).json({
            success: true,
            mali_mode_active: Boolean(isActive)
        });
    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Birşeyler ters gitti!"
        });
    }
};

// ==================== YAZARKASA ENTEGRASYON ====================

// IP adresi validasyon fonksiyonu
const isValidIP = (ip) => {
    const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    return ipRegex.test(ip);
};

/**
 * Yazarkasa entegrasyon ayarlarını getir
 */
exports.getYazarkasaSettings = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const settings = await getYazarkasaSettingsDB(tenantId);

        return res.status(200).json({
            success: true,
            settings: {
                is_active: Boolean(settings.is_active),
                created_at: settings.created_at,
                updated_at: settings.updated_at
            }
        });
    } catch (error) {
        console.error('Yazarkasa settings error:', error);
        return res.status(500).json({
            success: false,
            message: "Yazarkasa ayarları alınırken bir hata oluştu."
        });
    }
};

/**
 * Yazarkasa entegrasyonu aktif/pasif et
 */
exports.updateYazarkasaSettings = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const { isActive } = req.body;

        if (typeof isActive !== 'boolean') {
            return res.status(400).json({
                success: false,
                message: "isActive alanı boolean olmalıdır."
            });
        }

        await updateYazarkasaSettingsDB(tenantId, isActive);

        return res.status(200).json({
            success: true,
            message: `Yazarkasa entegrasyonu ${isActive ? 'aktif' : 'pasif'} edildi.`
        });
    } catch (error) {
        console.error('Yazarkasa settings update error:', error);
        return res.status(500).json({
            success: false,
            message: "Yazarkasa ayarları güncellenirken bir hata oluştu."
        });
    }
};

// ==================== YAZARKASA CİHAZ YÖNETİMİ ====================

/**
 * Yazarkasa cihazlarını listele
 */
exports.getYazarkasaDevices = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const devices = await getYazarkasaDevicesDB(tenantId);

        return res.status(200).json({
            success: true,
            devices: devices.map(device => ({
                ...device,
                is_active: Boolean(device.is_active),
                cash_register_id: device.cash_register_id,
                cash_register_name: device.cash_register_name,
                printer_id: device.printer_id,
                printer_name: device.printer_name
            }))
        });
    } catch (error) {
        console.error('Yazarkasa devices error:', error);
        return res.status(500).json({
            success: false,
            message: "Cihazlar listelenirken bir hata oluştu."
        });
    }
};

/**
 * Yazarkasa cihazı ekle
 */
exports.addYazarkasaDevice = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const { deviceName, deviceSerial, applicationId, ipAddress, port, cashRegisterId, printerId } = req.body;

        // Validasyon
        if (!deviceName || !deviceSerial || !applicationId || !ipAddress || !port) {
            return res.status(400).json({
                success: false,
                message: "Tüm alanlar zorunludur: deviceName, deviceSerial, applicationId, ipAddress, port"
            });
        }

        // IP adresi validasyonu
        if (!isValidIP(ipAddress)) {
            return res.status(400).json({
                success: false,
                message: "Geçersiz IP adresi formatı"
            });
        }

        const deviceId = await addYazarkasaDeviceDB(tenantId, {
            deviceName,
            deviceSerial,
            applicationId,
            ipAddress,
            port,
            cashRegisterId: cashRegisterId || null,
            printerId: printerId || null
        });

        return res.status(201).json({
            success: true,
            message: "Cihaz başarıyla eklendi.",
            deviceId
        });
    } catch (error) {
        console.error('Yazarkasa device add error:', error);
        return res.status(500).json({
            success: false,
            message: error.message || "Cihaz eklenirken bir hata oluştu."
        });
    }
};

/**
 * Yazarkasa cihazı güncelle
 */
exports.updateYazarkasaDevice = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const deviceId = req.params.id;
        const { deviceName, deviceSerial, applicationId, ipAddress, port, isActive, cashRegisterId, printerId } = req.body;

        // Validasyon
        if (!deviceName || !deviceSerial || !applicationId || !ipAddress || !port) {
            return res.status(400).json({
                success: false,
                message: "Tüm alanlar zorunludur: deviceName, deviceSerial, applicationId, ipAddress, port"
            });
        }

        // IP adresi validasyonu
        if (!isValidIP(ipAddress)) {
            return res.status(400).json({
                success: false,
                message: "Geçersiz IP adresi formatı"
            });
        }

        const updated = await updateYazarkasaDeviceDB(tenantId, deviceId, {
            deviceName,
            deviceSerial,
            applicationId,
            ipAddress,
            port,
            isActive: isActive !== undefined ? isActive : true,
            cashRegisterId: cashRegisterId || null,
            printerId: printerId || null
        });

        if (!updated) {
            return res.status(404).json({
                success: false,
                message: "Cihaz bulunamadı."
            });
        }

        return res.status(200).json({
            success: true,
            message: "Cihaz başarıyla güncellendi."
        });
    } catch (error) {
        console.error('Yazarkasa device update error:', error);
        return res.status(500).json({
            success: false,
            message: error.message || "Cihaz güncellenirken bir hata oluştu."
        });
    }
};

/**
 * Yazarkasa cihazı sil
 */
exports.deleteYazarkasaDevice = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const deviceId = req.params.id;

        const deleted = await deleteYazarkasaDeviceDB(tenantId, deviceId);

        if (!deleted) {
            return res.status(404).json({
                success: false,
                message: "Cihaz bulunamadı."
            });
        }

        return res.status(200).json({
            success: true,
            message: "Cihaz başarıyla silindi."
        });
    } catch (error) {
        console.error('Yazarkasa device delete error:', error);
        return res.status(500).json({
            success: false,
            message: "Cihaz silinirken bir hata oluştu."
        });
    }
};

/**
 * Yazarkasa cihazı detayı getir
 */
exports.getYazarkasaDevice = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const deviceId = req.params.id;

        const device = await getYazarkasaDeviceByIdDB(tenantId, deviceId);

        if (!device) {
            return res.status(404).json({
                success: false,
                message: "Cihaz bulunamadı."
            });
        }

        return res.status(200).json({
            success: true,
            device: {
                ...device,
                is_active: Boolean(device.is_active)
            }
        });
    } catch (error) {
        console.error('Yazarkasa device detail error:', error);
        return res.status(500).json({
            success: false,
            message: "Cihaz detayı alınırken bir hata oluştu."
        });
    }
};
