const {
    getAllQRTemplatesDB,
    getQRTemplateByIdDB,
    getTenantQRTemplateDB,
    setTenantQRTemplateDB,
    createQRTemplateDB,
    updateQRTemplateDB,
    deleteQRTemplateDB
} = require("../services/qr_template.service");

/**
 * Tüm aktif QR menü şablonlarını listele
 */
exports.getAllQRTemplates = async (req, res) => {
    try {
        const templates = await getAllQRTemplatesDB();
        
        return res.status(200).json({
            success: true,
            templates
        });
    } catch (error) {
        console.error("QR templates listesi hatası:", error);
        return res.status(500).json({
            success: false,
            message: "Şablonlar yüklenirken bir hata oluştu."
        });
    }
};

/**
 * Belirli bir şablonun detaylarını getir
 */
exports.getQRTemplateById = async (req, res) => {
    try {
        const templateId = req.params.id;
        const template = await getQRTemplateByIdDB(templateId);
        
        if (!template) {
            return res.status(404).json({
                success: false,
                message: "Şablon bulunamadı."
            });
        }
        
        return res.status(200).json({
            success: true,
            template
        });
    } catch (error) {
        console.error("QR template detay hatası:", error);
        return res.status(500).json({
            success: false,
            message: "Şablon yüklenirken bir hata oluştu."
        });
    }
};

/**
 * Tenant'ın seçili şablonunu getir
 */
exports.getTenantQRTemplate = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const template = await getTenantQRTemplateDB(tenantId);
        
        return res.status(200).json({
            success: true,
            template
        });
    } catch (error) {
        console.error("Tenant QR template hatası:", error);
        return res.status(500).json({
            success: false,
            message: "Şablon yüklenirken bir hata oluştu."
        });
    }
};

/**
 * Tenant için şablon seç
 */
exports.setTenantQRTemplate = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const { templateId } = req.body;
        
        if (!templateId) {
            return res.status(400).json({
                success: false,
                message: "Şablon ID gereklidir."
            });
        }
        
        await setTenantQRTemplateDB(tenantId, templateId);
        
        return res.status(200).json({
            success: true,
            message: "Şablon başarıyla seçildi."
        });
    } catch (error) {
        console.error("Tenant QR template seçim hatası:", error);
        return res.status(500).json({
            success: false,
            message: error.message || "Şablon seçilirken bir hata oluştu."
        });
    }
};

/**
 * Yeni şablon oluştur (admin için)
 */
exports.createQRTemplate = async (req, res) => {
    try {
        const { name, description, templateConfig, previewImage } = req.body;
        
        if (!name || !templateConfig) {
            return res.status(400).json({
                success: false,
                message: "Şablon adı ve yapılandırması gereklidir."
            });
        }
        
        const templateId = await createQRTemplateDB(name, description, templateConfig, previewImage);
        
        return res.status(201).json({
            success: true,
            message: "Şablon başarıyla oluşturuldu.",
            templateId
        });
    } catch (error) {
        console.error("QR template oluşturma hatası:", error);
        return res.status(500).json({
            success: false,
            message: "Şablon oluşturulurken bir hata oluştu."
        });
    }
};

/**
 * Şablon güncelle (admin için)
 */
exports.updateQRTemplate = async (req, res) => {
    try {
        const templateId = req.params.id;
        const { name, description, templateConfig, previewImage } = req.body;
        
        if (!name || !templateConfig) {
            return res.status(400).json({
                success: false,
                message: "Şablon adı ve yapılandırması gereklidir."
            });
        }
        
        await updateQRTemplateDB(templateId, name, description, templateConfig, previewImage);
        
        return res.status(200).json({
            success: true,
            message: "Şablon başarıyla güncellendi."
        });
    } catch (error) {
        console.error("QR template güncelleme hatası:", error);
        return res.status(500).json({
            success: false,
            message: "Şablon güncellenirken bir hata oluştu."
        });
    }
};

/**
 * Şablon sil (admin için)
 */
exports.deleteQRTemplate = async (req, res) => {
    try {
        const templateId = req.params.id;
        
        await deleteQRTemplateDB(templateId);
        
        return res.status(200).json({
            success: true,
            message: "Şablon başarıyla silindi."
        });
    } catch (error) {
        console.error("QR template silme hatası:", error);
        return res.status(500).json({
            success: false,
            message: error.message || "Şablon silinirken bir hata oluştu."
        });
    }
};
