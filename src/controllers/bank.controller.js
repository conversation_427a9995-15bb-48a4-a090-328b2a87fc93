const {
  getAllBanksDB,
  getBankByIdDB,
  addBankDB,
  updateBankDB,
  deleteBankDB
} = require('../services/bank.service');

/**
 * Tüm bankaları getirir
 */
exports.getAllBanks = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const { active_only } = req.query;
    
    const activeOnly = active_only === 'true';
    const banks = await getAllBanksDB(tenantId, activeOnly);

    return res.status(200).json({
      success: true,
      data: banks
    });
  } catch (error) {
    console.error("getAllBanks error:", error);
    return res.status(500).json({
      success: false,
      message: "Bankalar getirilirken bir hata oluştu",
      error: error.message
    });
  }
};

/**
 * ID'ye göre banka getirir
 */
exports.getBankById = async (req, res) => {
  try {
    const { id } = req.params;
    const tenantId = req.user.tenant_id;

    const bank = await getBankByIdDB(id, tenantId);

    if (!bank) {
      return res.status(404).json({
        success: false,
        message: "Banka bulunamadı"
      });
    }

    return res.status(200).json({
      success: true,
      data: bank
    });
  } catch (error) {
    console.error("getBankById error:", error);
    return res.status(500).json({
      success: false,
      message: "Banka getirilirken bir hata oluştu",
      error: error.message
    });
  }
};

/**
 * Yeni banka ekler
 */
exports.addBank = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const { name, description } = req.body;

    // Validasyon
    if (!name) {
      return res.status(400).json({
        success: false,
        message: "Banka adı zorunludur"
      });
    }

    const bankId = await addBankDB({ name, description }, tenantId);

    return res.status(201).json({
      success: true,
      message: "Banka başarıyla eklendi",
      data: { id: bankId }
    });
  } catch (error) {
    console.error("addBank error:", error);
    return res.status(500).json({
      success: false,
      message: "Banka eklenirken bir hata oluştu",
      error: error.message
    });
  }
};

/**
 * Banka bilgilerini günceller
 */
exports.updateBank = async (req, res) => {
  try {
    const { id } = req.params;
    const tenantId = req.user.tenant_id;
    const { name, description, is_active } = req.body;

    // Validasyon
    if (!name) {
      return res.status(400).json({
        success: false,
        message: "Banka adı zorunludur"
      });
    }

    const success = await updateBankDB(id, { name, description, is_active }, tenantId);

    if (!success) {
      return res.status(404).json({
        success: false,
        message: "Banka bulunamadı veya güncellenemedi"
      });
    }

    return res.status(200).json({
      success: true,
      message: "Banka başarıyla güncellendi"
    });
  } catch (error) {
    console.error("updateBank error:", error);
    return res.status(500).json({
      success: false,
      message: "Banka güncellenirken bir hata oluştu",
      error: error.message
    });
  }
};

/**
 * Banka siler
 */
exports.deleteBank = async (req, res) => {
  try {
    const { id } = req.params;
    const tenantId = req.user.tenant_id;

    const success = await deleteBankDB(id, tenantId);

    if (!success) {
      return res.status(404).json({
        success: false,
        message: "Banka bulunamadı veya silinemedi"
      });
    }

    return res.status(200).json({
      success: true,
      message: "Banka başarıyla silindi"
    });
  } catch (error) {
    console.error("deleteBank error:", error);
    
    // Kullanım hatası kontrolü
    if (error.message.includes("kullanıldığı için silinemez")) {
      return res.status(400).json({
        success: false,
        message: error.message
      });
    }

    return res.status(500).json({
      success: false,
      message: "Banka silinirken bir hata oluştu",
      error: error.message
    });
  }
};
