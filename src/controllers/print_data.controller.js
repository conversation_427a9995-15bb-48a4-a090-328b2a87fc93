const { savePrintDataKitchenDB, savePrintDataRecipetnDB, getPrintDataService, updatePrintStatusService, savePrintDataReportDB } = require("../services/print_data.service");

exports.savePrintDataKitchen = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const orderData = req.body;

    await savePrintDataKitchenDB(tenantId, orderData);

    return res.status(200).json({
      success: true,
      message: "Yazdırma verileri başarıyla kaydedildi"
    });
  } catch (error) {
    console.error("Yazdırma verisi kaydedilirken hata:", error);
    return res.status(500).json({
      success: false,
      message: "Yazdırma verisi kaydedilemedi",
      error: error.message
    });
  }
};

exports.savePrintDataRecipet = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const orderData = req.body;

    await savePrintDataRecipetnDB(tenantId, orderData);


    return res.status(200).json({
      success: true,
      message: "Yazdırma verileri başarıyla kaydedildi"
    });
  } catch (error) {
    console.error("Yazdırma verisi kaydedilirken hata:", error);
    return res.status(500).json({
      success: false,
      message: "Yazdırma verisi kaydedilemedi",
      error: error.message
    });
  }
};

exports.getPrintData = async (req, res) => {
  try {
    const tenantId = req.query.tenant_id;

    if (!tenantId) {
      return res.status(400).json({
        success: false,
        message: "tenant_id is required",
      });
    }

    const printData = await getPrintDataService(tenantId);

    return res.status(200).json({
      success: true,
      printData
    });
  } catch (error) {
    console.error("Hata oluştu:", error);
    return res.status(500).json({
      success: false,
      message: "Error fetching print data",
      error: error.message
    });
  }
};

exports.updatePrintStatus = async (req, res) => {
  try {
    const { printId } = req.params;
    const { status } = req.body;

    await updatePrintStatusService(printId, status);

    return res.status(200).json({
      success: true,
      message: "Print status updated successfully"
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: "Error updating print status",
      error: error.message
    });
  }
};

exports.savePrintDataReport = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const reportData = req.body;

    await savePrintDataReportDB(tenantId, reportData);

    return res.status(200).json({
      success: true,
      message: "Rapor yazdırma verileri başarıyla kaydedildi"
    });
  } catch (error) {
    console.error("Rapor yazdırma verisi kaydedilirken hata:", error);
    return res.status(500).json({
      success: false,
      message: "Rapor yazdırma verisi kaydedilemedi",
      error: error.message
    });
  }
};
