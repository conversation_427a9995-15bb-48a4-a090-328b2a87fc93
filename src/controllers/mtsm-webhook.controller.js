const {
  getOrdersForWebhookDB,
  getOrderDetailsForWebhookDB,
  updateOrderStatusForWebhookDB,
  updateOrderPaymentForWebhookDB
} = require("../services/mtsm-webhook.service");

/**
 * GET /webhook/orders
 * Siparişleri Listeleme Webhook
 * Query params: csn, pageNumber, pageSize, no, etc.
 */
exports.listOrders = async (req, res) => {
  try {
    const {
      csn,           // Cihaz sicil numarası (bilgi amaçlı)
      pageNumber = 1,
      pageSize = 20,
      no,            // Sipariş numarası (opsiyonel)
      status         // Sipariş durumu (opsiyonel)
    } = req.query;

    // Tenant bilgisi auth middleware'den geliyor
    const tenantId = req.tenantId;
    const tenantName = req.tenant.name;

    console.log(`📋 mTSM Webhook - Sipariş listesi istendi - Tenant: ${tenantName}, CSN: ${csn}, Page: ${pageNumber}`);

    const result = await getOrdersForWebhookDB(tenantId, {
      pageNumber: parseInt(pageNumber),
      pageSize: parseInt(pageSize),
      orderNo: no,
      status
    });

    console.log(`✅ mTSM Webhook - ${result.items.length} sipariş döndürüldü - Tenant: ${tenantName}`);

    // TSM formatında response döndür (wrapper olmadan direkt data)
    res.status(200).json(result);

  } catch (error) {
    console.error('mTSM Webhook listOrders error:', error);
    res.status(500).json({
      success: false,
      message: 'Sipariş listesi alınamadı'
    });
  }
};

/**
 * GET /webhook/orders/details
 * Sipariş Detaylarını Alma Webhook
 * Query params: id
 */
exports.getOrderDetails = async (req, res) => {
  try {
    const { id } = req.query;
    const tenantId = req.tenantId;
    const tenantName = req.tenant.name;

    console.log(`📄 mTSM Webhook - Sipariş detayı istendi - Tenant: ${tenantName}, ID: ${id}`);

    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'Sipariş ID gerekli'
      });
    }

    const orderDetails = await getOrderDetailsForWebhookDB(id, tenantId);

    if (!orderDetails) {
      return res.status(404).json({
        success: false,
        message: 'Sipariş bulunamadı'
      });
    }

    console.log(`✅ mTSM Webhook - Sipariş detayı döndürüldü - Tenant: ${tenantName}, ID: ${id}`);

    // TSM formatında response döndür (wrapper olmadan direkt data)
    res.status(200).json(orderDetails);

  } catch (error) {
    console.error('mTSM Webhook getOrderDetails error:', error);
    res.status(500).json({
      success: false,
      message: 'Sipariş detayı alınamadı'
    });
  }
};

/**
 * POST /webhook/orders/status
 * Sipariş Durum Güncelleme Webhook
 * Body: { id, status, payments?, receipt?, deviceDocument? }
 */
exports.updateOrderStatus = async (req, res) => {
  try {
    // Body'dan al (TSM POST ile gönderiyor)
    const { id, status, payments, receipt, deviceDocument } = req.body;

    console.log(`🔄 mTSM Webhook - Sipariş durum güncelleme - Order ID: ${id}, Status: ${status}`);

    if (!id || !status) {
      return res.status(400).json({
        success: false,
        message: 'Sipariş ID ve status gerekli'
      });
    }

    // Direkt 200 döndür, işlem yapma (ödeme webhook'u asıl işi yapacak)
    console.log(`✅ mTSM Webhook - Sipariş durum güncelleme onaylandı - Order ID: ${id}, Status: ${status}`);

    res.status(200).json({
      success: true,
      message: 'Sipariş durumu alındı'
    });

  } catch (error) {
    console.error('mTSM Webhook updateOrderStatus error:', error);
    res.status(500).json({
      success: false,
      message: 'Sipariş durumu güncellenemedi'
    });
  }
};

/**
 * POST /webhook/orders/payment
 * Sipariş Ödeme Webhook - TSM'den ödeme tamamlandığında çağrılır
 * Body: { id, payments, receipt, csn }
 * Auth middleware devre dışı - TSM direkt çağırıyor
 */
exports.updateOrderPayment = async (req, res) => {
  try {
    const { id, payments, receipt, csn } = req.body;

    console.log(`💳 mTSM Webhook - Sipariş ödeme tamamlandı - CSN: ${csn}, Order ID: ${id}`);
    console.log(`📄 Receipt bilgileri:`, receipt);
    console.log(`💰 Payment bilgileri:`, payments);

    if (!id || !payments) {
      return res.status(400).json({
        success: false,
        message: 'Sipariş ID ve payments gerekli'
      });
    }

    // tenant_id'yi orders tablosundan otomatik çekecek
    const result = await updateOrderPaymentForWebhookDB(id, null, payments, receipt, csn);

    console.log(`✅ mTSM Webhook - Sipariş ödemesi tamamlandı - Order ID: ${id}`);

    // TSM için basit response döndür
    res.status(200).json({
      success: true,
      message: 'Ödeme başarıyla işlendi'
    });

  } catch (error) {
    console.error('mTSM Webhook updateOrderPayment error:', error);
    res.status(500).json({
      success: false,
      message: 'Sipariş ödemesi güncellenemedi'
    });
  }
};

// CSN helper fonksiyonu artık gerekli değil
// Tenant bilgisi auth middleware'den geliyor
