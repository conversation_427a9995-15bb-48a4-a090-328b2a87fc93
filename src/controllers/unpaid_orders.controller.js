const { getUnpaidOrdersDB, getOrderPaymentDetailsDB } = require("../services/unpaid_orders.service");

exports.getUnpaidOrders = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;

        const from = req.query.from || null;
        const to = req.query.to || null;
        const type = req.query.type || 'today'; // Varsayılan olarak bugün

        if(type === 'custom') {
            if(!(from && to)) {
                return res.status(400).json({
                    success: false,
                    message: "Custom tarih seçimi için from ve to parametreleri gerekli!"
                });
            }
        }

        const unpaidOrders = await getUnpaidOrdersDB(type, from, to, tenantId);

        // Özet bilgileri hesapla
        const summary = {
            total_unpaid_orders: unpaidOrders.length,
            total_debt_amount: unpaidOrders.reduce((sum, order) => sum + parseFloat(order.remaining_debt), 0),
            total_order_value: unpaidOrders.reduce((sum, order) => sum + parseFloat(order.order_total), 0),
            total_paid_amount: unpaidOrders.reduce((sum, order) => sum + parseFloat(order.total_paid), 0)
        };

        // Sipariş türlerine göre gruplandır
        const groupedByType = unpaidOrders.reduce((acc, order) => {
            const type = order.delivery_type || 'unknown';
            if (!acc[type]) {
                acc[type] = {
                    orders: [],
                    count: 0,
                    total_debt: 0
                };
            }
            acc[type].orders.push(order);
            acc[type].count++;
            acc[type].total_debt += parseFloat(order.remaining_debt);
            return acc;
        }, {});

        return res.status(200).json({
            success: true,
            summary,
            grouped_by_delivery_type: groupedByType,
            all_orders: unpaidOrders
        });

    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Ödenmemiş siparişler getirilirken bir hata oluştu!"
        });
    }
};

// Belirli bir siparişin ödeme detaylarını getir
exports.getOrderPaymentDetails = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const orderId = req.params.orderId;

        if (!orderId) {
            return res.status(400).json({
                success: false,
                message: "Sipariş ID gerekli!"
            });
        }

        const orderDetails = await getOrderPaymentDetailsDB(orderId, tenantId);

        if (!orderDetails) {
            return res.status(404).json({
                success: false,
                message: "Sipariş bulunamadı!"
            });
        }

        return res.status(200).json({
            success: true,
            ...orderDetails
        });

    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Sipariş ödeme detayları getirilirken bir hata oluştu!"
        });
    }
};
