const { addCampaignDB, deleteCampaignDB, listCampaignsDB, updateCampaignDB, updateCampaignImageDB } = require("../services/campaings.service");
const path = require("path");
const fs = require("fs");

// Kampanya ekleme
exports.addCampaign = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const { campaign_name, description, start_date, end_date } = req.body;

    if (!campaign_name || !start_date || !end_date) {
      return res.status(400).json({ message: "<PERSON>erek<PERSON> alan<PERSON> eksik!" });
    }

    const campaignId = await addCampaignDB(campaign_name, description, start_date, end_date, tenantId, null);

    return res.status(200).json({
      success: true,
      message: "Kampanya başarıyla eklendi!",
      campaignId,
    });
  } catch (error) {
    console.error("Kampanya eklenirken hata:", error);
    return res.status(500).json({ message: "Bir hata oluştu." });
  }
};

// Kampanya silme
exports.deleteCampaign = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const { campaignId } = req.params;

    await deleteCampaignDB(campaignId, tenantId);

    return res.status(200).json({
      success: true,
      message: "Kampanya başarıyla silindi!",
    });
  } catch (error) {
    console.error("Kampanya silinirken hata:", error);
    return res.status(500).json({ message: "Bir hata oluştu." });
  }
};

// Kampanya listeleme
exports.listCampaigns = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const campaigns = await listCampaignsDB(tenantId);

    return res.status(200).json({
      success: true,
      campaigns,
    });
  } catch (error) {
    console.error("Kampanyalar listelenirken hata:", error);
    return res.status(500).json({ message: "Bir hata oluştu." });
  }
};

// Kampanya güncelleme
exports.updateCampaign = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const { campaignId } = req.params;
    const { campaign_name, description, start_date, end_date } = req.body;

    if (!campaign_name || !start_date || !end_date) {
      return res.status(400).json({ message: "Gerekli alanlar eksik!" });
    }

    await updateCampaignDB(campaignId, campaign_name, description, start_date, end_date, tenantId, null);

    return res.status(200).json({
      success: true,
      message: "Kampanya başarıyla güncellendi!",
    });
  } catch (error) {
    console.error("Kampanya güncellenirken hata:", error);
    return res.status(500).json({ message: "Bir hata oluştu." });
  }
};

// Kampanya resmi yükleme
exports.uploadCampaignPhoto = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const { campaignId } = req.params;

    const file = req.files.image;
    const imagePath = path.join(__dirname, `../../public/${tenantId}/`) + campaignId;
    const imageURL = `/public/${tenantId}/${campaignId}`;

    if (!fs.existsSync(path.join(__dirname, `../../public/${tenantId}/`))) {
      fs.mkdirSync(path.join(__dirname, `../../public/${tenantId}/`), { recursive: true });
    }

    await file.mv(imagePath);
    await updateCampaignImageDB(campaignId, imageURL, tenantId);

    return res.status(200).json({
      success: true,
      message: "Kampanya resmi yüklendi.",
      imageURL,
    });
  } catch (error) {
    console.error("Kampanya resmi yüklenirken hata:", error);
    return res.status(500).json({ message: "Bir hata oluştu!" });
  }
};

// Kampanya resmi kaldırma
exports.removeCampaignPhoto = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const { campaignId } = req.params;
    const imagePath = path.join(__dirname, `../../public/${tenantId}/`) + campaignId;

    if (fs.existsSync(imagePath)) {
      fs.unlinkSync(imagePath);
    }

    await updateCampaignImageDB(campaignId, null, tenantId);

    return res.status(200).json({
      success: true,
      message: "Kampanya resmi kaldırıldı.",
    });
  } catch (error) {
    console.error("Kampanya resmi kaldırılırken hata:", error);
    return res.status(500).json({ message: "Bir hata oluştu!" });
  }
};