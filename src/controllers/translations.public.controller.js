const translationService = require('../services/translations.public.service');

exports.getPublicTranslations = async (req, res) => {
  try {
    const { object_type, language_code, tenant_id } = req.query;
    if (!(object_type && language_code)) { // tenant_id artık zorunlu değil
      return res.status(400).json({
        success: false,
        message: "Gerekli parametreler eksik (object_type ve language_code zorunlu).",
      });
    }
    const translations = await translationService.getPublicTranslationsDB(
      tenant_id || null, // tenant_id yoksa null geç
      object_type,
      language_code
    );
    return res.status(200).json({
      success: true,
      translations,
    });
  } catch (error) {
    console.error("getPublicTranslations hata:", error);
    return res.status(500).json({
      success: false,
      message: "<PERSON>ir hata olu<PERSON>, lütfen daha sonra tekrar deneyin.",
    });
  }
};