const {
    addSupplierDB,
    updateSupplierDB,
    deleteSupplierDB,
    getSupplierDB,
    getAllSuppliersDB
  } = require("../services/supplier.service");
  
  // Tedarikçi ekleme
  exports.addSupplier = async (req, res) => {
    try {
      const tenantId = req.user.tenant_id;
      const { name, address, phone, email, contactPerson } = req.body;
  
      if (!name) {
        return res.status(400).json({
          success: false,
          message: "Lütfen gerekli alanları doldurun: isim"
        });
      }
  
      const supplierId = await addSupplierDB(
        name,
        address,
        phone,
        email,
        contactPerson,
        tenantId
      );
  
      return res.status(200).json({
        success: true,
        message: "Tedarikçi başarıyla eklendi.",
        supplierId
      });
    } catch (error) {
      console.error("Error in addSupplier:", error);
      return res.status(500).json({
        success: false,
        message: "<PERSON>ir hata oluştu! Lütfen daha sonra tekrar deneyin!"
      });
    }
  };
  
  // Tedarikçi güncelleme
  exports.updateSupplier = async (req, res) => {
    try {
      const supplierId = req.params.id;
      const { name, address, phone, email, contactPerson, isActive } = req.body;
  
      if (!name) {
        return res.status(400).json({
          success: false,
          message: "Lütfen gerekli alanları doldurun: isim"
        });
      }
  
      const affectedRows = await updateSupplierDB(
        supplierId,
        name,
        address,
        phone,
        email,
        contactPerson,
        isActive
      );
  
      if (affectedRows === 0) {
        return res.status(404).json({
          success: false,
          message: "Tedarikçi bulunamadı!"
        });
      }
  
      return res.status(200).json({
        success: true,
        message: "Tedarikçi başarıyla güncellendi."
      });
    } catch (error) {
      console.error("Error in updateSupplier:", error);
      return res.status(500).json({
        success: false,
        message: "Bir hata oluştu! Lütfen daha sonra tekrar deneyin!"
      });
    }
  };
  
  // Tedarikçi silme
  exports.deleteSupplier = async (req, res) => {
    try {
      const supplierId = req.params.id;
      const affectedRows = await deleteSupplierDB(supplierId);
  
      if (affectedRows === 0) {
        return res.status(404).json({
          success: false,
          message: "Tedarikçi bulunamadı!"
        });
      }
  
      return res.status(200).json({
        success: true,
        message: "Tedarikçi başarıyla silindi."
      });
    } catch (error) {
      console.error("Error in deleteSupplier:", error);
      return res.status(500).json({
        success: false,
        message: "Bir hata oluştu! Lütfen daha sonra tekrar deneyin!"
      });
    }
  };
  
  // Tek tedarikçi sorgulama
  exports.getSupplier = async (req, res) => {
    try {
      const supplierId = req.params.id;
      const supplier = await getSupplierDB(supplierId);
  
      if (!supplier) {
        return res.status(404).json({
          success: false,
          message: "Tedarikçi bulunamadı!"
        });
      }
  
      return res.status(200).json({
        success: true,
        data: supplier
      });
    } catch (error) {
      console.error("Error in getSupplier:", error);
      return res.status(500).json({
        success: false,
        message: "Bir hata oluştu! Lütfen daha sonra tekrar deneyin!"
      });
    }
  };
  
  // Tüm tedarikçileri sorgulama (tenant bazında)
  exports.getAllSuppliers = async (req, res) => {
    try {
      const tenantId = req.user.tenant_id;
      const suppliers = await getAllSuppliersDB(tenantId);
      return res.status(200).json({
        success: true,
        data: suppliers
      });
    } catch (error) {
      console.error("Error in getAllSuppliers:", error);
      return res.status(500).json({
        success: false,
        message: "Bir hata oluştu! Lütfen daha sonra tekrar deneyin!"
      });
    }
  };
  