const DeliveryPlatformService = require('../services/delivery-platform.service');

exports.handleGetirWebhook = async (req, res) => {
    try {
        const orderData = req.body;
        await DeliveryPlatformService.saveOrder(
            DeliveryPlatformService.platforms.GETIR,
            orderData.restaurantId,
            orderData
        );
        
        res.status(200).json({
            success: true,
            message: 'Order received'
        });
    } catch (error) {
        console.error('Getir Webhook Error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};

// Yeni sipariş webhook handler
exports.handleGetirNewOrder = async (req, res) => {
    try {
        // Gelen x-api-key'i kontrol et
        const apiKey = req.headers['x-api-key'];
        if (!apiKey || apiKey !== process.env.GETIR_API_KEY) {
            return res.status(401).json({
                success: false,
                message: 'Unauthorized'
            });
        }

        const orderData = req.body;
        await DeliveryPlatformService.saveOrder(
            DeliveryPlatformService.platforms.GETIR,
            orderData.restaurantId,
            {
                ...orderData,
                orderType: 'new'
            }
        );
        
        res.status(200).json({
            success: true,
            message: 'New order received'
        });
    } catch (error) {
        console.error('Getir New Order Error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};

// Sipariş iptal webhook handler
exports.handleGetirCancelOrder = async (req, res) => {
    try {
        const apiKey = req.headers['x-api-key'];
        if (!apiKey || apiKey !== process.env.GETIR_API_KEY) {
            return res.status(401).json({
                success: false,
                message: 'Unauthorized'
            });
        }

        const orderData = req.body;
        await DeliveryPlatformService.updateOrderStatus(
            orderData.orderId,
            orderData.restaurantId,
            'cancelled'
        );
        
        res.status(200).json({
            success: true,
            message: 'Order cancelled'
        });
    } catch (error) {
        console.error('Getir Cancel Order Error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};

// Kurye varış bildirimi webhook handler
exports.handleGetirCourierArrival = async (req, res) => {
    try {
        const apiKey = req.headers['x-api-key'];
        if (!apiKey || apiKey !== process.env.GETIR_API_KEY) {
            return res.status(401).json({
                success: false,
                message: 'Unauthorized'
            });
        }

        const courierData = req.body;
        // Kurye bilgisini işle
        await DeliveryPlatformService.updateCourierStatus(
            courierData.orderId,
            courierData.restaurantId,
            courierData
        );
        
        res.status(200).json({
            success: true,
            message: 'Courier status updated'
        });
    } catch (error) {
        console.error('Getir Courier Arrival Error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};

// Restoran durum değişikliği webhook handler
exports.handleGetirRestaurantStatus = async (req, res) => {
    try {
        const apiKey = req.headers['x-api-key'];
        if (!apiKey || apiKey !== process.env.GETIR_API_KEY) {
            return res.status(401).json({
                success: false,
                message: 'Unauthorized'
            });
        }

        const statusData = req.body;
        await DeliveryPlatformService.updateRestaurantStatus(
            statusData.restaurantId,
            statusData.status
        );
        
        res.status(200).json({
            success: true,
            message: 'Restaurant status updated'
        });
    } catch (error) {
        console.error('Getir Restaurant Status Error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};

exports.handleYemeksepetiWebhook = async (req, res) => {
    try {
        const orderData = req.body;
        await DeliveryPlatformService.saveOrder(
            DeliveryPlatformService.platforms.YEMEKSEPETI,
            orderData.restaurantId,
            orderData
        );
        
        res.status(200).json({
            success: true,
            message: 'Order received'
        });
    } catch (error) {
        console.error('Yemeksepeti Webhook Error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};

exports.handleTrendyolWebhook = async (req, res) => {
    try {
        const orderData = req.body;
        await DeliveryPlatformService.saveOrder(
            DeliveryPlatformService.platforms.TRENDYOL,
            orderData.restaurantId,
            orderData
        );
        
        res.status(200).json({
            success: true,
            message: 'Order received'
        });
    } catch (error) {
        console.error('Trendyol Webhook Error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};

exports.getTenantOrders = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const filters = {
            status: req.query.status,
            platformId: req.query.platformId,
            dateFrom: req.query.dateFrom,
            dateTo: req.query.dateTo
        };

        const orders = await DeliveryPlatformService.getTenantOrders(tenantId, filters);
        res.status(200).json(orders);
    } catch (error) {
        console.error('Get Orders Error:', error);
        res.status(500).json({
            success: false,
            message: 'Error retrieving orders'
        });
    }
};

exports.updateOrderStatus = async (req, res) => {
    try {
        const tenantId = req.user.tenant_id;
        const orderId = req.params.orderId;
        const { status } = req.body;

        await DeliveryPlatformService.updateOrderStatus(orderId, tenantId, status);
        res.status(200).json({
            success: true,
            message: 'Order status updated'
        });
    } catch (error) {
        console.error('Update Status Error:', error);
        res.status(500).json({
            success: false,
            message: 'Error updating order status'
        });
    }
};