const {
  getCategoriesDB,
  getPaymentTypesDB,
  getPrintSettingDB,
  getStoreSettingDB,
  getStoreTablesDB,
} = require("../services/settings.service");
const {
  getAllMenuItemsDB,
  getAllAddonsDB,
  getAllVariantsDB,
  getAllRecipeItemsDB,
} = require("../services/menu_item.service");
const { createOrderDB, updateOrderDB, getTableStatus, getTablesWithDetailsDB, getTableDetailsDB, getPOSQROrdersCountDB, getPOSQROrdersDB, updateQROrderStatusDB, cancelAllQROrdersDB } = require("../services/pos.service");
const { createInvoiceDB, savePartialPaymentforOrdersDB, getOrdersDB } = require("../services/orders.service");
const { updateStockAndRecordMovementDB } = require("../services/menu_item.service");
const { getUserAccessibleFloorsDB } = require("../services/floor.service");


exports.getPOSInitData = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const username = req.user.username;

    // Tüm gerekli verileri eşzamanlı olarak alın
    const [categories, paymentTypes, printSettings, storeSettings, storeTables, accessibleFloors] = await Promise.all([
      getCategoriesDB(tenantId),
      getPaymentTypesDB(true, tenantId, true), // forPOS=true to filter out hideOnPos=1
      getPrintSettingDB(tenantId),
      getStoreSettingDB(tenantId),
      getStoreTablesDB(tenantId),
      getUserAccessibleFloorsDB(username, tenantId),
    ]);

    // Kullanıcının erişim izni olan katları floor_id ve floor_name formatına dönüştür
    const floors = accessibleFloors.map(floor => ({
      floor_id: floor.id,
      floor_name: floor.title,
      floor_description: floor.description
    }));

    const [menuItems, addons, variants, recipeItems] = await Promise.all([
      getAllMenuItemsDB(tenantId),
      getAllAddonsDB(tenantId),
      getAllVariantsDB(tenantId),
      getAllRecipeItemsDB(tenantId)
    ]);

    // Sipariş verilerini alın
    const { kitchenOrders, kitchenOrdersItems, addons: orderAddons, partialPayments, orderDiscounts } = await getOrdersDB(tenantId);
    // Siparişleri masalara göre grupla
    const ordersGroupedByTable = kitchenOrders.reduce((acc, order) => {
      const tableId = order.table_id;
      if (!acc[tableId]) {
        acc[tableId] = [];
      }

      // Siparişin ürünlerini ekle
      const orderItems = kitchenOrdersItems.filter((item) => item.order_id == order.id);
      order.items = orderItems.map((item) => {
        const itemAddons = item.addons
          ? JSON.parse(item.addons).map((addonId) =>
              orderAddons.find((addon) => addon.id == addonId)
            )
          : [];
        return { ...item, addons: itemAddons };
      });

      // Siparişe parçalı ödemeleri ekle
      const orderPartialPayments = partialPayments.filter(pp => pp.order_id == order.id);
      order.partialPayments = orderPartialPayments;

      // Siparişe indirimlerini ekle
      const orderDiscountsForOrder = orderDiscounts.filter(od => od.order_id == order.id);
      order.discounts = orderDiscountsForOrder;

      acc[tableId].push(order);
      return acc;
    }, {});

    // Floor ve Store Tables verilerini birleştirme
    const floorAndTables = floors.map((floor) => {
      const tablesWithOrders = storeTables
        .filter((table) => table.floor === floor.floor_id)
        .map((table) => {
          const tableOrders = ordersGroupedByTable[table.id] || [];
          return {
            ...table,
            orders: tableOrders, // Masaya ait tüm siparişler
            order_ids: tableOrders.map((order) => order.id), // Masaya ait sipariş ID'leri
          };
        });

      return {
        floor_id: floor.floor_id,
        floor_name: floor.floor_name,
        tables: tablesWithOrders,
      };
    });

    const formattedMenuItems = menuItems.map((item) => {
      const itemAddons = addons.filter((addon) => addon.item_id == item.id);
      const itemVariants = variants.filter(
        (variant) => variant.item_id == item.id
      );
      const itemRecipeItems = recipeItems.filter(
        (recipeItem) => recipeItem.menu_item_id == item.id
      );

      return {
        ...item,
        addons: [...itemAddons],
        variants: [...itemVariants],
        recipeItems: [...itemRecipeItems]
      };
    });

    return res.status(200).json({
      categories,
      paymentTypes,
      printSettings,
      storeSettings,
      storeTables,
      floors: floorAndTables, // Floor ve Store Tables birleşik olarak dönüyor
      menuItems: formattedMenuItems,
    });
  } catch (error) {
    console.error(error);
    return res.status(500).json({
      success: false,
      message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin.",
    });
  }
};

exports.createOrder = async (req, res) => {
  try {
    const userName = req.user.username
    const tenantId = req.user.tenant_id;
    const { cart, deliveryType, customerType, customerId, tableId, selectedQrOrderItem, floorId } = req.body;

    const orderId = selectedQrOrderItem;
    const status = "completed";



    if (selectedQrOrderItem) {

      await updateQROrderStatusDB(tenantId, orderId, status);
    }

    if (!cart || cart.length === 0) {
      return res.status(400).json({ success: false, message: "Sepet boş!" });
    }

    // 2. Masa kontrolü sadece tableId varsa yapılır
    if (tableId) {
      const tableStatus = await getTableStatus(tableId, tenantId);
      if (!tableStatus) {
        return res.status(400).json({ success: false, message: "Masa bulunamadı!" });
      }
      if (tableStatus.table_status === "locked") {
        return res.status(400).json({ success: false, message: "Masa kilitli!" });
      }

      // 3. Sipariş Güncelle veya Yeni Sipariş Oluştur
      if (tableStatus.table_status === "busy" && tableStatus.order_ids) {
        const lastOrderId = tableStatus.order_ids;
        const result = await updateOrderDB(tenantId, cart, lastOrderId, userName); // username parametresi eklendi



        return res.status(200).json({
          success: true,
          message: "Sipariş mevcut masaya eklendi.",
          orderId: result.orderId,
        });
      } else {
        // Yeni sipariş oluştur
        const result = await createOrderDB(
          tenantId,
          userName, // **✔ USERNAME Doğru geçirildi**
          cart,
          deliveryType,
          customerType,
          customerId?.phone,
          tableId,
          'pending',
          null,
          floorId
        );

        return res.status(200).json({
          success: true,
          message: `Sipariş oluşturuldu. Token: ${result.tokenNo}`,
          tokenNo: result.tokenNo,
          orderId: result.orderId,
        });
      }
    }

    // Eğer tableId yoksa doğrudan yeni sipariş oluştur
    const result = await createOrderDB(
      tenantId,
      userName, // **✔ USERNAME Doğru geçirildi**
      cart,
      deliveryType,
      customerType,
      customerId?.phone,
      null,
      'pending',
      null,
      floorId
    );



    return res.status(200).json({
      success: true,
      message: `Sipariş oluşturuldu. Token: ${result.tokenNo}`,
      tokenNo: result.tokenNo,
      orderId: result.orderId,
    });
  } catch (error) {
    console.error(error);
    return res.status(500).json({
      success: false,
      message: "Bir hata oluştu, lütfen tekrar deneyin.",
    });
  }
};


exports.createOrderAndInvoice = async (req, res) => {

  try {
    const tenantId = req.user.tenant_id;
    const userName = req.user.username;

    const {cart, deliveryType, customerType, customerId, tableId, netTotal, taxTotal, total, selectedQrOrderItem, selectedPaymentType, floorId} = req.body;

    if(cart?.length == 0) {
      return res.status(400).json({
        success: false,
        message: "Sepet Boş!"
      });
    }

    // create invoice
    const now = new Date();
    const date = `${now.getFullYear()}-${(now.getMonth()+1).toString().padStart(2, '0')}-${now.getDate().toString().padStart(2, '0')} ${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;

    const invoiceId = await createInvoiceDB(netTotal, taxTotal, total, date, selectedPaymentType, tenantId);
    // create invoice

    const result = await createOrderDB(
      tenantId,
      userName,
      cart,
      deliveryType,
      customerType,
      customerId?.phone || null,
      tableId || null,
      'paid',
      invoiceId,
      floorId
    );
    const orderId = result.orderId;
    const tokenNo = result.tokenNo;

    if(selectedQrOrderItem) {
      await updateQROrderStatusDB(tenantId, selectedQrOrderItem, "completed");
    }


    const payments = [
      {
        orderId,
        method: selectedPaymentType,
        amount: total,
        productName: "Genel Sipariş",
        orderItem_id: null, // Genel ödeme olduğu için null
      },
    ];

    await savePartialPaymentforOrdersDB(payments, tenantId);

    return res.status(200).json({
      success: true,
      message: `Sipariş Oluşturuldu. Token: ${tokenNo}`,
      tokenNo,
      orderId,
      invoiceId
    });

  } catch (error) {
    console.error(error);
    return res.status(500).json({
      success: false,
      message: "Error processing the request, please try after sometime!"
    });
  }

};

exports.getPOSQROrdersCount = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;

    const totalQROrders = await getPOSQROrdersCountDB(tenantId);
    return res.status(200).json({
      status: true,
      totalQROrders: totalQROrders
    })
  } catch (error) {
    console.error(error);
    return res.status(500).json({
      success: false,
      message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin.",
    });
  }
};

exports.getPOSQROrders = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;

    const { kitchenOrders, kitchenOrdersItems, addons } = await getPOSQROrdersDB(tenantId);

    const formattedOrders = kitchenOrders.map((order)=>{
      const orderItems = kitchenOrdersItems.filter((oi)=>oi.order_id == order.id);

      orderItems.forEach((oi, index)=>{
        const addonsIds = oi?.addons ? JSON.parse(oi?.addons) : null;

        if(addonsIds) {
          const itemAddons = addonsIds.map((addonId)=>{
            const addon = addons.filter((a)=>a.id == addonId);
            return addon[0];
          });
          orderItems[index].addons = [...itemAddons];
        }
      });

      return {
        ...order,
        items: orderItems
      }
    })

    return res.status(200).json(formattedOrders)
  } catch (error) {
    console.error(error);
    return res.status(500).json({
      success: false,
      message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin.",
    });
  }
};

exports.updatePOSQROrderStatus = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const orderId = req.params.id;
    const status = req.body.status;

    if(!status) {
      return res.status(400).json({
        success: false,
        message: "Please provide required details!"
      });
    }

    await updateQROrderStatusDB(tenantId, orderId, status);
    return res.status(200).json({
      status: true,
      message: "QR Siparişteki Ürün Durumu Değişti."
    });
  } catch (error) {
    console.error(error);
    return res.status(500).json({
      success: false,
      message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin.",
    });
  }
};

exports.cancelAllQROrders = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const status = 'cancelled';

    await cancelAllQROrdersDB(tenantId, status);
    return res.status(200).json({
      status: true,
      message: "Tüm QR Siparişleri İptal Edildi."
    });
  } catch (error) {
    console.error(error);
    return res.status(500).json({
      success: false,
      message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin.",
    });
  }
};

/**
 * Masaları ve detaylarını getirir
 * Garson, açılış saati, hesap tutarı, kısmi ödeme bilgisi
 */
exports.getTablesWithDetails = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;

    const tables = await getTablesWithDetailsDB(tenantId);

    return res.status(200).json({
      tables
    });
  } catch (error) {
    console.error("getTablesWithDetails error:", error);
    return res.status(500).json({
      success: false,
      message: "Masalar getirilirken bir hata oluştu."
    });
  }
};


exports.getTablesWithDetaylar = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;

    // Tüm gerekli verileri eşzamanlı olarak alın
    const [categories, paymentTypes, printSettings, storeSettings, ] = await Promise.all([
      getCategoriesDB(tenantId),
      getPaymentTypesDB(true, tenantId, true), // forPOS=true to filter out hideOnPos=1
      getPrintSettingDB(tenantId),
      getStoreSettingDB(tenantId),
    ]);

    const [menuItems, addons, variants, recipeItems] = await Promise.all([
      getAllMenuItemsDB(tenantId),
      getAllAddonsDB(tenantId),
      getAllVariantsDB(tenantId),
      getAllRecipeItemsDB(tenantId)
    ]);

    const formattedMenuItems = menuItems.map((item) => {
      const itemAddons = addons.filter((addon) => addon.item_id == item.id);
      const itemVariants = variants.filter(
        (variant) => variant.item_id == item.id
      );
      const itemRecipeItems = recipeItems.filter(
        (recipeItem) => recipeItem.menu_item_id == item.id
      );

      return {
        ...item,
        addons: [...itemAddons],
        variants: [...itemVariants],
        recipeItems: [...itemRecipeItems]
      };
    });

    return res.status(200).json({
      categories,
      paymentTypes,
      printSettings,
      storeSettings,
      menuItems: formattedMenuItems,
    });
  } catch (error) {
    console.error(error);
    return res.status(500).json({
      success: false,
      message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin.",
    });
  }
};

/**
 * Belirli bir masanın tüm detaylarını getirir
 * Siparişler, ürünler, ödemeler, indirimler dahil
 */
exports.getTablesWithDetaylar = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const tableId = req.params.tableId;

    if (!tableId) {
      return res.status(400).json({
        success: false,
        message: "Table ID gerekli."
      });
    }

    const tableDetails = await getTableDetailsDB(tableId, tenantId);

    if (!tableDetails.table) {
      return res.status(404).json({
        success: false,
        message: "Masa bulunamadı."
      });
    }

    return res.status(200).json({
      success: true,
      data: tableDetails
    });
  } catch (error) {
    console.error("getTablesWithDetaylar error:", error);
    return res.status(500).json({
      success: false,
      message: "Masa detayları getirilirken bir hata oluştu."
    });
  }
};
