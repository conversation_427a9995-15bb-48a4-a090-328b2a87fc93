const {
  getBusinessHoursDB,
  updateBusinessHoursDB,
  deleteBusinessHoursDB,
  isWithinBusinessHoursDB
} = require("../services/business-hours.service");

/**
 * Tenant'ın çalışma saatlerini getirir
 */
exports.getBusinessHours = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;

    const businessHours = await getBusinessHoursDB(tenantId);

    if (!businessHours) {
      // Varsayılan değerler döndür
      return res.status(200).json({
        success: true,
        data: {
          day_start_time: "08:00:00",
          day_end_time: "23:59:00",
          is_overnight: false,
          is_active: true
        }
      });
    }

    return res.status(200).json({
      success: true,
      data: {
        ...businessHours,
        is_overnight: businessHours.is_overnight === 1
      }
    });
  } catch (error) {
    console.error("getBusinessHours error:", error);
    return res.status(500).json({
      success: false,
      message: "Çalışma saatleri getirilirken bir hata oluştu",
      error: error.message
    });
  }
};

/**
 * Tenant'ın çalışma saatlerini günceller
 */
exports.updateBusinessHours = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const { day_start_time, day_end_time, is_overnight } = req.body;

    // Validasyon
    if (!day_start_time || !day_end_time) {
      return res.status(400).json({
        success: false,
        message: "Gün başlangıç ve bitiş saatleri zorunludur"
      });
    }

    // Saat formatı kontrolü (HH:MM veya HH:MM:SS)
    const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9](:[0-5][0-9])?$/;
    if (!timeRegex.test(day_start_time) || !timeRegex.test(day_end_time)) {
      return res.status(400).json({
        success: false,
        message: "Geçersiz saat formatı. HH:MM veya HH:MM:SS formatında olmalıdır"
      });
    }

    // Saat formatını HH:MM:SS'ye çevir
    const formatTime = (time) => {
      return time.length === 5 ? `${time}:00` : time;
    };

    const businessHours = {
      day_start_time: formatTime(day_start_time),
      day_end_time: formatTime(day_end_time),
      is_overnight: Boolean(is_overnight)
    };

    const success = await updateBusinessHoursDB(tenantId, businessHours);

    if (!success) {
      return res.status(500).json({
        success: false,
        message: "Çalışma saatleri güncellenemedi"
      });
    }

    return res.status(200).json({
      success: true,
      message: "Çalışma saatleri başarıyla güncellendi",
      data: businessHours
    });
  } catch (error) {
    console.error("updateBusinessHours error:", error);
    return res.status(500).json({
      success: false,
      message: "Çalışma saatleri güncellenirken bir hata oluştu",
      error: error.message
    });
  }
};

/**
 * Tenant'ın çalışma saatlerini siler
 */
exports.deleteBusinessHours = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;

    const success = await deleteBusinessHoursDB(tenantId);

    if (!success) {
      return res.status(404).json({
        success: false,
        message: "Çalışma saatleri bulunamadı veya silinemedi"
      });
    }

    return res.status(200).json({
      success: true,
      message: "Çalışma saatleri başarıyla silindi"
    });
  } catch (error) {
    console.error("deleteBusinessHours error:", error);
    return res.status(500).json({
      success: false,
      message: "Çalışma saatleri silinirken bir hata oluştu",
      error: error.message
    });
  }
};

/**
 * Şu anki saatin çalışma saatleri içinde olup olmadığını kontrol eder
 */
exports.checkBusinessHours = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const { checkTime } = req.query; // Opsiyonel: belirli bir saati kontrol etmek için

    const isWithinHours = await isWithinBusinessHoursDB(tenantId, checkTime);
    const businessHours = await getBusinessHoursDB(tenantId);

    return res.status(200).json({
      success: true,
      data: {
        isWithinBusinessHours: isWithinHours,
        currentTime: new Date().toTimeString().split(' ')[0],
        businessHours: businessHours ? {
          day_start_time: businessHours.day_start_time,
          day_end_time: businessHours.day_end_time,
          is_overnight: businessHours.is_overnight === 1
        } : null
      }
    });
  } catch (error) {
    console.error("checkBusinessHours error:", error);
    return res.status(500).json({
      success: false,
      message: "Çalışma saatleri kontrol edilirken bir hata oluştu",
      error: error.message
    });
  }
};
