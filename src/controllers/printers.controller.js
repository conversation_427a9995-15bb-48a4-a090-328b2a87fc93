const {
    addPrinterService,
    getPrintersService,
    updatePrinterService,
    deletePrinterService,
  } = require("../services/printers.service");
  
  exports.addPrinter = async (req, res) => {
    try {
      const tenantId = req.user.tenant_id;
      const { name, path, type } = req.body;
  
      if (!name || !path || !type) {
        return res.status(400).json({ success: false, message: "Tüm alanlar zorunludur." });
      }
  
      // ENUM doğrulaması
      if (!["kitchen", "cashier"].includes(type)) {
        return res.status(400).json({ success: false, message: "Geçersiz yazıcı türü." });
      }
  
      await addPrinterService(tenantId, name, path, type);
  
      return res.status(201).json({ success: true, message: "Yazıcı başarıyla eklendi." });
    } catch (error) {
      console.error("Yazıcı eklenirken hata:", error);
      return res.status(500).json({ success: false, message: "Yazıcı eklenemedi", error: error.message });
    }
  };
  
  exports.getPrinters = async (req, res) => {
    try {
      const tenantId = req.user.tenant_id;
      const printers = await getPrintersService(tenantId);
      return res.status(200).json(printers);
    } catch (error) {
      console.error("Yazıcılar alınırken hata:", error);
      return res.status(500).json({
        success: false,
        message: "Yazıcılar alınamadı",
        error: error.message
      });
    }
  };
  
  exports.updatePrinter = async (req, res) => {
    try {
      const { printerId } = req.params;
      const { name, path, type } = req.body;
  
      await updatePrinterService(printerId, name, path, type);
  
      return res.status(200).json({
        success: true,
        message: "Yazıcı başarıyla güncellendi"
      });
    } catch (error) {
      console.error("Yazıcı güncellenirken hata:", error);
      return res.status(500).json({
        success: false,
        message: "Yazıcı güncellenemedi",
        error: error.message
      });
    }
  };
  
  exports.deletePrinter = async (req, res) => {
    try {
      const { printerId } = req.params;
  
      await deletePrinterService(printerId);
  
      return res.status(200).json({
        success: true,
        message: "Yazıcı başarıyla silindi"
      });
    } catch (error) {
      console.error("Yazıcı silinirken hata:", error);
      return res.status(500).json({
        success: false,
        message: "Yazıcı silinemedi",
        error: error.message
      });
    }
  };