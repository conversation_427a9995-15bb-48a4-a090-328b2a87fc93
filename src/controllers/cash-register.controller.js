const {
  getAllCashRegistersDB,
  getCashRegisterByIdDB,
  addCashRegisterDB,
  updateCashRegisterDB,
  deleteCashRegisterDB,
  getUserActiveSessionDB,
  getRegisterActiveSessionDB,
  openCashRegisterSessionDB,
  closeCashRegisterSessionDB,
  getCashRegisterSessionsDB,
  getCashRegisterSessionByIdDB,
  getSessionTransactionsDB,
  addPaymentTransactionDB,
  updatePaymentTransactionDB,
  deletePaymentTransactionDB,
  addCashWithdrawalDB,
  addCashDepositDB,
  getLastSessionsForCashRegistersDB
} = require('../services/cash-register.service');

/**
 * Tüm kasaları listeler
 */
exports.getAllCashRegisters = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const registers = await getAllCashRegistersDB(tenantId);

    return res.status(200).json({
      success: true,
      data: registers
    });
  } catch (error) {
    console.error("getAllCashRegisters error:", error);
    return res.status(500).json({
      success: false,
      message: "Kasalar listelenirken bir hata oluştu",
      error: error.message
    });
  }
};

/**
 * Belirli bir kasayı getirir
 */
exports.getCashRegisterById = async (req, res) => {
  try {
    const { id } = req.params;
    const tenantId = req.user.tenant_id;

    const register = await getCashRegisterByIdDB(id, tenantId);

    if (!register) {
      return res.status(404).json({
        success: false,
        message: "Kasa bulunamadı"
      });
    }

    return res.status(200).json({
      success: true,
      data: register
    });
  } catch (error) {
    console.error("getCashRegisterById error:", error);
    return res.status(500).json({
      success: false,
      message: "Kasa bilgileri getirilirken bir hata oluştu",
      error: error.message
    });
  }
};

/**
 * Yeni kasa ekler
 */
exports.addCashRegister = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const { name, description, location } = req.body;

    // Validasyon
    if (!name) {
      return res.status(400).json({
        success: false,
        message: "Kasa adı zorunludur"
      });
    }

    const registerId = await addCashRegisterDB({ name, description, location }, tenantId);

    return res.status(201).json({
      success: true,
      message: "Kasa başarıyla eklendi",
      data: { id: registerId }
    });
  } catch (error) {
    console.error("addCashRegister error:", error);
    return res.status(500).json({
      success: false,
      message: "Kasa eklenirken bir hata oluştu",
      error: error.message
    });
  }
};

/**
 * Kasa bilgilerini günceller
 */
exports.updateCashRegister = async (req, res) => {
  try {
    const { id } = req.params;
    const tenantId = req.user.tenant_id;
    const { name, description, location, is_active } = req.body;

    // Validasyon
    if (!name) {
      return res.status(400).json({
        success: false,
        message: "Kasa adı zorunludur"
      });
    }

    const success = await updateCashRegisterDB(id, { name, description, location, is_active }, tenantId);

    if (!success) {
      return res.status(404).json({
        success: false,
        message: "Kasa bulunamadı veya güncellenemedi"
      });
    }

    return res.status(200).json({
      success: true,
      message: "Kasa başarıyla güncellendi"
    });
  } catch (error) {
    console.error("updateCashRegister error:", error);
    return res.status(500).json({
      success: false,
      message: "Kasa güncellenirken bir hata oluştu",
      error: error.message
    });
  }
};

/**
 * Kasa siler
 */
exports.deleteCashRegister = async (req, res) => {
  try {
    const { id } = req.params;
    const tenantId = req.user.tenant_id;

    const success = await deleteCashRegisterDB(id, tenantId);

    if (!success) {
      return res.status(404).json({
        success: false,
        message: "Kasa bulunamadı veya silinemedi"
      });
    }

    return res.status(200).json({
      success: true,
      message: "Kasa başarıyla silindi"
    });
  } catch (error) {
    console.error("deleteCashRegister error:", error);
    return res.status(500).json({
      success: false,
      message: "Kasa silinirken bir hata oluştu",
      error: error.message
    });
  }
};

/**
 * Kullanıcının aktif kasa oturumunu getirir
 */
exports.getUserActiveSession = async (req, res) => {
  try {
    const username = req.user.username;
    const tenantId = req.user.tenant_id;

    const session = await getUserActiveSessionDB(username, tenantId);

    return res.status(200).json({
      success: true,
      data: session
    });
  } catch (error) {
    console.error("getUserActiveSession error:", error);
    return res.status(500).json({
      success: false,
      message: "Aktif oturum bilgisi getirilirken bir hata oluştu",
      error: error.message
    });
  }
};

/**
 * Kasanın aktif oturumunu getirir
 */
exports.getRegisterActiveSession = async (req, res) => {
  try {
    const { registerId } = req.params;
    const tenantId = req.user.tenant_id;

    const session = await getRegisterActiveSessionDB(registerId, tenantId);

    return res.status(200).json({
      success: true,
      data: session
    });
  } catch (error) {
    console.error("getRegisterActiveSession error:", error);
    return res.status(500).json({
      success: false,
      message: "Aktif oturum bilgisi getirilirken bir hata oluştu",
      error: error.message
    });
  }
};

/**
 * Kasa oturumu açar
 */
exports.openCashRegisterSession = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const username = req.user.username;
    const { cash_register_id, opening_amount, opening_notes } = req.body;

    // Validasyon
    if (!cash_register_id) {
      return res.status(400).json({
        success: false,
        message: "Kasa ID zorunludur"
      });
    }

    if (opening_amount === undefined || opening_amount === null) {
      return res.status(400).json({
        success: false,
        message: "Açılış tutarı zorunludur"
      });
    }

    const sessionId = await openCashRegisterSessionDB({
      cash_register_id,
      opened_by: username,
      opening_amount,
      opening_notes
    }, tenantId);

    // Socket bildirimi gönder
    if (global.io) {
      try {
        global.io.to(tenantId.toString()).emit('cash-register-session-opened', {
          sessionId,
          cashRegisterId: cash_register_id,
          openedBy: username
        });
      } catch (socketError) {
        console.error('Socket gönderim hatası:', socketError);
      }
    }

    return res.status(201).json({
      success: true,
      message: "Kasa oturumu başarıyla açıldı",
      data: { id: sessionId }
    });
  } catch (error) {
    console.error("openCashRegisterSession error:", error);
    return res.status(500).json({
      success: false,
      message: "Kasa oturumu açılırken bir hata oluştu",
      error: error.message
    });
  }
};

/**
 * Kasa oturumunu kapatır
 */
exports.closeCashRegisterSession = async (req, res) => {
  try {
    const { id } = req.params;
    const tenantId = req.user.tenant_id;
    const username = req.user.username;
    const { closing_amount, closing_notes, bank_details } = req.body;

    // Validasyon
    if (closing_amount === undefined || closing_amount === null) {
      return res.status(400).json({
        success: false,
        message: "Kapanış tutarı zorunludur"
      });
    }

    const result = await closeCashRegisterSessionDB(id, {
      closed_by: username,
      closing_amount,
      closing_notes,
      bank_details
    }, tenantId);

    if (!result.success) {
      return res.status(404).json({
        success: false,
        message: "Kasa oturumu bulunamadı veya kapatılamadı"
      });
    }

    // Socket bildirimi gönder
    if (global.io) {
      try {
        global.io.to(tenantId.toString()).emit('cash-register-session-closed', {
          sessionId: id,
          closedBy: username
        });
      } catch (socketError) {
        console.error('Socket gönderim hatası:', socketError);
      }
    }

    return res.status(200).json({
      success: true,
      message: "Kasa oturumu başarıyla kapatıldı",
      data: {
        lastTransactions: result.lastTransactions
      }
    });
  } catch (error) {
    console.error("closeCashRegisterSession error:", error);
    return res.status(500).json({
      success: false,
      message: "Kasa oturumu kapatılırken bir hata oluştu",
      error: error.message
    });
  }
};

/**
 * Kasa oturumlarını listeler (sayfalama ile)
 */
exports.getCashRegisterSessions = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const filters = {
      status: req.query.status,
      cashRegisterId: req.query.cashRegisterId ? parseInt(req.query.cashRegisterId) : null,
      username: req.query.username,
      startDate: req.query.startDate,
      endDate: req.query.endDate || req.query.startDate
    };

    // Sayfalama parametreleri
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;

    const result = await getCashRegisterSessionsDB(filters, tenantId, page, limit);

    return res.status(200).json({
      success: true,
      data: result.data,
      pagination: result.pagination
    });
  } catch (error) {
    console.error("getCashRegisterSessions error:", error);
    return res.status(500).json({
      success: false,
      message: "Kasa oturumları listelenirken bir hata oluştu",
      error: error.message
    });
  }
};

/**
 * Belirli bir kasa oturumunu getirir
 */
exports.getCashRegisterSessionById = async (req, res) => {
  try {
    const { id } = req.params;
    const tenantId = req.user.tenant_id;

    const session = await getCashRegisterSessionByIdDB(id, tenantId);

    if (!session) {
      return res.status(404).json({
        success: false,
        message: "Kasa oturumu bulunamadı"
      });
    }

    // Oturumdaki işlemleri getir
    const transactions = await getSessionTransactionsDB(id, tenantId);

    return res.status(200).json({
      success: true,
      data: {
        session,
        transactions
      }
    });
  } catch (error) {
    console.error("getCashRegisterSessionById error:", error);
    return res.status(500).json({
      success: false,
      message: "Kasa oturumu bilgileri getirilirken bir hata oluştu",
      error: error.message
    });
  }
};

/**
 * Ödeme işlemi ekler
 */
exports.addPaymentTransaction = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const username = req.user.username;

    const {
      order_id,
      payment_type_id,
      amount,
      transaction_type,
      notes,
      invoice_id
    } = req.body;

    // Validasyon
    if (!order_id) {
      return res.status(400).json({
        success: false,
        message: "Sipariş ID zorunludur"
      });
    }

    if (!payment_type_id) {
      return res.status(400).json({
        success: false,
        message: "Ödeme türü zorunludur"
      });
    }

    if (amount === undefined || amount === null || amount <= 0) {
      return res.status(400).json({
        success: false,
        message: "Geçerli bir ödeme tutarı girilmelidir"
      });
    }

    // Kullanıcının aktif kasa oturumunu kontrol et
    const activeSession = await getUserActiveSessionDB(username, tenantId);

    if (!activeSession) {
      return res.status(400).json({
        success: false,
        message: "Ödeme işlemi yapabilmek için aktif bir kasa oturumu açmanız gerekmektedir"
      });
    }

    // Ödeme işlemini kaydet
    const transactionId = await addPaymentTransactionDB({
      order_id,
      payment_type_id,
      amount,
      transaction_type: transaction_type || 'payment',
      notes,
      created_by: username,
      invoice_id,
      cash_register_session_id: activeSession.id
    }, tenantId);

    // Socket bildirimi gönder
    if (global.io) {
      try {
        global.io.to(tenantId.toString()).emit('payment-transaction-added', {
          transactionId,
          orderId: order_id,
          amount,
          paymentTypeId: payment_type_id,
          createdBy: username
        });
      } catch (socketError) {
        console.error('Socket gönderim hatası:', socketError);
      }
    }

    return res.status(201).json({
      success: true,
      message: "Ödeme işlemi başarıyla kaydedildi",
      data: { id: transactionId }
    });
  } catch (error) {
    console.error("addPaymentTransaction error:", error);
    return res.status(500).json({
      success: false,
      message: "Ödeme işlemi kaydedilirken bir hata oluştu",
      error: error.message
    });
  }
};

/**
 * Ödeme işlemini günceller
 */
exports.updatePaymentTransaction = async (req, res) => {
  try {
    const { id } = req.params;
    const tenantId = req.user.tenant_id;
    const username = req.user.username;

    const {
      payment_type_id,
      amount,
      transaction_type,
      status,
      notes
    } = req.body;

    // Validasyon
    if (!payment_type_id) {
      return res.status(400).json({
        success: false,
        message: "Ödeme türü zorunludur"
      });
    }

    if (amount === undefined || amount === null || amount <= 0) {
      return res.status(400).json({
        success: false,
        message: "Geçerli bir ödeme tutarı girilmelidir"
      });
    }

    const success = await updatePaymentTransactionDB(id, {
      payment_type_id,
      amount,
      transaction_type: transaction_type || 'payment',
      status: status || 'completed',
      notes,
      updated_by: username
    }, tenantId);

    if (!success) {
      return res.status(404).json({
        success: false,
        message: "Ödeme işlemi bulunamadı veya güncellenemedi"
      });
    }

    // Socket bildirimi gönder
    if (global.io) {
      try {
        global.io.to(tenantId.toString()).emit('payment-transaction-updated', {
          transactionId: id,
          updatedBy: username
        });
      } catch (socketError) {
        console.error('Socket gönderim hatası:', socketError);
      }
    }

    return res.status(200).json({
      success: true,
      message: "Ödeme işlemi başarıyla güncellendi"
    });
  } catch (error) {
    console.error("updatePaymentTransaction error:", error);
    return res.status(500).json({
      success: false,
      message: "Ödeme işlemi güncellenirken bir hata oluştu",
      error: error.message
    });
  }
};

/**
 * Ödeme işlemini siler
 */
exports.deletePaymentTransaction = async (req, res) => {
  try {
    const { id } = req.params;
    const tenantId = req.user.tenant_id;
    const username = req.user.username;

    const success = await deletePaymentTransactionDB(id, username, tenantId);

    if (!success) {
      return res.status(404).json({
        success: false,
        message: "Ödeme işlemi bulunamadı veya silinemedi"
      });
    }

    // Socket bildirimi gönder
    if (global.io) {
      try {
        global.io.to(tenantId.toString()).emit('payment-transaction-deleted', {
          transactionId: id,
          deletedBy: username
        });
      } catch (socketError) {
        console.error('Socket gönderim hatası:', socketError);
      }
    }

    return res.status(200).json({
      success: true,
      message: "Ödeme işlemi başarıyla silindi"
    });
  } catch (error) {
    console.error("deletePaymentTransaction error:", error);
    return res.status(500).json({
      success: false,
      message: "Ödeme işlemi silinirken bir hata oluştu",
      error: error.message
    });
  }
};

/**
 * Kasadan para çıkışı yapar
 */
exports.addCashWithdrawal = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const username = req.user.username;

    const {
      amount,
      reason,
      payment_type_id
    } = req.body;

    // Validasyon
    if (!payment_type_id) {
      return res.status(400).json({
        success: false,
        message: "Ödeme türü zorunludur"
      });
    }

    if (amount === undefined || amount === null || amount <= 0) {
      return res.status(400).json({
        success: false,
        message: "Geçerli bir çıkış tutarı girilmelidir"
      });
    }

    if (!reason) {
      return res.status(400).json({
        success: false,
        message: "Para çıkışı nedeni zorunludur"
      });
    }

    // Kullanıcının aktif kasa oturumunu kontrol et
    const activeSession = await getUserActiveSessionDB(username, tenantId);

    if (!activeSession) {
      return res.status(400).json({
        success: false,
        message: "Para çıkışı yapabilmek için aktif bir kasa oturumu açmanız gerekmektedir"
      });
    }

    // Para çıkışını kaydet
    const transactionId = await addCashWithdrawalDB({
      amount,
      reason,
      payment_type_id,
      created_by: username,
      cash_register_session_id: activeSession.id
    }, tenantId);

    // Socket bildirimi gönder
    if (global.io) {
      try {
        global.io.to(tenantId.toString()).emit('cash-withdrawal-added', {
          transactionId,
          amount,
          paymentTypeId: payment_type_id,
          createdBy: username
        });
      } catch (socketError) {
        console.error('Socket gönderim hatası:', socketError);
      }
    }

    return res.status(201).json({
      success: true,
      message: "Para çıkışı başarıyla kaydedildi",
      data: { id: transactionId }
    });
  } catch (error) {
    console.error("addCashWithdrawal error:", error);
    return res.status(500).json({
      success: false,
      message: "Para çıkışı kaydedilirken bir hata oluştu",
      error: error.message
    });
  }
};

/**
 * Kasaya para girişi yapar
 */
exports.addCashDeposit = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const username = req.user.username;

    const {
      amount,
      reason,
      payment_type_id
    } = req.body;

    // Validasyon
    if (!payment_type_id) {
      return res.status(400).json({
        success: false,
        message: "Ödeme türü zorunludur"
      });
    }

    if (amount === undefined || amount === null || amount <= 0) {
      return res.status(400).json({
        success: false,
        message: "Geçerli bir giriş tutarı girilmelidir"
      });
    }

    if (!reason) {
      return res.status(400).json({
        success: false,
        message: "Para girişi nedeni zorunludur"
      });
    }

    // Kullanıcının aktif kasa oturumunu kontrol et
    const activeSession = await getUserActiveSessionDB(username, tenantId);

    if (!activeSession) {
      return res.status(400).json({
        success: false,
        message: "Para girişi yapabilmek için aktif bir kasa oturumu açmanız gerekmektedir"
      });
    }

    // Para girişini kaydet
    const transactionId = await addCashDepositDB({
      amount,
      reason,
      payment_type_id,
      created_by: username,
      cash_register_session_id: activeSession.id
    }, tenantId);

    // Socket bildirimi gönder
    if (global.io) {
      try {
        global.io.to(tenantId.toString()).emit('cash-deposit-added', {
          transactionId,
          amount,
          paymentTypeId: payment_type_id,
          createdBy: username
        });
      } catch (socketError) {
        console.error('Socket gönderim hatası:', socketError);
      }
    }

    return res.status(201).json({
      success: true,
      message: "Para girişi başarıyla kaydedildi",
      data: { id: transactionId }
    });
  } catch (error) {
    console.error("addCashDeposit error:", error);
    return res.status(500).json({
      success: false,
      message: "Para girişi kaydedilirken bir hata oluştu",
      error: error.message
    });
  }
};

/**
 * Her kasa için son oturumu getirir
 */
exports.getLastSessionsForCashRegisters = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;

    const sessions = await getLastSessionsForCashRegistersDB(tenantId);

    return res.status(200).json({
      success: true,
      data: sessions
    });
  } catch (error) {
    console.error("getLastSessionsForCashRegisters error:", error);
    return res.status(500).json({
      success: false,
      message: "Kasa oturumları getirilirken bir hata oluştu",
      error: error.message
    });
  }
};
