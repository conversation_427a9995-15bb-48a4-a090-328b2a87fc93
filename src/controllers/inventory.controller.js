const {
  addInventoryItemDB,
  getInventoryItemsDB,
  updateInventoryItemDB,
  deleteInventoryItemDB,
  addInventoryItemStockMovementDB,
  getInventoryLogsDB,
  getCummulativeInventoryMovementsDB,
  getInventoryUsageVsCurrentStockDB,
} = require("../services/inventory.service");

exports.addInventoryItem = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const username = req.user.username;
    const {
      title,
      quantity,
      unit,
      min_quantity_threshold,
    } = req.body;

    if (!title || !quantity || !unit || !min_quantity_threshold) {
      return res.status(400).json({
        success: false,
        message: "<PERSON>üm Zorunlu Alanları Doldurunuz!",
      });
    }

    const newItemId = await addInventoryItemDB(
      title,
      quantity,
      unit,
      min_quantity_threshold,
      tenantId,
      username
    );

    return res.status(200).json({
      success: true,
      message:"<PERSON><PERSON><PERSON>!",
      itemId: newItemId,
    });
  } catch (error) {
    console.error(error);
    return res.status(500).json({
      success: false,
      message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin.",
    });
  }
};

exports.getInventoryItems = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const status = req.query.status || null;
    const {items, statusCounts} = await getInventoryItemsDB(status, tenantId);
    return res.status(200).json({items, statusCounts});
  } catch (error) {
    console.error(error);
    return res.status(500).json({
      success: false,
      message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin.",
    });
  }
};

exports.updateInventoryItem = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const itemId = req.params.id;
    const {
      title,
      unit,
      min_quantity_threshold,
    } = req.body;

    await updateInventoryItemDB(
      itemId,
      title,
      unit,
      min_quantity_threshold,
      tenantId
    );

    return res.status(200).json({
      success: true,
      message: "Envantner guncellendi",
    });
  } catch (error) {
    console.error(error);
    return res.status(500).json({
      success: false,
      message: "Envantner guncellenemedi!",
    });
  }
};

exports.addInventoryItemStockMovement = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const username = req.user.username;
    const itemId = req.params.id;
    const { movementType, quantity, note } = req.body;

    if(!movementType){
      return res.status(400).json({
        success: false,
        message: "Tüm Zorunlu Alanları Doldurunuz!",
      });
    }

    if (!quantity || quantity <= 0) {
      return res.status(400).json({
        success: false,
        message: "Geçerli bir miktar giriniz!",
      });
    }

    await addInventoryItemStockMovementDB(req, itemId, movementType, quantity, note, tenantId, username);

    return res.status(200).json({
      success: true,
      message: "Stok Eklendi!",
    });
  } catch (error) {
    console.error(error);

     const knownErrors = [
      "Envanter bulunamadı!",
      "Geçersiz hareket türü!",
      "Yetersiz stok!"
    ];

    if (knownErrors.includes(error.message)) {
      return res.status(400).json({
        success: false,
        message: error.message,
      });
    }

    return res.status(500).json({
      success: false,
      message: "Stok eklenemedi!",
    });
  }
};

exports.deleteInventoryItem = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const itemId = req.params.id;

    await deleteInventoryItemDB(itemId, tenantId);

    return res.status(200).json({
      success: true,
      message: "Envantner Silindi!",
    });
  } catch (error) {
    console.error(error);
    return res.status(500).json({
      success: false,
      message: "Envantner Silinemedi!",
    });
  }
};

exports.getInventoryLogs = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;

    const from = req.query.from || null;
    const to = req.query.to || null;
    const type = req.query.type;
    const movementType = req.query.movementType || null;

    if(!type){
      return res.status(400).json({
        success: false,
        message: "Lütfen tarih türünü belirtiniz!",
      });
    }

    if (type == "custom") {
      if (!(from && to)) {
        return res.status(400).json({
          success: false,
          message: "Lütfen tarih aralığını belirtiniz!",
        });
      }
    }

    const logs = await getInventoryLogsDB(movementType, type, from, to, tenantId);
    return res.status(200).json(logs);
  } catch (error) {
    console.error(error);
    return res.status(500).json({
      success: false,
      message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin.",
    });
  }
};

exports.getInventoryDashboardData = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;

    const from = req.query.from || null;
    const to = req.query.to || null;
    const type = req.query.type;

    if (!type) {
      return res.status(400).json({
        success: false,
        message: "Lütfen tarih türünü belirtiniz!",
      });
    }

    if (type == "custom") {
      if (!(from && to)) {
        return res.status(400).json({
          success: false,
          message: "Lütfen tarih aralığını belirtiniz!",
        });
      }
    }

    const [cummulativeInventoryMovements, inventoryUsageVSCurrentStock] = await Promise.all([
      getCummulativeInventoryMovementsDB(type, from, to, tenantId),
      getInventoryUsageVsCurrentStockDB(type, from, to, tenantId),
    ]);

    return res.status(200).json({
      cummulativeInventoryMovements,
      inventoryUsageVSCurrentStock,
    });
  } catch (error) {
    console.error(error);
    return res.status(500).json({
      success: false,
      message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin.",
    });
  }
};
