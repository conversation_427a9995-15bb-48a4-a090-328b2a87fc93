const {
  generateApiKeyDB,
  getApiKeysDB,
  deleteApiKeyDB,
  regenerateApiKeyDB
} = require("../services/mtsm-api-keys.service");

/**
 * GET /api/v1/mtsm/api-keys
 * Tenant'ın API key'lerini listele
 */
exports.getApiKeys = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;

    const apiKeys = await getApiKeysDB(tenantId);

    res.status(200).json({
      success: true,
      data: apiKeys
    });

  } catch (error) {
    console.error('getApiKeys error:', error);
    res.status(500).json({
      success: false,
      message: 'API key\'ler alınamadı'
    });
  }
};

/**
 * POST /api/v1/mtsm/api-keys
 * Yeni API key oluştur
 */
exports.generateApiKey = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const { name, description } = req.body;

    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'API key adı gerekli'
      });
    }

    const apiKey = await generateApiKeyDB(tenantId, name, description);

    res.status(201).json({
      success: true,
      message: 'API key oluşturuldu',
      data: apiKey
    });

  } catch (error) {
    console.error('generateApiKey error:', error);
    res.status(500).json({
      success: false,
      message: 'API key oluşturulamadı'
    });
  }
};

/**
 * PUT /api/v1/mtsm/api-keys/:id/regenerate
 * API key'i yeniden oluştur
 */
exports.regenerateApiKey = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const { id } = req.params;

    const newApiKey = await regenerateApiKeyDB(tenantId, id);

    if (!newApiKey) {
      return res.status(404).json({
        success: false,
        message: 'API key bulunamadı'
      });
    }

    res.status(200).json({
      success: true,
      message: 'API key yenilendi',
      data: newApiKey
    });

  } catch (error) {
    console.error('regenerateApiKey error:', error);
    res.status(500).json({
      success: false,
      message: 'API key yenilenemedi'
    });
  }
};

/**
 * DELETE /api/v1/mtsm/api-keys/:id
 * API key'i sil
 */
exports.deleteApiKey = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const { id } = req.params;

    const deleted = await deleteApiKeyDB(tenantId, id);

    if (!deleted) {
      return res.status(404).json({
        success: false,
        message: 'API key bulunamadı'
      });
    }

    res.status(200).json({
      success: true,
      message: 'API key silindi'
    });

  } catch (error) {
    console.error('deleteApiKey error:', error);
    res.status(500).json({
      success: false,
      message: 'API key silinemedi'
    });
  }
};
