const { getAllOrderDetailsDB } = require('../services/order-details.service');

/**
 * Tüm sipari<PERSON>, sipariş kalemlerini ve ödemeleri detaylı bir şekilde getirir
 */
exports.getAllOrderDetails = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;

    // Filtreleme seçeneklerini al
    const filters = {
      type: req.query.type || 'today', // Varsayılan olarak bugün
      startDate: req.query.startDate,
      endDate: req.query.endDate || req.query.startDate || new Date().toISOString().split('T')[0], // Bitiş tarihi belirtilmemişse başlangıç tarihi veya bugün
      status: req.query.status,
      paymentStatus: req.query.paymentStatus,
      tableId: req.query.tableId ? parseInt(req.query.tableId) : null,
      customerId: req.query.customerId,
      floorId: req.query.floorId ? parseInt(req.query.floorId) : null
    };

    // Tarih kontrolü - custom filtre türü için
    if (filters.type === 'custom' && !filters.startDate) {
      // Başlangıç tarihi belirtilmemişse, son 30 günü varsayılan olarak al
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      filters.startDate = thirtyDaysAgo.toISOString().split('T')[0];
    }

    console.log("Filters:", filters);

    // Tüm sipariş detaylarını getir
    const orderDetails = await getAllOrderDetailsDB(filters, tenantId);

    // Sipariş sayısı
    const totalOrders = orderDetails.orders.length;

    // Toplam tutar hesapla
    let totalAmount = 0;
    let totalTax = 0;
    let totalPaid = 0;

    // Her sipariş için toplam tutarı hesapla
    orderDetails.orders.forEach(order => {
      // Bu siparişe ait kalemler
      const items = orderDetails.orderItems.filter(item => item.order_id === order.id);

      // Kalem tutarlarını topla
      let orderTotal = 0;
      let orderTax = 0;
      let cancelledTotal = 0;
      let wasteTotal = 0;
      let complimentaryTotal = 0;

      items.forEach(item => {
        const itemTotal = item.order_item_price * item.quantity;

        // İptal edilen, zayi edilen veya ikram edilen ürünleri toplam tutardan düş
        if (item.status === 'cancelled') {
          cancelledTotal += itemTotal;
        } else if (item.status === 'waste') {
          wasteTotal += itemTotal;
        } else if (item.status === 'complimentary') {
          complimentaryTotal += itemTotal;
        } else {
          // Normal ürünleri toplam tutara ekle
          orderTotal += itemTotal;

        }
      });

      // İptal edilen, zayi edilen ve ikram edilen ürünlerin toplam tutarlarını sipariş nesnesine ekle
      order.cancelledTotal = parseFloat(cancelledTotal.toFixed(2));
      order.wasteTotal = parseFloat(wasteTotal.toFixed(2));
      order.complimentaryTotal = parseFloat(complimentaryTotal.toFixed(2));

      // İndirimleri uygula
      const orderDiscounts = orderDetails.discounts.filter(discount => discount.order_id === order.id);
      orderDiscounts.forEach(discount => {
        if (discount.discount_type === 'amount') {
          orderTotal -= discount.discount_value;
        } else if (discount.discount_type === 'percentage') {
          orderTotal -= (orderTotal * (discount.discount_value / 100));
        }
      });

      // Bu siparişe ait ödemeler
      const orderPayments = orderDetails.payments.filter(payment => payment.order_id === order.id);
      let orderPaid = 0;
      orderPayments.forEach(payment => {
        orderPaid += parseFloat(payment.amount);
      });


      // Toplam indirim tutarını hesapla
      let totalDiscount = 0;
      orderDiscounts.forEach(discount => {
        if (discount.discount_type === 'amount') {
          totalDiscount += parseFloat(discount.discount_value);
        } else if (discount.discount_type === 'percentage') {
          totalDiscount += (orderTotal * (parseFloat(discount.discount_value) / 100));
        }
      });

      // İndirim tutarını sipariş nesnesine ekle
      order.discountTotal = parseFloat(totalDiscount.toFixed(2));

      // Sipariş toplamını ekle
      totalAmount += orderTotal;
      totalTax += orderTax;
      totalPaid += orderPaid;

      // Sipariş nesnesine toplam tutarı ve ödenen tutarı ekle
      order.calculatedTotal = parseFloat(orderTotal.toFixed(2));
      order.calculatedTax = parseFloat(orderTax.toFixed(2));
      order.calculatedPaid = parseFloat(orderPaid.toFixed(2));
      order.calculatedRemaining = parseFloat((orderTotal - orderPaid).toFixed(2));

      // Brüt toplam (iptal, zayi, ikram ve indirimler dahil)
      order.grossTotal = parseFloat((orderTotal + cancelledTotal + wasteTotal + complimentaryTotal).toFixed(2));

      // Net toplam (indirimler düşülmüş)
      order.netTotal = parseFloat((orderTotal - totalDiscount).toFixed(2));
    });

    // Toplam iptal, zayi, ikram ve indirim tutarlarını hesapla
    let totalCancelled = 0;
    let totalWaste = 0;
    let totalComplimentary = 0;
    let totalDiscount = 0;
    let grossTotal = 0;

    orderDetails.orders.forEach(order => {
      totalCancelled += parseFloat(order.cancelledTotal || 0);
      totalWaste += parseFloat(order.wasteTotal || 0);
      totalComplimentary += parseFloat(order.complimentaryTotal || 0);
      totalDiscount += parseFloat(order.discountTotal || 0);
      grossTotal += parseFloat(order.grossTotal || 0);
    });

    // Özet bilgileri ekle
    const summary = {
      totalOrders,
      totalAmount: parseFloat(totalAmount.toFixed(2)),
      totalTax: parseFloat(totalTax.toFixed(2)),
      totalPaid: parseFloat(totalPaid.toFixed(2)),
      totalRemaining: parseFloat((totalAmount - totalPaid).toFixed(2)),
      totalCancelled: parseFloat(totalCancelled.toFixed(2)),
      totalWaste: parseFloat(totalWaste.toFixed(2)),
      totalComplimentary: parseFloat(totalComplimentary.toFixed(2)),
      totalDiscount: parseFloat(totalDiscount.toFixed(2)),
      grossTotal: parseFloat(grossTotal.toFixed(2)),
      netTotal: parseFloat((totalAmount - totalDiscount).toFixed(2)),
      period: {
        type: filters.type,
        startDate: filters.startDate,
        endDate: filters.endDate
      }
    };

    return res.status(200).json({
      success: true,
      summary,
      data: orderDetails
    });
  } catch (error) {
    console.error("getAllOrderDetails error:", error);
    return res.status(500).json({
      success: false,
      message: "Sipariş detayları getirilirken bir hata oluştu",
      error: error.message
    });
  }
};
