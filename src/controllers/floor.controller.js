const { 
  getFloorsDB, 
  getFloorByIdDB, 
  addFloorDB, 
  updateFloorDB, 
  deleteFloorDB 
} = require("../services/floor.service");

/**
 * Tüm katları getirir
 */
exports.getFloors = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const floors = await getFloorsDB(tenantId);
    
    return res.status(200).json({
      success: true,
      data: floors
    });
  } catch (error) {
    console.error("Error in getFloors:", error);
    return res.status(500).json({
      success: false,
      message: "Katlar getirilirken bir hata oluştu."
    });
  }
};

/**
 * ID'ye göre kat getirir
 */
exports.getFloorById = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const { id } = req.params;
    
    if (!id) {
      return res.status(400).json({
        success: false,
        message: "Kat ID gereklidir."
      });
    }
    
    const floor = await getFloorByIdDB(id, tenantId);
    
    if (!floor) {
      return res.status(404).json({
        success: false,
        message: "Kat bulunamadı."
      });
    }
    
    return res.status(200).json({
      success: true,
      data: floor
    });
  } catch (error) {
    console.error("Error in getFloorById:", error);
    return res.status(500).json({
      success: false,
      message: "Kat getirilirken bir hata oluştu."
    });
  }
};

/**
 * Yeni kat ekler
 */
exports.addFloor = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const { title, description, is_active, waiter_username } = req.body;
    
    if (!title) {
      return res.status(400).json({
        success: false,
        message: "Kat başlığı gereklidir."
      });
    }
    
    const floorId = await addFloorDB(
      title, 
      description || null, 
      is_active !== undefined ? is_active : true, 
      waiter_username || null, 
      tenantId
    );
    
    return res.status(201).json({
      success: true,
      message: "Kat başarıyla eklendi.",
      data: { id: floorId }
    });
  } catch (error) {
    console.error("Error in addFloor:", error);
    return res.status(500).json({
      success: false,
      message: "Kat eklenirken bir hata oluştu."
    });
  }
};

/**
 * Kat bilgilerini günceller
 */
exports.updateFloor = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const { id } = req.params;
    const { title, description, is_active, waiter_username } = req.body;
    
    if (!id) {
      return res.status(400).json({
        success: false,
        message: "Kat ID gereklidir."
      });
    }
    
    if (!title) {
      return res.status(400).json({
        success: false,
        message: "Kat başlığı gereklidir."
      });
    }
    
    // Önce katın var olup olmadığını kontrol et
    const floor = await getFloorByIdDB(id, tenantId);
    
    if (!floor) {
      return res.status(404).json({
        success: false,
        message: "Kat bulunamadı."
      });
    }
    
    const success = await updateFloorDB(
      id, 
      title, 
      description !== undefined ? description : floor.description, 
      is_active !== undefined ? is_active : floor.is_active, 
      waiter_username !== undefined ? waiter_username : floor.waiter_username, 
      tenantId
    );
    
    if (!success) {
      return res.status(400).json({
        success: false,
        message: "Kat güncellenemedi."
      });
    }
    
    return res.status(200).json({
      success: true,
      message: "Kat başarıyla güncellendi."
    });
  } catch (error) {
    console.error("Error in updateFloor:", error);
    return res.status(500).json({
      success: false,
      message: "Kat güncellenirken bir hata oluştu."
    });
  }
};

/**
 * Kat siler
 */
exports.deleteFloor = async (req, res) => {
  try {
    const tenantId = req.user.tenant_id;
    const { id } = req.params;
    
    if (!id) {
      return res.status(400).json({
        success: false,
        message: "Kat ID gereklidir."
      });
    }
    
    // Önce katın var olup olmadığını kontrol et
    const floor = await getFloorByIdDB(id, tenantId);
    
    if (!floor) {
      return res.status(404).json({
        success: false,
        message: "Kat bulunamadı."
      });
    }
    
    try {
      const success = await deleteFloorDB(id, tenantId);
      
      if (!success) {
        return res.status(400).json({
          success: false,
          message: "Kat silinemedi."
        });
      }
      
      return res.status(200).json({
        success: true,
        message: "Kat başarıyla silindi."
      });
    } catch (error) {
      if (error.message.includes("Bu kata bağlı masalar var")) {
        return res.status(400).json({
          success: false,
          message: error.message
        });
      }
      throw error;
    }
  } catch (error) {
    console.error("Error in deleteFloor:", error);
    return res.status(500).json({
      success: false,
      message: "Kat silinirken bir hata oluştu."
    });
  }
};
