const { collectOrderPointsDB } = require("../services/points.service");
const { decrypt } = require("../config/crypto");


/**
 * Sipariş ID'sine göre puanları toplar ve döndürür
 */
exports.collectOrderPoints = async (req, res) => {
  try {
    const { orderId: encryptedOrderId } = req.body;
    if (!encryptedOrderId) {
      return res.status(400).json({
        success: false,
        message: "Sipariş ID gereklidir"
      });
    }

    // 1️⃣ Şifreyi çöz
    let orderId;
    try {
      orderId = decrypt(encryptedOrderId);
    } catch (e) {
      return res.status(400).json({
        success: false,
        message: "Geçersiz veya bozuk QR kod"
      });
    }

    // 2️⃣ gerçek ID ile DB fonksiyonunu çağır
    const result = await collectOrderPointsDB(orderId);
    if (result.status === "failed") {
      return res.status(200).json({
        success: false,
        message: result.message
      });
    }

    // 3️⃣ Sonucu dön
    return res.status(200).json({
      success: true,
      message: result.message,
      data: result
    });
  } catch (error) {
    console.error(error);
    return res.status(500).json({
      success: false,
      message: "Sunucu hatası"
    });
  }
};
