require("dotenv").config({});

const express = require("express");
const morgan = require("morgan");
const {rateLimit} = require("express-rate-limit");
const cors = require("cors");
// const path = require("path")
// const rfs = require("rotating-file-stream");
const cookieParser = require("cookie-parser");
const userAgent = require('express-useragent');
const { CONFIG } = require("./config");
const fileUpload = require("express-fileupload")
const path = require("path")

// routes import
const authRoutes = require("./routes/auth.routes");
const settingsRoutes = require("./routes/settings.routes");
const customerRoutes = require("./routes/customer.routes");
const reservationRoutes = require("./routes/reservation.routes");
const userRoutes = require("./routes/user.routes");
const menuItemRoutes = require("./routes/menu_item.routes");
const posRoutes = require("./routes/pos.routes");
const kitchenRoutes = require("./routes/kitchen.routes")
const ordersRoutes = require("./routes/orders.routes")
const invoiceRoutes = require("./routes/invoice.routes")
const dashboardRoutes = require("./routes/dashboard.routes")
const reportsRoutes = require("./routes/reports.routes")
const qrMenuRoutes = require("./routes/qrmenu.routes")
const superAdminRoutes = require("./routes/superadmin.routes")
const PrintData = require("./routes/print.routes")
const Pin = require("./routes/pin.routes")
const callerRoutes = require("./routes/caller.routes");
const mobileRoutes = require("./routes/mobile.routes");
const deliveryRoutes = require("./routes/delivery.routes");
const printersRoutes = require("./routes/printers.routes");
const campaingRoutes = require("./routes/campaings.routes");
const translationRoutes = require("./routes/trasnlation.routes");
const translationsPublicRoutes = require("./routes/translations.public.route");
const inventoryRoutes = require("./routes/inventory.routes");
const supplierRoutes = require("./routes/supplier.routes");
const pointsRoutes = require("./routes/points.routes");
const floorRoutes = require("./routes/floor.routes");
const qrPaymentRoutes = require("./routes/qr-payment.routes");
const orderDetailsRoutes = require("./routes/order-details.routes");
const floorPrinterMappings  = require ("./routes/floorPrinterMappings.route");
const cashRegisterRoutes = require("./routes/cash-register.routes");
const reasonRoutes = require("./routes/reason.routes");
const unpaidOrdersRoutes = require("./routes/unpaid_orders.routes");
const businessHoursRoutes = require("./routes/business-hours.routes");
const storageLocationRoutes = require("./routes/storage-location.routes");
const qrTemplateRoutes = require("./routes/qr_template.routes");


const app = express();


var corsWhitelist = [
  CONFIG.FRONTEND_DOMAIN,
  CONFIG.FRONTEND_DOMAINTWO,
  CONFIG.FRONTEND_DOMAIN3,
  CONFIG.FRONTEND_DOMAIN4,

];

var corsOptions = {
  credentials: true,
  origin: function (origin, callback) {
    if (corsWhitelist.indexOf(origin) !== -1 || !origin) {
      callback(null, true);
    } else {
      callback(new Error("Not allowed by CORS"));
    }
  },
};
app.use(express.json({ limit: "2mb" })); // JSON boyut sınırı
app.use(express.urlencoded({ limit: "2mb", extended: true }));
app.use(cors(corsOptions));
app.use(cookieParser());
app.use(userAgent.express());
app.use('/api/v1/delivery/webhook', express.raw({ type: 'application/json' }));
app.use('/api/v1/auth/stripe-webhook', express.raw({ type: 'application/json' }));
app.use(express.json());
// app.use(morgan("combined", {stream: accessLogStream}));
app.use(fileUpload({
  preserveExtension: true,
  safeFileNames: true,
  useTempFiles: true,
  tempFileDir: path.join(__dirname, "../tmp")
}))
app.use("/public", express.static(path.join(__dirname, "../public")))
app.use(morgan("tiny"));
// app.use(limiter);
/**
 * Middlewares
 * */


// routes
app.use("/api/v1/auth", authRoutes);
app.use("/api/v1/settings", settingsRoutes);
app.use("/api/v1/printers", printersRoutes);
app.use("/api/v1/customers", customerRoutes);
app.use("/api/v1/reservations", reservationRoutes);
app.use("/api/v1/users", userRoutes);
app.use("/api/v1/menu-items", menuItemRoutes);
app.use("/api/v1/pos", posRoutes);
app.use("/api/v1/kitchen", kitchenRoutes);
app.use("/api/v1/orders", ordersRoutes);
app.use("/api/v1/invoices", invoiceRoutes);
app.use("/api/v1/dashboard", dashboardRoutes);
app.use("/api/v1/reports", reportsRoutes);
app.use("/api/v1/qrmenu", qrMenuRoutes);
app.use("/api/v1/superadmin", superAdminRoutes);
app.use("/api/v1/print", PrintData);
app.use("/api/v1/pin", Pin);
app.use("/api/v1/caller", callerRoutes);
app.use("/api/v1/campaigns", campaingRoutes);
app.use("/api/v1/mobile", mobileRoutes);
app.use("/api/v1/delivery", deliveryRoutes);
app.use("/api/v1/translation", translationRoutes);
app.use("/api/translations", translationsPublicRoutes);
app.use("/api/v1/inventory", inventoryRoutes);
app.use("/api/v1/suppliers", supplierRoutes);
app.use("/api/v1/points", pointsRoutes);
app.use("/api/v1/floors", floorRoutes);
app.use("/api/v1/qr-payment", qrPaymentRoutes);
app.use("/api/v1/order-details", orderDetailsRoutes);
app.use("/api/v1/printer-mapping", floorPrinterMappings);
app.use("/api/v1/cash-register", cashRegisterRoutes);
app.use("/api/v1/reasons", reasonRoutes);
app.use("/api/v1/unpaid-orders", unpaidOrdersRoutes);
app.use("/api/v1/business-hours", businessHoursRoutes);
app.use("/api/v1/storage-locations", storageLocationRoutes);
app.use("/api/v1/qr-templates", qrTemplateRoutes);

app.get("/", (req, res)=>{
  res.send("⚡️");
});


module.exports = app;