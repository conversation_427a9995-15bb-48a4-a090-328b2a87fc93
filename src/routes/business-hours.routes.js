const { Router } = require("express");
const {
  isLoggedIn,
  isAuthenticated,
  authorize,
  isSubscriptionActive,
} = require("../middlewares/auth.middleware");
const { SCOPES } = require("../config/user.config");
const {
  getBusinessHours,
  updateBusinessHours,
  deleteBusinessHours,
  checkBusinessHours
} = require("../controllers/business-hours.controller");

const router = Router();

/**
 * Çalışma saatlerini getir
 */
router.get(
  "/",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.AYARLAR]),
  getBusinessHours
);

/**
 * Çalışma saatlerini güncelle
 */
router.put(
  "/",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.AYARLAR]),
  updateBusinessHours
);

/**
 * Çalışma saatlerini sil
 */
router.delete(
  "/",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.AYARLAR]),
  deleteBusinessHours
);

/**
 * <PERSON>u anki saatin çalışma saatleri içinde olup olmadığını kontrol et
 */
router.get(
  "/check",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.POS, SCOPES.ORDERS, SCOPES.AYARLAR]),
  checkBusinessHours
);

module.exports = router;
