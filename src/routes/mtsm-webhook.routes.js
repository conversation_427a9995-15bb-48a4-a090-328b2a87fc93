const { Router } = require("express");
const {
  listOrders,
  getOrderDetails,
  updateOrderStatus,
  updateOrderPayment
} = require("../controllers/mtsm-webhook.controller");
const { deviceSerialAuth } = require("../middlewares/mtsm-auth.middleware");

const router = Router();

// GET - Siparişleri Listeleme (Yazarkasa Device Serial ile)
// Query params: csn (cihaz sicil no - zorunlu), pageNumber, pageSize, no (sipariş no), etc.
router.get("/orders", deviceSerialAuth, listOrders);

// GET - Sipariş Detaylarını Alma (Yazarkasa Device Serial ile)
// Query params: csn (cihaz sicil no - zorunlu), id (sipariş ID)
router.get("/orders/details", deviceSerialAuth, getOrderDetails);

// POST - Sipariş Durum Güncelleme (Yazarkasa Device Serial ile)
// Query params: csn (cihaz sicil no - zorunlu)
// Body: { id, status, payments?, receipt?, deviceDocument? }
router.post("/orders/status", deviceSerialAuth, updateOrderStatus);

// POST - Sipariş Ödeme (Yazarkasa Device Serial ile)
// Query params: csn (cihaz sicil no - zorunlu)
// Body: { id, payments }
router.post("/orders/payment", deviceSerialAuth, updateOrderPayment);

module.exports = router;
