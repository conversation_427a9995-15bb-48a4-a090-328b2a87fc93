const { Router } = require("express");
const {
  isLoggedIn,
  isAuthenticated,
  authorize,
  isSubscriptionActive,
} = require("../middlewares/auth.middleware");
const { SCOPES } = require("../config/user.config");
const { getAllOrderDetails } = require("../controllers/order-details.controller");

const router = Router();

// Tüm sipariş detaylarını getir
router.get(
  "/",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.REPORTS, SCOPES.ORDERS, SCOPES.POS]),
  getAllOrderDetails
);

module.exports = router;
