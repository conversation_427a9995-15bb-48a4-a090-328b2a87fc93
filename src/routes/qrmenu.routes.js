const { Router } = require("express");
const {
  getQRMenuInit,
  placeOrderViaQrMenu,
  getDynamicRecommendations,
  getRecommendationsForUser,
  addFeedback,
  deleteFeedback,
  getQROrderStatus,
} = require("../controllers/qrmenu.controller");

const router = Router();

// QR menü başlangıç
router.get("/:qrcode", getQRMenuInit);

// Sipariş oluşturma
router.post("/:qrcode/place-order", placeOrderViaQrMenu);

// Sipariş durumu sorgulama
router.get("/:qrcode/order-status/:orderId", getQROrderStatus);

// İlk öneriler ve sorular
router.get("/:qrcode/dynamic-recommendations", getDynamicRecommendations);

// Müşteri yanıtlarına göre öneriler
router.post("/:qrcode/recommendations", getRecommendationsForUser);

// Feedback ekleme
router.post("/:qrcode/feedback", addFeedback);

// Feedback silme
router.delete("/:qrcode/feedback/:feedbackId", deleteFeedback);

module.exports = router;
