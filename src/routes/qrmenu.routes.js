const { Router } = require("express");
const {
  getQRMenuInit,
  placeOrderViaQrMenu,
  getDynamicRecommendations,
  getRecommendationsForUser,
  addFeedback,
  deleteFeedback,
  getTenantByDomain,
  getQRMenuByTenantId,
  placeOrderByTenantId,
} = require("../controllers/qrmenu.controller");

const router = Router();

// QR menü başlangıç
router.get("/:qrcode", getQRMenuInit);

// Sipariş oluşturma
router.post("/:qrcode/place-order", placeOrderViaQrMenu);


// İlk öneriler ve sorular
router.get("/:qrcode/dynamic-recommendations", getDynamicRecommendations);

// Müşteri yanıtlarına göre öneriler
router.post("/:qrcode/recommendations", getRecommendationsForUser);

// Feedback ekleme
router.post("/:qrcode/feedback", addFeedback);

// Feedback silme
router.delete("/:qrcode/feedback/:feedbackId", deleteFeedback);

// Domain'den tenant ID bulma (Frontend için)
router.post("/domain/check", getTenantByDomain);

// Tenant ID ile menü getirme (QR menü ile aynı veriyi döndürür)
router.get("/tenant/:id", getQRMenuByTenantId);

// Tenant ID ile sipariş verme (QR menü ile aynı işlevi yapar)
router.post("/tenant/:id/place-order", placeOrderByTenantId);

module.exports = router;
