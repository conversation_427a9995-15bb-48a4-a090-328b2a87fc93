const { Router } = require("express");
const {
  isLoggedIn,
  isAuthenticated,
  authorize,
  isSubscriptionActive,
} = require("../middlewares/auth.middleware");
const { SCOPES } = require("../config/user.config");
const {
  addPrinter,
  getPrinters,
  updatePrinter,
  deletePrinter,
} = require("../controllers/printers.controller");

const router = Router();

router.get("/", 
    isLoggedIn,
    isAuthenticated,
    isSubscriptionActive,
    authorize([SCOPES.POS]), 
    getPrinters
  );

router.post("/add", 
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.POS]), 
  addPrinter
);

router.put("/update/:printerId", 
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.POS]), 
  updatePrinter
);

router.delete("/delete/:printerId", 
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.POS]), 
  deletePrinter
);

module.exports = router;