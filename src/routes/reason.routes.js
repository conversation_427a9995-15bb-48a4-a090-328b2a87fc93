const { Router } = require("express");
const {
  isLoggedIn,
  isAuthenticated,
  authorize,
  isSubscriptionActive,
} = require("../middlewares/auth.middleware");
const { SCOPES } = require("../config/user.config");
const {
  getCancellationReasons,
  getCancellationReasonById,
  addCancellationReason,
  updateCancellationReason,
  deleteCancellationReason,
  getComplimentaryReasons,
  getComplimentaryReasonById,
  addComplimentaryReason,
  updateComplimentaryReason,
  deleteComplimentaryReason,
  getWasteReasons,
  getWasteReasonById,
  addWasteReason,
  updateWasteReason,
  deleteWasteReason
} = require("../controllers/reason.controller");

const router = Router();

// Cancellation Reasons Routes
router.get(
  "/cancellation",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.KASIYER, SCOPES.SIPARIS_IPTAL]),
  getCancellationReasons
);

router.get(
  "/cancellation/:id",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.KASIYER, SCOPES.SIPARIS_IPTAL]),
  getCancellationReasonById
);

router.post(
  "/cancellation",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.KASIYER, SCOPES.SIPARIS_IPTAL]),
  addCancellationReason
);

router.put(
  "/cancellation/:id",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.KASIYER, SCOPES.SIPARIS_IPTAL]),
  updateCancellationReason
);

router.delete(
  "/cancellation/:id",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.KASIYER, SCOPES.SIPARIS_IPTAL]),
  deleteCancellationReason
);

// Complimentary Reasons Routes
router.get(
  "/complimentary",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.KASIYER]),
  getComplimentaryReasons
);

router.get(
  "/complimentary/:id",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.KASIYER]),
  getComplimentaryReasonById
);

router.post(
  "/complimentary",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.KASIYER]),
  addComplimentaryReason
);

router.put(
  "/complimentary/:id",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.KASIYER]),
  updateComplimentaryReason
);

router.delete(
  "/complimentary/:id",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.KASIYER]),
  deleteComplimentaryReason
);

// Waste Reasons Routes
router.get(
  "/waste",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.KASIYER]),
  getWasteReasons
);

router.get(
  "/waste/:id",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.KASIYER]),
  getWasteReasonById
);

router.post(
  "/waste",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.KASIYER]),
  addWasteReason
);

router.put(
  "/waste/:id",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.KASIYER]),
  updateWasteReason
);

router.delete(
  "/waste/:id",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.KASIYER]),
  deleteWasteReason
);

module.exports = router;
