const { Router } = require("express");

const {
  isLoggedIn,
  isAuthenticated,
  authorize,
  isSubscriptionActive,
} = require("../middlewares/auth.middleware");
const { SCOPES } = require("../config/user.config");
const {
  getMTSMSettings,
  saveMTSMSettings,
  testMTSMConnection,
  getMTSMMatchList,
  getMTSMStatus
} = require("../controllers/mtsm.controller");

const {
  getApiKeys,
  generateApiKey,
  regenerateApiKey,
  deleteApiKey
} = require("../controllers/mtsm-api-keys.controller");

const router = Router();

// mTSM Entegrasyon Durumu
router.get(
  "/status",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.SETTINGS]),
  getMTSMStatus
);

// mTSM Ayarlarını Getir
router.get(
  "/settings",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.SETTINGS]),
  getMTSMSettings
);

// mTSM Ayarlarını Kaydet
router.post(
  "/settings",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.SETTINGS]),
  saveMTSMSettings
);

// mTSM Bağlantısını Test Et
router.post(
  "/test-connection",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.SETTINGS]),
  testMTSMConnection
);

// mTSM Eşleştirmeleri Getir
router.get(
  "/matches",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.SETTINGS]),
  getMTSMMatchList
);

// API Key Yönetimi
router.get(
  "/api-keys",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.SETTINGS]),
  getApiKeys
);

router.post(
  "/api-keys",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.SETTINGS]),
  generateApiKey
);

router.put(
  "/api-keys/:id/regenerate",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.SETTINGS]),
  regenerateApiKey
);

router.delete(
  "/api-keys/:id",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.SETTINGS]),
  deleteApiKey
);

module.exports = router;
