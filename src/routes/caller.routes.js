const { Router } = require("express");

const {
    isLoggedIn,
    isAuthenticated,
    authorize,
    isSubscriptionActive,
  } = require("../middlewares/auth.middleware");
  const { SCOPES } = require("../config/user.config");
const { saveCall, getCalls } = require("../controllers/caller.controller");

const router = Router();

router.post(
  "/:tenantId/calls",
  saveCall
);

router.get(
  "/calls",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.DASHBOARD]),
  getCalls
);

module.exports = router;