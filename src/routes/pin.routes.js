const { Router } = require("express");
const {
  isLoggedIn,
  isAuthenticated,
  authorize,
  isSubscriptionActive,
} = require("../middlewares/auth.middleware");
const { SCOPES } = require("../config/user.config");

const { setPin, verifyPin } = require("../controllers/pin.controller");

const router = Router();

router.post(
  "/set-pin",
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.POS]),
  setPin
);

router.post(
  "/verify-pin",
  verifyPin
);

module.exports = router;