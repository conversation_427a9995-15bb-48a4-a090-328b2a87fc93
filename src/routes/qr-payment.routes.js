const { Router } = require("express");

const {
  isLoggedIn,
  isAuthenticated,
  authorize,
  isSubscriptionActive,
} = require("../middlewares/auth.middleware");
const { SCOPES } = require("../config/user.config");
const { processQRPayment } = require("../controllers/qr-payment.controller");

const router = Router();

// JSON parse middleware
const jsonParser = require('express').json();

// QR kod ile ödeme işlemi
router.post(
  "/process",
  jsonParser, // JSON parse middleware'i ekle
  (req, res, next) => {
    // İsteği logla
    console.log('Raw body:', req.body);
    next();
  },
  processQRPayment
);

module.exports = router;
