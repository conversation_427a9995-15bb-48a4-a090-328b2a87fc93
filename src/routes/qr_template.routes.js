const { Router } = require("express");
const {
    getAllQRTemplates,
    getQRTemplateById,
    getTenantQRTemplate,
    setTenantQRTemplate,
    createQRTemplate,
    updateQRTemplate,
    deleteQRTemplate
} = require("../controllers/qr_template.controller");

const {
    isLoggedIn,
    isAuthenticated,
    authorize,
    isSubscriptionActive,
  } = require("../middlewares/auth.middleware")
  const { SCOPES } = require("../config/user.config");
  

const router = Router();

// Tüm şablonları listele (public - QR menü için)


router.get("/", getAllQRTemplates);

// Belirli şablon detayı (public)
router.get("/:id", getQRTemplateById);

// Tenant'ın seçili şablonunu getir (auth gerekli)
router.get("/tenant/selected", isLoggedIn, isAuthenticated, isSubscriptionActive, getTenantQRTemplate);

// Tenant için şablon seç (auth gerekli)
router.post("/tenant/select", isLoggedIn, isAuthenticated, isSubscriptionActive, setTenantQRTemplate);

// Admin işlemleri (auth gerekli)
router.post("/admin/create", isLoggedIn, isAuthenticated, isSubscriptionActive, authorize([SCOPES.DASHBOARD]), createQRTemplate);
router.put("/admin/:id", isLoggedIn, isAuthenticated, isSubscriptionActive, authorize([SCOPES.DASHBOARD]), updateQRTemplate);
router.delete("/admin/:id", isLoggedIn, isAuthenticated, isSubscriptionActive, authorize([SCOPES.DASHBOARD]), deleteQRTemplate);

module.exports = router;
