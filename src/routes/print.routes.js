const { Router } = require("express");
const {
  isLoggedIn,
  isAuthenticated,
  authorize,
  isSubscriptionActive,
} = require("../middlewares/auth.middleware");
const { SCOPES } = require("../config/user.config");
const { savePrintDataRecipet, savePrintDataKitchen, getPrintData, updatePrintStatus, savePrintDataReport } = require("../controllers/print_data.controller");

const router = Router();

router.post("/saverecipet", 
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.POS]), 
  savePrintDataRecipet
);

router.post("/savekitchen", 
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.POS]), 
  savePrintDataKitchen
);

router.post("/savereport", 
  isLoggedIn,
  isAuthenticated,
  isSubscriptionActive,
  authorize([SCOPES.POS]), 
  savePrintDataReport
);

router.get("/get", getPrintData );

router.put("/update-status/:printId", updatePrintStatus );

module.exports = router;
