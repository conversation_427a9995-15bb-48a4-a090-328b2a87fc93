const { getMySqlPromiseConnection } = require("../config/mysql.db");

/**
 * mTSM Webhook API Key Auth Middleware
 * TSM'den gelen API Key ile tenant'ı belirler
 */

exports.apiKeyAuth = async (req, res, next) => {
  try {
    // Header'dan API key'i al (farklı header isimleri deneyebiliriz)
    const apiKey = req.headers['x-api-key'] ||
                   req.headers['api-key'] ||
                   req.headers['authorization']?.replace('Bearer ', '') ||
                   req.headers['x-mtsm-key'];

    if (!apiKey) {
      return res.status(401).json({
        success: false,
        message: 'API Key gerekli. Header: x-api-key, api-key, authorization veya x-mtsm-key'
      });
    }

    console.log(`🔑 mTSM API Key alındı: ${apiKey.substring(0, 8)}...`);

    // Veritabanından API key ile tenant'ı bul
    const tenant = await findTenantByApiKey(apiKey);

    if (!tenant) {
      return res.status(401).json({
        success: false,
        message: 'Geçersiz API Key'
      });
    }

    // Tenant bilgisini request'e ekle
    req.tenant = tenant;
    req.tenantId = tenant.id;

    console.log(`✅ mTSM Webhook Auth - Tenant: ${tenant.name} (ID: ${tenant.id})`);

    // API key kullanım zamanını güncelle (async, hata olsa bile devam et)
    updateApiKeyUsage(apiKey).catch(error => {
      console.error('API key usage update error:', error);
    });

    next();
  } catch (error) {
    console.error('mTSM API Key Auth Error:', error);
    return res.status(401).json({
      success: false,
      message: 'Authentication failed'
    });
  }
};

/**
 * API Key ile tenant bulma
 */
async function findTenantByApiKey(apiKey) {
  const conn = await getMySqlPromiseConnection();
  try {
    // mtsm_api_keys tablosundan API key'i bul
    const [keyRows] = await conn.query(`
      SELECT
        mak.tenant_id,
        mak.api_key,
        mak.is_active,
        mak.created_at,
        t.name as tenant_name
      FROM mtsm_api_keys mak
      JOIN tenants t ON mak.tenant_id = t.id
      WHERE mak.api_key = ? AND mak.is_active = 1
    `, [apiKey]);

    if (keyRows.length === 0) {
      console.log(`❌ API Key bulunamadı: ${apiKey.substring(0, 8)}...`);
      return null;
    }

    const keyData = keyRows[0];

    return {
      id: keyData.tenant_id,
      name: keyData.tenant_name,
      apiKey: keyData.api_key
    };

  } catch (error) {
    console.error('findTenantByApiKey error:', error);
    return null;
  } finally {
    conn.release();
  }
}

/**
 * API key kullanım zamanını güncelle
 */
async function updateApiKeyUsage(apiKey) {
  try {
    const { updateApiKeyUsageDB } = require("../services/mtsm-api-keys.service");
    await updateApiKeyUsageDB(apiKey);
  } catch (error) {
    console.error('updateApiKeyUsage error:', error);
  }
}

// Backward compatibility için basic auth'u da tut
exports.basicAuth = exports.apiKeyAuth;

/**
 * Yazarkasa Device Serial Auth Middleware
 * CSN (Cihaz Serial No) ile tenant'ı belirler
 */
exports.deviceSerialAuth = async (req, res, next) => {
  try {
    // Query params'tan CSN al
    const csn = req.query.csn || req.body.csn;

    if (!csn) {
      return res.status(400).json({
        success: false,
        message: 'CSN (Cihaz Serial No) gerekli'
      });
    }

    console.log(`🔑 Yazarkasa CSN alındı: ${csn}`);

    // Veritabanından CSN ile tenant'ı bul
    const tenant = await findTenantByDeviceSerial(csn);

    if (!tenant) {
      return res.status(404).json({
        success: false,
        message: 'Cihaz bulunamadı veya aktif değil'
      });
    }

    // Tenant bilgisini request'e ekle
    req.tenant = tenant;
    req.tenantId = tenant.id;
    req.deviceSerial = csn;

    console.log(`✅ Yazarkasa Device Auth - Tenant: ${tenant.name} (ID: ${tenant.id}), CSN: ${csn}`);

    next();
  } catch (error) {
    console.error('Yazarkasa Device Serial Auth Error:', error);
    return res.status(401).json({
      success: false,
      message: 'Authentication failed'
    });
  }
};

/**
 * Device Serial ile tenant bulma
 */
async function findTenantByDeviceSerial(deviceSerial) {
  const conn = await getMySqlPromiseConnection();
  try {
    // yazarkasa_devices tablosundan device serial ile tenant bul
    const [deviceRows] = await conn.query(`
      SELECT
        yd.tenant_id,
        yd.device_serial,
        yd.device_name,
        yd.is_active,
        t.name as tenant_name
      FROM yazarkasa_devices yd
      JOIN tenants t ON yd.tenant_id = t.id
      WHERE yd.device_serial = ? AND yd.is_active = 1
    `, [deviceSerial]);

    if (deviceRows.length === 0) {
      console.log(`❌ Cihaz bulunamadı: ${deviceSerial}`);
      return null;
    }

    const deviceData = deviceRows[0];

    return {
      id: deviceData.tenant_id,
      name: deviceData.tenant_name,
      deviceSerial: deviceData.device_serial,
      deviceName: deviceData.device_name
    };

  } catch (error) {
    console.error('findTenantByDeviceSerial error:', error);
    return null;
  } finally {
    conn.release();
  }
}
