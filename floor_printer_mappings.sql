-- Kat-<PERSON><PERSON><PERSON>-<PERSON>er <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>e tablosu
CREATE TABLE `floor_printer_mappings` (
  `id` int NOT NULL AUTO_INCREMENT,
  `floor_id` int NOT NULL,
  `category_id` int DEFAULT NULL,
  `printer_id` int NOT NULL,
  `printer_type` enum('kitchen','receipt') NOT NULL,
  `tenant_id` int NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `floor_category_printer_type_unique` (`floor_id`, `category_id`, `printer_type`, `tenant_id`),
  KEY `floor_id` (`floor_id`),
  KEY `category_id` (`category_id`),
  KEY `printer_id` (`printer_id`),
  KEY `tenant_id` (`tenant_id`),
  CONSTRAINT `floor_printer_mappings_ibfk_1` FOREIGN KEY (`floor_id`) REFERENCES `floor` (`id`) ON DELETE CASCADE,
  CONSTRAINT `floor_printer_mappings_ibfk_2` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE CASCADE,
  CONSTRAINT `floor_printer_mappings_ibfk_3` FOREIGN KEY (`printer_id`) REFERENCES `printers` (`id`) ON DELETE CASCADE,
  CONSTRAINT `floor_printer_mappings_ibfk_4` FOREIGN KEY (`tenant_id`) REFERENCES `tenants` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
