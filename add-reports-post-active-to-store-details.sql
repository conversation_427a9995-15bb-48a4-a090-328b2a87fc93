-- store_details tablosuna images_post_active kolonu ekleme

-- 1. images_post_active kolonu ekle (default false)
ALTER TABLE store_details
ADD COLUMN images_post_active TINYINT(1) DEFAULT 0 COMMENT 'Resimler POST aktif mi (0=false, 1=true)';

-- 2. Mevcut kayıtları kontrol et
SELECT
    'STORE DETAILS WITH IMAGES POST' as info,
    tenant_id,
    store_name,
    images_post_active,
    mali_mode_active
FROM store_details
ORDER BY tenant_id;

-- 3. Test için bir tenant'ı aktif yap (opsiyonel)
-- UPDATE store_details SET images_post_active = 1 WHERE tenant_id = 5;
