<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>mTSM Socket Test</title>
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .log { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        input { padding: 8px; margin: 5px; width: 200px; }
    </style>
</head>
<body>
    <h1>🧪 mTSM Socket Test</h1>
    
    <div>
        <label>Tenant ID:</label>
        <input type="number" id="tenantId" value="5" placeholder="Tenant ID">
        <button onclick="connectSocket()">🔌 Bağlan</button>
        <button onclick="disconnectSocket()">❌ Bağlantıyı Kes</button>
    </div>

    <div>
        <button onclick="testPaymentWebhook()">💳 Test Payment Webhook</button>
        <button onclick="clearLogs()">🗑️ Logları Temizle</button>
    </div>

    <div id="status">❌ Bağlı değil</div>
    <div id="logs"></div>

    <script>
        let socket = null;
        let tenantId = 5;

        function log(message, type = 'info') {
            const logs = document.getElementById('logs');
            const logDiv = document.createElement('div');
            logDiv.className = `log ${type}`;
            logDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong> - ${message}`;
            logs.insertBefore(logDiv, logs.firstChild);
        }

        function updateStatus(status, connected = false) {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = connected ? `✅ ${status}` : `❌ ${status}`;
        }

        function connectSocket() {
            tenantId = document.getElementById('tenantId').value;
            
            if (socket) {
                socket.disconnect();
            }

            socket = io('http://*************:3000');

            socket.on('connect', () => {
                log('Socket bağlantısı kuruldu', 'success');
                updateStatus(`Bağlandı (${socket.id})`, true);
                
                // Tenant'a authenticate ol
                socket.emit('authenticate', parseInt(tenantId));
                log(`Tenant ${tenantId} için authenticate edildi`, 'info');
            });

            socket.on('disconnect', () => {
                log('Socket bağlantısı kesildi', 'error');
                updateStatus('Bağlantı kesildi', false);
            });

            socket.on('order_update', (data) => {
                log(`📦 Order Update alındı: ${JSON.stringify(data, null, 2)}`, 'success');
                
                if (data.paymentType === 'mTSM') {
                    log(`🎉 mTSM Ödeme Bildirimi - Order: ${data.orderId}, Amount: ${data.amount} TL`, 'success');
                }
            });

            socket.on('new_order', (data) => {
                log(`🆕 New Order alındı: ${JSON.stringify(data, null, 2)}`, 'info');
            });

            socket.on('connect_error', (error) => {
                log(`Bağlantı hatası: ${error.message}`, 'error');
                updateStatus('Bağlantı hatası', false);
            });
        }

        function disconnectSocket() {
            if (socket) {
                socket.disconnect();
                socket = null;
            }
        }

        function testPaymentWebhook() {
            log('💳 Payment webhook test ediliyor...', 'info');
            
            fetch('http://*************:3000/api/v1/mtsm-webhook/orders/payment', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    "id": "49185",
                    "payments": [
                        {
                            "Type": "CashPayment",
                            "Amount": 200,
                            "Details": null
                        }
                    ],
                    "receipt": {
                        "No": "4",
                        "ZNo": "20",
                        "EruNo": "1",
                        "Date": "2025-09-30T19:02:19.452"
                    },
                    "csn": "T60008697517"
                })
            })
            .then(response => response.json())
            .then(data => {
                log(`Webhook response: ${JSON.stringify(data)}`, 'info');
            })
            .catch(error => {
                log(`Webhook error: ${error.message}`, 'error');
            });
        }

        function clearLogs() {
            document.getElementById('logs').innerHTML = '';
        }

        // Sayfa yüklendiğinde otomatik bağlan
        window.onload = () => {
            log('Socket test sayfası yüklendi', 'info');
        };
    </script>
</body>
</html>
