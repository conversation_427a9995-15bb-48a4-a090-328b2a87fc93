#!/bin/bash

# mTSM Socket ve Webhook Test Script

echo "🧪 mTSM Socket ve Webhook Test..."
echo "================================"

# 1. Önce mevcut siparişi kontrol et
echo "📋 1. <PERSON><PERSON><PERSON><PERSON> du<PERSON> (BEFORE):"
mysql -u root -p hollyposyeni -e "
SELECT 
    o.id,
    o.status,
    o.payment_status,
    o.table_id,
    st.status as table_status
FROM orders o
LEFT JOIN store_tables st ON o.table_id = st.id
WHERE o.id = 49185;
"

echo -e "\n================================\n"

# 2. Payment webhook'u çağır
echo "💳 2. Payment Webhook çağırılıyor..."
curl -X POST "http://*************:3000/api/v1/mtsm-webhook/orders/payment" \
  -H "Content-Type: application/json" \
  -d '{
    "id": "49185",
    "payments": [
      {
        "Type": "CashPayment",
        "Amount": 200,
        "Details": null
      }
    ],
    "receipt": {
      "No": "4",
      "ZNo": "20",
      "EruNo": "1",
      "Date": "2025-09-30T19:02:19.452"
    },
    "csn": "T60008697517"
  }' \
  -w "\nHTTP Status: %{http_code}\n" \
  -s | jq '.'

echo -e "\n================================\n"

# 3. Sonuçları kontrol et
echo "🔍 3. Sipariş durumu (AFTER):"
mysql -u root -p hollyposyeni -e "
SELECT 
    o.id,
    o.status,
    o.payment_status,
    o.table_id,
    st.status as table_status,
    st.order_ids
FROM orders o
LEFT JOIN store_tables st ON o.table_id = st.id
WHERE o.id = 49185;
"

echo -e "\n📊 Payment Transactions:"
mysql -u root -p hollyposyeni -e "
SELECT 
    pt.order_id,
    pt.payment_type_id,
    pty.title as payment_type,
    pty.mtsm_code,
    pt.amount,
    pt.created_by,
    pt.created_at
FROM payment_transactions pt
LEFT JOIN payment_types pty ON pt.payment_type_id = pty.id
WHERE pt.order_id = 49185
ORDER BY pt.created_at DESC
LIMIT 3;
"

echo -e "\n================================"
echo "✅ Test Tamamlandı!"
echo ""
echo "Beklenen Sonuçlar:"
echo "- ✅ Sipariş durumu: completed"
echo "- ✅ Ödeme durumu: paid"
echo "- ✅ Masa durumu: empty"
echo "- ✅ Socket bildirimi: Frontend'e gönderildi"
echo ""
echo "Socket test için:"
echo "1. test-socket-listener.html dosyasını tarayıcıda aç"
echo "2. Tenant ID: 5 ile bağlan"
echo "3. Bu script'i tekrar çalıştır"
echo "4. Socket'te order_update mesajını gör"
