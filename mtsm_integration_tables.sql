-- mTSM Entegrasyon Tabloları

-- Tenant bazlı mTSM ayarları
CREATE TABLE `mtsm_settings` (
  `id` int NOT NULL AUTO_INCREMENT,
  `tenant_id` int NOT NULL,
  `server_url` varchar(255) NOT NULL DEFAULT 'https://tsmtest.inpos.com.tr',
  `email` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL, -- Encrypted
  `access_token` text DEFAULT NULL,
  `refresh_token` text DEFAULT NULL,
  `token_expires_at` timestamp NULL DEFAULT NULL,
  `integration_type` enum('API','WEBHOOK') NOT NULL DEFAULT 'API',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `webhook_order_list_url` varchar(500) DEFAULT NULL,
  `webhook_order_detail_url` varchar(500) DEFAULT NULL,
  `webhook_order_update_url` varchar(500) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `tenant_id_unique` (`tenant_id`),
  KEY `tenant_id` (`tenant_id`),
  CONSTRAINT `mtsm_settings_ibfk_1` FOREIGN KEY (`tenant_id`) REFERENCES `tenants` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- mTSM Sipariş Eşleştirmeleri
CREATE TABLE `mtsm_order_mappings` (
  `id` int NOT NULL AUTO_INCREMENT,
  `tenant_id` int NOT NULL,
  `local_order_id` int NOT NULL, -- Bizim sistemdeki order ID
  `mtsm_order_id` varchar(100) NOT NULL, -- mTSM'deki order ID
  `mtsm_match_id` varchar(100) DEFAULT NULL, -- mTSM match ID
  `mtsm_csn` varchar(100) DEFAULT NULL, -- mTSM cihaz CSN
  `status` enum('PENDING','LOCKED','CLOSED','CANCELLED') NOT NULL DEFAULT 'PENDING',
  `sync_status` enum('SYNCED','PENDING','FAILED') NOT NULL DEFAULT 'PENDING',
  `last_sync_at` timestamp NULL DEFAULT NULL,
  `error_message` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `local_order_unique` (`tenant_id`, `local_order_id`),
  UNIQUE KEY `mtsm_order_unique` (`tenant_id`, `mtsm_order_id`),
  KEY `tenant_id` (`tenant_id`),
  KEY `local_order_id` (`local_order_id`),
  CONSTRAINT `mtsm_order_mappings_ibfk_1` FOREIGN KEY (`tenant_id`) REFERENCES `tenants` (`id`) ON DELETE CASCADE,
  CONSTRAINT `mtsm_order_mappings_ibfk_2` FOREIGN KEY (`local_order_id`) REFERENCES `orders` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- mTSM Eşleştirme Bilgileri (Şube/Cihaz)
CREATE TABLE `mtsm_matches` (
  `id` int NOT NULL AUTO_INCREMENT,
  `tenant_id` int NOT NULL,
  `match_id` varchar(100) NOT NULL,
  `match_name` varchar(255) NOT NULL,
  `branch_name` varchar(255) DEFAULT NULL,
  `device_count` int DEFAULT 0,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `tenant_match_unique` (`tenant_id`, `match_id`),
  KEY `tenant_id` (`tenant_id`),
  CONSTRAINT `mtsm_matches_ibfk_1` FOREIGN KEY (`tenant_id`) REFERENCES `tenants` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- mTSM Sync Logları
CREATE TABLE `mtsm_sync_logs` (
  `id` int NOT NULL AUTO_INCREMENT,
  `tenant_id` int NOT NULL,
  `operation_type` enum('LOGIN','ORDER_CREATE','ORDER_UPDATE','ORDER_LIST','MATCH_LIST') NOT NULL,
  `request_data` json DEFAULT NULL,
  `response_data` json DEFAULT NULL,
  `status` enum('SUCCESS','FAILED') NOT NULL,
  `error_message` text DEFAULT NULL,
  `execution_time_ms` int DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `tenant_id` (`tenant_id`),
  KEY `operation_type` (`operation_type`),
  KEY `status` (`status`),
  KEY `created_at` (`created_at`),
  CONSTRAINT `mtsm_sync_logs_ibfk_1` FOREIGN KEY (`tenant_id`) REFERENCES `tenants` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
