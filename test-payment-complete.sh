#!/bin/bash

# mTSM Ödeme Webhook Test Script
BASE_URL="https://backendpos.hollystone.com.tr/api/v1/mtsm-webhook"

echo "🧪 mTSM Ödeme Webhook Test Başlıyor..."
echo "================================"

# Test verilerini oluştur
echo "📋 1. Test verilerini oluşturuyor..."
mysql -u root -p your_database < test-payment-webhook.sql

echo -e "\n================================\n"

# Ödeme webhook'unu test et
echo "💳 2. Ödeme Webhook Test..."
curl -X POST "${BASE_URL}/orders/payment" \
  -H "Content-Type: application/json" \
  -d '{
    "id": "48791",
    "payments": [
      {
        "Type": "CashPayment",
        "Amount": 200,
        "Details": null
      }
    ],
    "receipt": {
      "No": "4",
      "ZNo": "20",
      "EruNo": "1",
      "Date": "2025-09-30T19:02:19.452"
    },
    "csn": "T60008697517"
  }' \
  -w "\nHTTP Status: %{http_code}\n" \
  -s | jq '.'

echo -e "\n================================\n"

# Sonuçları kontrol et
echo "🔍 3. Sonuçları kontrol ediyor..."

echo "📊 Sipariş Durumu:"
mysql -u root -p your_database -e "
SELECT 
    'AFTER PAYMENT' as stage,
    o.id as order_id,
    o.status as order_status,
    o.payment_status,
    o.table_id,
    st.table_title,
    st.status as table_status,
    st.order_ids as table_order_ids
FROM orders o
LEFT JOIN store_tables st ON o.table_id = st.id
WHERE o.id = 48791;
"

echo -e "\n📊 Ödeme Kayıtları:"
mysql -u root -p your_database -e "
SELECT 
    'AFTER PAYMENT' as stage,
    pt.order_id,
    pt.payment_type,
    pt.amount,
    pt.created_at
FROM payment_transactions pt
WHERE pt.order_id = 48791;
"

echo -e "\n================================"
echo "✅ mTSM Ödeme Webhook Test Tamamlandı!"
echo ""
echo "Beklenen Sonuçlar:"
echo "- Sipariş durumu: completed"
echo "- Ödeme durumu: paid"
echo "- Masa durumu: empty"
echo "- Masa order_ids: NULL"
echo "- Ödeme kaydı: CashPayment, 200 TL"
