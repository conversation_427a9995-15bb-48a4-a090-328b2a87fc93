// Frontend'de Domain Detection (React/Vue/Vanilla JS)

// 1. Domain Detection Utility
const DomainUtils = {
  // Mevcut domain'i al
  getCurrentDomain() {
    return window.location.hostname;
  },

  // Ana domain'leri k<PERSON> et
  isMainDomain(domain) {
    const mainDomains = [
      'localhost',
      '127.0.0.1',
      'backendpos.hollystone.com.tr',
      'yourmaindomain.com' // Ana domain'inizi buraya ekleyin
    ];
    return mainDomains.includes(domain);
  },

  // URL'den QR code'u al
  getQRCodeFromURL() {
    const path = window.location.pathname;
    const match = path.match(/\/qrmenu\/([^\/]+)/);
    return match ? match[1] : null;
  },

  // Tenant ID'yi belirle
  async resolveTenantId() {
    const domain = this.getCurrentDomain();
    
    // Ana domain ise QR code'dan tenant ID al
    if (this.isMainDomain(domain)) {
      const qrcode = this.getQRCodeFromURL();
      if (qrcode) {
        return {
          tenantId: await this.getTenantIdFromQRCode(qrcode),
          source: 'qrcode',
          qrcode: qrcode
        };
      }
      return null;
    }

    // Özel domain ise backend'den tenant ID al
    try {
      const response = await fetch('/api/v1/qrmenu/domain/check', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ domain })
      });

      if (response.ok) {
        const data = await response.json();
        return {
          tenantId: data.tenantId,
          source: 'domain',
          domain: domain,
          customDomain: true
        };
      }
    } catch (error) {
      console.error('Domain kontrolü hatası:', error);
    }

    return null;
  },

  // QR code'dan tenant ID al (mevcut API'nizi kullanın)
  async getTenantIdFromQRCode(qrcode) {
    try {
      const response = await fetch(`/api/v1/qrmenu/${qrcode}`);
      if (response.ok) {
        const data = await response.json();
        return data.storeSettings?.tenant_id;
      }
    } catch (error) {
      console.error('QR code kontrolü hatası:', error);
    }
    return null;
  }
};

// 2. React Hook Örneği
function useTenantDetection() {
  const [tenantInfo, setTenantInfo] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    async function detectTenant() {
      try {
        setLoading(true);
        const info = await DomainUtils.resolveTenantId();
        
        if (info) {
          setTenantInfo(info);
          console.log('🏢 Tenant belirlendi:', info);
        } else {
          setError('Tenant bulunamadı');
        }
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    }

    detectTenant();
  }, []);

  return { tenantInfo, loading, error };
}

// 3. React Component Örneği
function QRMenuApp() {
  const { tenantInfo, loading, error } = useTenantDetection();

  if (loading) {
    return <div>Menü yükleniyor...</div>;
  }

  if (error) {
    return <div>Hata: {error}</div>;
  }

  if (!tenantInfo) {
    return <div>Menü bulunamadı</div>;
  }

  return (
    <div>
      <h1>
        {tenantInfo.customDomain 
          ? `Hoş geldiniz! (${tenantInfo.domain})` 
          : 'QR Menü'
        }
      </h1>
      
      {/* Menü bileşenleriniz */}
      <MenuComponent tenantId={tenantInfo.tenantId} />
    </div>
  );
}

// 4. Vue.js Composition API Örneği
function useTenantDetection() {
  const tenantInfo = ref(null);
  const loading = ref(true);
  const error = ref(null);

  onMounted(async () => {
    try {
      loading.value = true;
      const info = await DomainUtils.resolveTenantId();
      
      if (info) {
        tenantInfo.value = info;
        console.log('🏢 Tenant belirlendi:', info);
      } else {
        error.value = 'Tenant bulunamadı';
      }
    } catch (err) {
      error.value = err.message;
    } finally {
      loading.value = false;
    }
  });

  return { tenantInfo, loading, error };
}

// 5. Vanilla JavaScript Örneği
document.addEventListener('DOMContentLoaded', async () => {
  try {
    const tenantInfo = await DomainUtils.resolveTenantId();
    
    if (tenantInfo) {
      console.log('🏢 Tenant belirlendi:', tenantInfo);
      
      // Menüyü yükle
      loadMenu(tenantInfo.tenantId);
      
      // Başlığı güncelle
      if (tenantInfo.customDomain) {
        document.title = `Menü - ${tenantInfo.domain}`;
      }
    } else {
      document.body.innerHTML = '<h1>Menü bulunamadı</h1>';
    }
  } catch (error) {
    console.error('Tenant detection hatası:', error);
    document.body.innerHTML = '<h1>Bir hata oluştu</h1>';
  }
});

async function loadMenu(tenantId) {
  // Menü verilerini yükle
  // Mevcut QR menü API'nizi kullanın
}

// 6. API Çağrıları için Helper
const APIHelper = {
  // Tenant bilgisine göre API URL'i oluştur
  getAPIUrl(tenantInfo, endpoint) {
    if (tenantInfo.source === 'domain') {
      // Özel domain için direkt endpoint kullan
      return `/api/v1/qrmenu/domain-menu${endpoint}`;
    } else {
      // QR code için mevcut yapıyı kullan
      return `/api/v1/qrmenu/${tenantInfo.qrcode}${endpoint}`;
    }
  },

  // Menü verilerini getir
  async getMenuData(tenantInfo) {
    const url = tenantInfo.source === 'domain' 
      ? `/api/v1/qrmenu/custom-domain-menu?tenantId=${tenantInfo.tenantId}`
      : `/api/v1/qrmenu/${tenantInfo.qrcode}`;
    
    const response = await fetch(url);
    return response.json();
  },

  // Sipariş oluştur
  async placeOrder(tenantInfo, orderData) {
    const url = tenantInfo.source === 'domain'
      ? `/api/v1/qrmenu/custom-domain-order?tenantId=${tenantInfo.tenantId}`
      : `/api/v1/qrmenu/${tenantInfo.qrcode}/place-order`;
    
    const response = await fetch(url, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(orderData)
    });
    
    return response.json();
  }
};

export { DomainUtils, useTenantDetection, APIHelper };
